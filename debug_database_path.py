#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مسار قاعدة البيانات المستخدمة فعلياً
"""

import os
import sys
import sqlite3

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from flask import Flask
    from flask_sqlalchemy import SQLAlchemy
    from dotenv import load_dotenv
    
    # تحميل متغيرات البيئة
    load_dotenv()
    
    # إنشاء تطبيق Flask
    app = Flask(__name__)
    
    # إعدادات التطبيق
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///legal_system.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    print(f"🔍 DATABASE_URL من .env: {os.environ.get('DATABASE_URL', 'غير محدد')}")
    print(f"🔍 SQLALCHEMY_DATABASE_URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
    
    # تحليل مسار قاعدة البيانات
    db_uri = app.config['SQLALCHEMY_DATABASE_URI']
    if db_uri.startswith('sqlite:///'):
        db_path = db_uri[10:]  # إزالة 'sqlite:///'
        if not os.path.isabs(db_path):
            db_path = os.path.join(os.getcwd(), db_path)
        print(f"🔍 مسار قاعدة البيانات المحسوب: {db_path}")
        print(f"🔍 هل الملف موجود؟ {os.path.exists(db_path)}")
        
        if os.path.exists(db_path):
            print(f"📊 حجم الملف: {os.path.getsize(db_path)} بايت")
            print(f"📅 تاريخ التعديل: {os.path.getmtime(db_path)}")
    
    # تهيئة قاعدة البيانات
    db = SQLAlchemy(app)
    
    with app.app_context():
        try:
            # الحصول على مسار قاعدة البيانات الفعلي من SQLAlchemy
            engine = db.engine
            print(f"🔍 محرك قاعدة البيانات: {engine}")
            print(f"🔍 URL المحرك: {engine.url}")
            
            # اختبار الاتصال المباشر
            with engine.connect() as connection:
                # الحصول على مسار قاعدة البيانات الفعلي
                result = connection.execute(db.text("PRAGMA database_list"))
                databases = result.fetchall()
                
                print("📋 قواعد البيانات المتصلة:")
                for db_info in databases:
                    print(f"  - {db_info[1]}: {db_info[2]}")
                
                # فحص جدول الجزاءات
                result = connection.execute(db.text("SELECT name FROM sqlite_master WHERE type='table' AND name='penalties'"))
                table_exists = result.fetchone()
                
                if table_exists:
                    print("✅ جدول الجزاءات موجود")
                    
                    # فحص أعمدة الجدول
                    result = connection.execute(db.text("PRAGMA table_info(penalties)"))
                    columns = result.fetchall()
                    column_names = [row[1] for row in columns]
                    
                    print(f"📋 أعمدة جدول الجزاءات: {', '.join(column_names)}")
                    
                    # التحقق من الأعمدة المطلوبة
                    required_columns = ['category', 'title', 'description', 'penalty_type']
                    missing_columns = [col for col in required_columns if col not in column_names]
                    
                    if missing_columns:
                        print(f"❌ أعمدة مفقودة: {', '.join(missing_columns)}")
                    else:
                        print("✅ جميع الأعمدة المطلوبة موجودة")
                    
                    # عدد السجلات
                    result = connection.execute(db.text("SELECT COUNT(*) FROM penalties"))
                    row = result.fetchone()
                    count = row[0] if row else 0
                    print(f"📊 عدد السجلات: {count}")
                    
                else:
                    print("❌ جدول الجزاءات غير موجود")
            
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            import traceback
            traceback.print_exc()
    
    # فحص ملفات قاعدة البيانات الموجودة
    print("\n🔍 فحص ملفات قاعدة البيانات الموجودة:")
    possible_paths = [
        'legal_system.db',
        'instance/legal_system.db',
        os.path.join('instance', 'legal_system.db')
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ {path} موجود ({os.path.getsize(path)} بايت)")
            
            # فحص سريع للمحتوى
            try:
                conn = sqlite3.connect(path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='penalties'")
                if cursor.fetchone():
                    cursor.execute("PRAGMA table_info(penalties)")
                    columns = [row[1] for row in cursor.fetchall()]
                    print(f"  📋 أعمدة: {', '.join(columns[:5])}{'...' if len(columns) > 5 else ''}")
                conn.close()
            except Exception as e:
                print(f"  ❌ خطأ في فحص {path}: {e}")
        else:
            print(f"❌ {path} غير موجود")
            
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
