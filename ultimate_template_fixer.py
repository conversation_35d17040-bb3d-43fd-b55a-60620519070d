#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل وفوري لجميع أخطاء القوالب
"""

import os
import re
import glob
import shutil
from datetime import datetime

def create_backup():
    """إنشاء نسخة احتياطية من القوالب"""
    
    print("💾 إنشاء نسخة احتياطية...")
    
    backup_dir = f"templates_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        if os.path.exists('templates'):
            shutil.copytree('templates', backup_dir)
            print(f"✅ تم إنشاء نسخة احتياطية في: {backup_dir}")
            return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return False

def fix_all_template_errors():
    """إصلاح جميع أخطاء القوالب"""
    
    print("🔥 بدء الإصلاح الشامل لجميع أخطاء القوالب...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    fixed_files = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # إصلاحات شاملة وقوية
            
            # 1. إصلاح الأقواس المفقودة والإضافية
            content = re.sub(r'\|default\(([^)]*)\s+\}\}', r'|default(\1) }}', content)
            content = re.sub(r'\.format\(([^)]*)\s+\}\}', r'.format(\1) }}', content)
            content = re.sub(r'\.strftime\(([^)]*)\s+\}\}', r'.strftime(\1) }}', content)
            content = re.sub(r'\|round\(([^)]*)\s+\}\}', r'|round(\1) }}', content)
            content = re.sub(r'\|truncate\(([^)]*)\s+\}\}', r'|truncate(\1) }}', content)
            
            # 2. إصلاح علامات HTML المكسورة
            content = re.sub(r'</(\w+)\s*>', r'</\1>', content)
            content = re.sub(r'<(\w+)([^>]*?)>\s*>', r'<\1\2>', content)
            
            # 3. إصلاح التعبيرات الشرطية المقسمة
            content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s*\n\s*([^}]+?)\s*\}\}', 
                           r'{{ \1 if \2 else \3 }}', content, flags=re.MULTILINE)
            content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', 
                           r'{{ \1 if \2 else \3 }}', content)
            
            # 4. إصلاح الأسطر المقسمة
            content = re.sub(r'\{\{\s*([^}]+?)\n\s*([^}]+?)\s*\}\}', r'{{ \1 \2 }}', content, flags=re.MULTILINE)
            content = re.sub(r'\{\%\s*([^%]+?)\n\s*([^%]+?)\s*\%\}', r'{% \1 \2 %}', content, flags=re.MULTILINE)
            
            # 5. إصلاح الأقواس المضاعفة
            content = re.sub(r'\{\{\s*\{\{', r'{{', content)
            content = re.sub(r'\}\}\s*\}\}', r'}}', content)
            content = re.sub(r'\{\%\s*\{\%', r'{%', content)
            content = re.sub(r'\%\}\s*\%\}', r'%}', content)
            
            # 6. إصلاح المسافات الإضافية
            content = re.sub(r'\{\{\s{2,}', r'{{ ', content)
            content = re.sub(r'\s{2,}\}\}', r' }}', content)
            content = re.sub(r'\{\%\s{2,}', r'{% ', content)
            content = re.sub(r'\s{2,}\%\}', r' %}', content)
            
            # 7. إصلاح علامات Django في Jinja2
            content = re.sub(r'\{\%\s*url\s+([^%]+?)\s*\%\}', r'{{ url_for(\1) }}', content)
            content = re.sub(r'\{\%\s*static\s+([^%]+?)\s*\%\}', r'{{ url_for("static", filename=\1) }}', content)
            
            # 8. إصلاح الفلاتر الخاطئة
            content = re.sub(r'\|date:"([^"]+)"', r'|strftime("\1")', content)
            content = re.sub(r'\|floatformat:(\d+)', r'|round(\1)', content)
            content = re.sub(r'\|truncatechars:(\d+)', r'|truncate(\1)', content)
            content = re.sub(r'\|default:"([^"]+)"', r'|default("\1")', content)
            
            # 9. إصلاح format مضاعف
            content = re.sub(r'format\(format\(([^)]+)\)\)', r'format(\1)', content)
            content = re.sub(r'\.format\(\.format\(([^)]+)\)\)', r'.format(\1)', content)
            
            # 10. إصلاحات نصية مباشرة للمشاكل الشائعة
            direct_fixes = [
                # الأقواس المفقودة
                ('|default(0 }}', '|default(0) }}'),
                ('|default(1 }}', '|default(1) }}'),
                ('|default("" }}', '|default("") }}'),
                ('|default(\'\' }}', '|default(\'\') }}'),
                ('.format(total_revenue }}', '.format(total_revenue) }}'),
                ('.format(pending_revenue }}', '.format(pending_revenue) }}'),
                ('.format(total_cases }}', '.format(total_cases) }}'),
                ('.format(active_cases }}', '.format(active_cases) }}'),
                ('.format(completed_cases }}', '.format(completed_cases) }}'),
                ('.format(total_clients }}', '.format(total_clients) }}'),
                ('.format(total_employees }}', '.format(total_employees) }}'),
                ('.format(paid_invoices }}', '.format(paid_invoices) }}'),
                ('.format(pending_invoices }}', '.format(pending_invoices) }}'),
                ('.format(overdue_invoices }}', '.format(overdue_invoices) }}'),
                
                # علامات HTML مكسورة
                ('</small>', '</small>'),
                ('</div>', '</div>'),
                ('</span>', '</span>'),
                ('</p>', '</p>'),
                ('</h1>', '</h1>'),
                ('</h2>', '</h2>'),
                ('</h3>', '</h3>'),
                ('</h4>', '</h4>'),
                ('</h5>', '</h5>'),
                ('</h6>', '</h6>'),
                ('</a>', '</a>'),
                ('</button>', '</button>'),
                ('</form>', '</form>'),
                ('</table>', '</table>'),
                ('</tr>', '</tr>'),
                ('</td>', '</td>'),
                ('</th>', '</th>'),
                
                # أقواس إضافية
                ('))', ')'),
                ('((', '('),
                ('}})', '}}'),
                ('{{{', '{{'),
                ('%%}', '%}'),
                ('{%%', '{%'),
                
                # مسافات إضافية
                ('  }}', ' }}'),
                ('{{  ', '{{ '),
                ('  %}', ' %}'),
                ('{%  ', '{% '),
                
                # علامات مختلطة
                ('{% url ', '{{ url_for('),
                (' %}"', ') }}"'),
                ('{% static ', '{{ url_for("static", filename='),
                ('{% widthratio', '{{ ('),
                (' %} %', ') }} %'),
                
                # تعبيرات شرطية مقسمة
                ('if user.email else\n                user.username', 'if user.email else user.username'),
                ('if user.full_name else\n                user.username', 'if user.full_name else user.username'),
                ('if employee.user.email else\n                employee.user.username', 'if employee.user.email else employee.user.username'),
                ('if case.created_at else\n                "-"', 'if case.created_at else "-"'),
                ('if contract.end_date else\n                "-"', 'if contract.end_date else "-"'),
                
                # إصلاحات خاصة
                ('> >', '>'),
                ('< <', '<'),
                ('> •', ' • '),
                ('• <', ' • <'),
                ('>&nbsp;', '> '),
                ('&nbsp;<', ' <'),
                
                # JavaScript/CSS مكسور
                ('});', '});'),
                ('};', '};'),
                ('{;', '{'),
                (';}', '}'),
                (';;', ';'),
                
                # إصلاحات إضافية
                ('case.created_at else "-")', 'case.created_at else "-"'),
                ('employee.hire_date else "-")', 'employee.hire_date else "-"'),
                ('contract.start_date else "-")', 'contract.start_date else "-"'),
            ]
            
            for old, new in direct_fixes:
                content = content.replace(old, new)
            
            # حفظ الملف إذا تم تغييره
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  🔧 {file_path}: تم إصلاحه")
                fixed_files += 1
                total_fixes += 1
            
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {fixed_files} ملف بإجمالي {total_fixes} إصلاح")
    return fixed_files, total_fixes

def validate_all_templates():
    """التحقق من جميع القوالب"""
    
    print("\n🔍 التحقق من جميع القوالب...")
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        template_files = glob.glob("templates/**/*.html", recursive=True)
        
        valid_count = 0
        error_count = 0
        errors = []
        
        for file_path in template_files:
            try:
                relative_path = os.path.relpath(file_path, 'templates').replace('\\', '/')
                template = env.get_template(relative_path)
                valid_count += 1
                
            except TemplateSyntaxError as e:
                error_count += 1
                error_msg = str(e)[:100] + "..." if len(str(e)) > 100 else str(e)
                errors.append(f"{relative_path}: {error_msg}")
            except Exception as e:
                error_count += 1
                error_msg = str(e)[:100] + "..." if len(str(e)) > 100 else str(e)
                errors.append(f"{relative_path}: {error_msg}")
        
        print(f"📊 نتائج التحقق:")
        print(f"  ✅ ملفات صحيحة: {valid_count}")
        print(f"  ❌ ملفات بها أخطاء: {error_count}")
        
        if errors and len(errors) <= 10:
            print(f"\n❌ الأخطاء المتبقية:")
            for error in errors:
                print(f"  - {error}")
        elif errors:
            print(f"\n❌ أول 5 أخطاء من {len(errors)} خطأ:")
            for error in errors[:5]:
                print(f"  - {error}")
        
        return error_count == 0, errors
        
    except ImportError:
        print("❌ لا يمكن التحقق من بناء الجملة - Jinja2 غير متوفر")
        return False, ["لا يمكن التحقق"]

def create_working_templates():
    """إنشاء قوالب أساسية تعمل بشكل مضمون"""
    
    print("\n🛠️ إنشاء قوالب أساسية احتياطية...")
    
    # قالب أساسي محسن
    base_template = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الشؤون القانونية{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .navbar-brand { font-weight: bold; }
        .card { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats-card { transition: transform 0.2s; }
        .stats-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-balance-scale me-2"></i>نظام الشؤون القانونية
            </a>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>'''
    
    # لوحة تحكم محسنة
    dashboard_template = '''{% extends "base_working.html" %}
{% block title %}لوحة التحكم - نظام الشؤون القانونية{% endblock %}
{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</h1>
    </div>
</div>

<div class="row g-4">
    <div class="col-md-3">
        <div class="card stats-card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_cases|default(0) }}</h4>
                        <p class="card-text">إجمالي القضايا</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-gavel fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_clients|default(0) }}</h4>
                        <p class="card-text">إجمالي العملاء</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_employees|default(0) }}</h4>
                        <p class="card-text">إجمالي الموظفين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_invoices|default(0) }}</h4>
                        <p class="card-text">إجمالي الفواتير</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>الإحصائيات المالية</h5>
            </div>
            <div class="card-body">
                <p>إجمالي الإيرادات: <strong>{{ total_revenue|default(0) }}</strong></p>
                <p>الإيرادات المعلقة: <strong>{{ pending_revenue|default(0) }}</strong></p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tasks me-2"></i>حالة القضايا</h5>
            </div>
            <div class="card-body">
                <p>القضايا النشطة: <strong>{{ active_cases|default(0) }}</strong></p>
                <p>القضايا المكتملة: <strong>{{ completed_cases|default(0) }}</strong></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
    
    # صفحة تسجيل دخول محسنة
    login_template = '''{% extends "base_working.html" %}
{% block title %}تسجيل الدخول{% endblock %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header text-center">
                <h4><i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">دخول</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
    
    # حفظ القوالب الاحتياطية
    working_templates = {
        'templates/base_working.html': base_template,
        'templates/dashboard_working.html': dashboard_template,
        'templates/login_working.html': login_template,
    }
    
    for file_path, content in working_templates.items():
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ تم إنشاء {file_path}")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء {file_path}: {e}")

def main():
    """الدالة الرئيسية للإصلاح الشامل"""
    
    print("🔥 بدء الإصلاح الشامل والفوري لجميع أخطاء القوالب...")
    
    # إنشاء نسخة احتياطية
    backup_created = create_backup()
    
    # إصلاح جميع الأخطاء
    fixed_files, total_fixes = fix_all_template_errors()
    
    # التحقق من النتائج
    is_valid, errors = validate_all_templates()
    
    # إنشاء قوالب احتياطية
    create_working_templates()
    
    print("\n" + "="*60)
    print("🔥 تقرير الإصلاح الشامل والفوري")
    print("="*60)
    print(f"💾 نسخة احتياطية: {'✅ تم إنشاؤها' if backup_created else '❌ فشل'}")
    print(f"🔧 ملفات مُصلحة: {fixed_files}")
    print(f"📊 إجمالي الإصلاحات: {total_fixes}")
    print(f"📁 ملفات صحيحة: {len(glob.glob('templates/**/*.html', recursive=True)) - len(errors) if errors else 'جميع الملفات'}")
    print(f"❌ أخطاء متبقية: {len(errors) if errors else 0}")
    
    if is_valid:
        print("\n🎉 تم إصلاح جميع أخطاء القوالب بنجاح!")
        print("✅ جميع القوالب تعمل بشكل صحيح")
    else:
        print(f"\n⚠️ لا تزال هناك {len(errors)} أخطاء")
        print("💡 تم إنشاء قوالب احتياطية تعمل بشكل مضمون")
    
    print("="*60)
    
    return is_valid

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
