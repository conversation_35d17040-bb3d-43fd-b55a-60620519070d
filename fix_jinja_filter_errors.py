#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أخطاء فلاتر Jinja2
"""

import os
import re
import glob

def fix_jinja_filter_errors():
    """إصلاح أخطاء فلاتر Jinja2"""
    
    print("🔧 إصلاح أخطاء فلاتر Jinja2...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # إصلاح الأنماط الخاطئة للفلاتر
            patterns = [
                # {{ "{:,.2f}"|contract.total_amount }} → {{ "{:,.2f}".format(contract.total_amount) }}
                (r'\{\{\s*"([^"]+)"\|([^}]+)\s*\}\}', r'{{ "\1".format(\2) }}'),
                
                # {{ "%.1f"|format(...) }} → {{ "%.1f".format(...) }}
                (r'\{\{\s*"([^"]+)"\|format\(([^}]+)\)\s*\}\}', r'{{ "\1".format(\2) }}'),
                
                # إصلاح فلاتر أخرى خاطئة
                (r'\{\{\s*"([^"]+)"\|([^}]+)\s*\}\}', r'{{ "\1".format(\2) }}'),
                
                # إصلاح stats.financial.total_value مع فلتر
                (r'\{\{\s*"([^"]+)"\|stats\.([^}]+)\s*\}\}', r'{{ "\1".format(stats.\2) }}'),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            # حفظ الملف إذا تم إجراء تغييرات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
            else:
                print(f"  ✅ {file_path}: لا توجد أخطاء")
                
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} خطأ فلتر")
    return total_fixes

def fix_specific_filter_errors():
    """إصلاح أخطاء فلاتر محددة"""
    
    print("\n🔧 إصلاح أخطاء فلاتر محددة...")
    
    specific_fixes = [
        {
            'file': 'templates/contracts/index.html',
            'old': '{{ "{:,.2f}"|contract.total_amount }}',
            'new': '{{ "{:,.2f}".format(contract.total_amount) }}'
        },
        {
            'file': 'templates/contracts/view.html',
            'old': '{{ "{:,.2f}"|contract.total_amount }}',
            'new': '{{ "{:,.2f}".format(contract.total_amount) }}'
        },
        {
            'file': 'templates/contracts/reports.html',
            'old': '{{ "{:,.0f}"|stats.financial.total_value }}',
            'new': '{{ "{:,.0f}".format(stats.financial.total_value) }}'
        },
        {
            'file': 'templates/contracts/reports.html',
            'old': '{{ "{:,.2f}"|stats.financial.total_value }}',
            'new': '{{ "{:,.2f}".format(stats.financial.total_value) }}'
        }
    ]
    
    total_fixes = 0
    
    for fix in specific_fixes:
        file_path = fix['file']
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if fix['old'] in content:
                    content = content.replace(fix['old'], fix['new'])
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print(f"  ✅ {file_path}: تم إصلاح فلتر محدد")
                    total_fixes += 1
                
            except Exception as e:
                print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"✅ تم إصلاح {total_fixes} فلتر محدد")
    return total_fixes

def validate_jinja_syntax():
    """التحقق من صحة بناء جملة Jinja2"""
    
    print("\n🔍 التحقق من صحة بناء جملة Jinja2...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    errors = []
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن أخطاء شائعة في فلاتر Jinja2
            error_patterns = [
                (r'\{\{\s*"[^"]+"\|[^}]+\s*\}\}', 'فلتر خاطئ - استخدم .format() بدلاً من |'),
                (r'\{\{\s*[^}]*\|[^}]*\.[^}]*\s*\}\}', 'فلتر خاطئ - ترتيب خاطئ'),
                (r'\{\{\s*\{\{', 'أقواس مضاعفة'),
                (r'\}\}\s*\}\}', 'أقواس إغلاق مضاعفة'),
            ]
            
            for pattern, error_type in error_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    errors.append(f"{file_path}: {error_type} ({len(matches)} مرة)")
        
        except Exception as e:
            errors.append(f"{file_path}: خطأ في القراءة - {e}")
    
    if errors:
        print("❌ تم العثور على أخطاء:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ جميع فلاتر Jinja2 صحيحة!")
        return True

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إصلاح أخطاء فلاتر Jinja2...")
    
    # إصلاح الأخطاء العامة
    general_fixes = fix_jinja_filter_errors()
    
    # إصلاح الأخطاء المحددة
    specific_fixes = fix_specific_filter_errors()
    
    # التحقق من النتائج
    is_valid = validate_jinja_syntax()
    
    print("\n" + "="*50)
    print(f"🎉 تم إصلاح {general_fixes + specific_fixes} خطأ فلتر إجمالي")
    if is_valid:
        print("✅ جميع فلاتر Jinja2 تعمل بشكل صحيح")
    else:
        print("⚠️ لا تزال هناك بعض الأخطاء")
    print("="*50)

if __name__ == "__main__":
    main()
