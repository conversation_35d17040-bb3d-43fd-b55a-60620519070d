from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from decimal import Decimal
import uuid
import os
from werkzeug.utils import secure_filename
from permissions_manager import permission_required

bp = Blueprint('leaves', __name__, url_prefix='/leaves')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from flask import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def get_db():
    """الحصول على كائن قاعدة البيانات"""
    try:
        from flask import current_app
        return current_app.extensions['sqlalchemy'].db
    except (RuntimeError, KeyError):
        return None

def allowed_file(filename):
    """التحقق من أن نوع الملف مسموح"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """التأكد من وجود مجلد رفع الملفات"""
    upload_folder = os.path.join(current_app.root_path, 'static', 'uploads', 'leaves')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    return upload_folder

@bp.route('/')
@login_required
@permission_required('view_leaves')
def index():
    """عرض طلبات الاستئذان"""
    try:
        models = get_models()
        LeaveRequest = models.get('LeaveRequest')
        Employee = models.get('Employee')
        Department = models.get('Department')
        
        if not all([LeaveRequest, Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # فلترة البيانات
        search = request.args.get('search', '').strip()
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        leave_type = request.args.get('leave_type', '').strip()
        status = request.args.get('status', '').strip()
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # بناء الاستعلام
        query = LeaveRequest.query.join(Employee)
        
        if search:
            query = query.filter(Employee.full_name.contains(search))
        
        if employee_id:
            query = query.filter(LeaveRequest.employee_id == employee_id)
        
        if department_id:
            query = query.filter(Employee.department_id == department_id)
        
        if leave_type:
            query = query.filter(LeaveRequest.leave_type == leave_type)
        
        if status:
            query = query.filter(LeaveRequest.status == status)
        
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(LeaveRequest.start_date >= date_from_obj)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                query = query.filter(LeaveRequest.end_date <= date_to_obj)
            except ValueError:
                pass
        
        # ترتيب النتائج
        leave_requests = query.order_by(LeaveRequest.created_at.desc()).all()
        
        # جلب قوائم الفلترة
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
        departments = Department.query.order_by(Department.name).all()
        
        # أنواع الإجازات
        leave_types = [
            ('annual', 'إجازة سنوية'),
            ('sick', 'إجازة مرضية'),
            ('emergency', 'إجازة طارئة'),
            ('maternity', 'إجازة أمومة'),
            ('paternity', 'إجازة أبوة'),
            ('study', 'إجازة دراسية'),
            ('unpaid', 'إجازة بدون راتب'),
            ('other', 'أخرى')
        ]
        
        # إحصائيات سريعة
        stats = {
            'total_requests': len(leave_requests),
            'pending_requests': len([r for r in leave_requests if r.status == 'pending']),
            'approved_requests': len([r for r in leave_requests if r.status == 'approved']),
            'rejected_requests': len([r for r in leave_requests if r.status == 'rejected']),
            'total_days': sum([r.days_count for r in leave_requests if r.status == 'approved'])
        }
        
        return render_template('leaves/index.html',
                             leave_requests=leave_requests,
                             employees=employees,
                             departments=departments,
                             leave_types=leave_types,
                             search=search,
                             employee_id=employee_id,
                             department_id=department_id,
                             leave_type=leave_type,
                             status=status,
                             date_from=date_from,
                             date_to=date_to,
                             stats=stats)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('view_leaves')
def add():
    """إضافة طلب استئذان جديد"""
    if request.method == 'GET':
        try:
            models = get_models()
            Employee = models.get('Employee')
            
            if not Employee:
                flash('خطأ في تحميل النماذج', 'error')
                return redirect(url_for('leaves.index'))

            employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
            
            # أنواع الإجازات
            leave_types = [
                ('annual', 'إجازة سنوية'),
                ('sick', 'إجازة مرضية'),
                ('emergency', 'إجازة طارئة'),
                ('maternity', 'إجازة أمومة'),
                ('paternity', 'إجازة أبوة'),
                ('study', 'إجازة دراسية'),
                ('unpaid', 'إجازة بدون راتب'),
                ('other', 'أخرى')
            ]
            
            return render_template('leaves/add.html',
                                 employees=employees,
                                 leave_types=leave_types,
                                 today=date.today())
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('leaves.index'))
    
    # POST request
    try:
        models = get_models()
        LeaveRequest = models.get('LeaveRequest')
        Employee = models.get('Employee')
        db = get_db()
        
        if not all([LeaveRequest, Employee, db]):
            return jsonify({'success': False, 'message': 'خطأ في تحميل النماذج'})

        # جلب البيانات من النموذج
        employee_id = request.form.get('employee_id', type=int)
        leave_type = request.form.get('leave_type', '').strip()
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        reason = request.form.get('reason', '').strip()
        is_paid = request.form.get('is_paid') == 'on'

        # التحقق من صحة البيانات
        if not all([employee_id, leave_type, start_date_str, end_date_str, reason]):
            return jsonify({'success': False, 'message': 'جميع الحقول مطلوبة'})

        # التحقق من وجود الموظف
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'success': False, 'message': 'الموظف غير موجود'})

        # تحويل التواريخ
        try:
            start_date_obj = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': 'تنسيق التاريخ غير صحيح'})

        # التحقق من صحة التواريخ
        if start_date_obj > end_date_obj:
            return jsonify({'success': False, 'message': 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية'})

        if start_date_obj < date.today():
            return jsonify({'success': False, 'message': 'لا يمكن تقديم طلب إجازة لتاريخ سابق'})

        # حساب عدد الأيام
        days_count = (end_date_obj - start_date_obj).days + 1

        # معالجة رفع الملف
        attachment_path = None
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                # إنشاء مجلد الرفع
                upload_folder = ensure_upload_folder()
                
                # إنشاء اسم ملف فريد
                original_filename = secure_filename(file.filename)
                file_extension = original_filename.rsplit('.', 1)[1].lower()
                unique_filename = f"leave_{employee_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
                file_path = os.path.join(upload_folder, unique_filename)
                
                # حفظ الملف
                file.save(file_path)
                attachment_path = f"uploads/leaves/{unique_filename}"

        # إنشاء طلب الإجازة
        leave_request = LeaveRequest(
            employee_id=employee_id,
            leave_type=leave_type,
            start_date=start_date_obj,
            end_date=end_date_obj,
            days_count=days_count,
            reason=reason,
            is_paid=is_paid,
            attachment_path=attachment_path,
            status='pending'
        )

        db.session.add(leave_request)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم إضافة طلب الإجازة بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:leave_id>/approve', methods=['POST'])
@login_required
@permission_required('approve_leaves')
def approve(leave_id):
    """الموافقة على طلب إجازة"""
    try:
        models = get_models()
        LeaveRequest = models.get('LeaveRequest')
        db = get_db()
        
        if not all([LeaveRequest, db]):
            return jsonify({'success': False, 'message': 'خطأ في تحميل النماذج'})

        leave_request = LeaveRequest.query.get_or_404(leave_id)
        
        if leave_request.status != 'pending':
            return jsonify({'success': False, 'message': 'لا يمكن تعديل طلب تم البت فيه'})

        approval_notes = request.form.get('approval_notes', '').strip()

        leave_request.status = 'approved'
        leave_request.approved_by = current_user.id
        leave_request.approval_date = datetime.utcnow()
        leave_request.approval_notes = approval_notes

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم الموافقة على طلب الإجازة'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:leave_id>/reject', methods=['POST'])
@login_required
@permission_required('approve_leaves')
def reject(leave_id):
    """رفض طلب إجازة"""
    try:
        models = get_models()
        LeaveRequest = models.get('LeaveRequest')
        db = get_db()
        
        if not all([LeaveRequest, db]):
            return jsonify({'success': False, 'message': 'خطأ في تحميل النماذج'})

        leave_request = LeaveRequest.query.get_or_404(leave_id)
        
        if leave_request.status != 'pending':
            return jsonify({'success': False, 'message': 'لا يمكن تعديل طلب تم البت فيه'})

        approval_notes = request.form.get('approval_notes', '').strip()
        
        if not approval_notes:
            return jsonify({'success': False, 'message': 'سبب الرفض مطلوب'})

        leave_request.status = 'rejected'
        leave_request.approved_by = current_user.id
        leave_request.approval_date = datetime.utcnow()
        leave_request.approval_notes = approval_notes

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم رفض طلب الإجازة'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:leave_id>/delete', methods=['POST'])
@login_required
@permission_required('manage_leaves')
def delete(leave_id):
    """حذف طلب إجازة"""
    try:
        models = get_models()
        LeaveRequest = models.get('LeaveRequest')
        db = get_db()
        
        if not all([LeaveRequest, db]):
            return jsonify({'success': False, 'message': 'خطأ في تحميل النماذج'})

        leave_request = LeaveRequest.query.get_or_404(leave_id)
        
        # حذف الملف المرفق إن وجد
        if leave_request.attachment_path:
            try:
                file_path = os.path.join(current_app.root_path, 'static', leave_request.attachment_path)
                if os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass  # تجاهل أخطاء حذف الملف
        
        db.delete(leave_request)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'تم حذف طلب الإجازة بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/report')
@login_required
@permission_required('view_leaves')
def report():
    """تقرير الإجازات"""
    try:
        models = get_models()
        LeaveRequest = models.get('LeaveRequest')
        Employee = models.get('Employee')
        Department = models.get('Department')
        
        if not all([LeaveRequest, Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # فلترة البيانات
        employee_id = request.args.get('employee_id', type=int)
        department_id = request.args.get('department_id', type=int)
        leave_type = request.args.get('leave_type', '').strip()
        status = request.args.get('status', '').strip()
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # تواريخ افتراضية (السنة الحالية)
        if not date_from:
            date_from = date.today().replace(month=1, day=1).strftime('%Y-%m-%d')
        if not date_to:
            date_to = date.today().strftime('%Y-%m-%d')

        # بناء الاستعلام
        query = LeaveRequest.query.join(Employee)
        
        if employee_id:
            query = query.filter(LeaveRequest.employee_id == employee_id)
        
        if department_id:
            query = query.filter(Employee.department_id == department_id)
        
        if leave_type:
            query = query.filter(LeaveRequest.leave_type == leave_type)
        
        if status:
            query = query.filter(LeaveRequest.status == status)
        
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(LeaveRequest.start_date >= date_from_obj, LeaveRequest.end_date <= date_to_obj)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return redirect(url_for('leaves.index'))

        leave_requests = query.order_by(LeaveRequest.start_date.desc()).all()
        
        # جلب قوائم الفلترة
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
        departments = Department.query.order_by(Department.name).all()
        
        # أنواع الإجازات
        leave_types = [
            ('annual', 'إجازة سنوية'),
            ('sick', 'إجازة مرضية'),
            ('emergency', 'إجازة طارئة'),
            ('maternity', 'إجازة أمومة'),
            ('paternity', 'إجازة أبوة'),
            ('study', 'إجازة دراسية'),
            ('unpaid', 'إجازة بدون راتب'),
            ('other', 'أخرى')
        ]
        
        # حساب الإحصائيات
        total_requests = len(leave_requests)
        approved_requests = [r for r in leave_requests if r.status == 'approved']
        total_approved_days = sum([r.days_count for r in approved_requests])
        
        # إحصائيات حسب النوع
        type_stats = {}
        for leave_type_code, leave_type_name in leave_types:
            type_requests = [r for r in approved_requests if r.leave_type == leave_type_code]
            type_stats[leave_type_code] = {
                'name': leave_type_name,
                'count': len(type_requests),
                'days': sum([r.days_count for r in type_requests])
            }
        
        stats = {
            'total_requests': total_requests,
            'approved_requests': len(approved_requests),
            'pending_requests': len([r for r in leave_requests if r.status == 'pending']),
            'rejected_requests': len([r for r in leave_requests if r.status == 'rejected']),
            'total_approved_days': total_approved_days,
            'average_days_per_request': total_approved_days / len(approved_requests) if approved_requests else 0,
            'type_stats': type_stats
        }
        
        return render_template('leaves/report.html',
                             leave_requests=leave_requests,
                             employees=employees,
                             departments=departments,
                             leave_types=leave_types,
                             employee_id=employee_id,
                             department_id=department_id,
                             leave_type=leave_type,
                             status=status,
                             date_from=date_from,
                             date_to=date_to,
                             stats=stats,
                             report_title="تقرير الإجازات والاستئذان",
                             report_period=f"من {date_from} إلى {date_to}",
                             report_date=date.today().strftime('%Y-%m-%d'))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('leaves.index'))
