{% extends "base.html" %}

{% block title %}تعديل إنذار{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-edit"></i>
            تعديل إنذار - {{ warning.employee.full_name }}
          </h3>
          <div class="card-tools">
            <a href="{{ url_for('warnings.index') }}" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a>
          </div>
        </div>

        <form id="warningForm" enctype="multipart/form-data">
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="employee_id">الموظف <span class="text-danger">*</span></label>
                  <select class="form-control" id="employee_id" name="employee_id" required>
                    <option value="">اختر الموظف</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}" 
                            {% if warning.employee_id == employee.id %}selected{% endif %}>
                      {{ employee.full_name }} - {{ employee.employee_number }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="warning_type">نوع الإنذار <span class="text-danger">*</span></label>
                  <select class="form-control" id="warning_type" name="warning_type" required>
                    <option value="">اختر نوع الإنذار</option>
                    {% for type_code, type_name in warning_types %}
                    <option value="{{ type_code }}" 
                            {% if warning.warning_type == type_code %}selected{% endif %}>
                      {{ type_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="category">الفئة <span class="text-danger">*</span></label>
                  <select class="form-control" id="category" name="category" required>
                    <option value="">اختر الفئة</option>
                    {% for cat_code, cat_name in categories %}
                    <option value="{{ cat_code }}" 
                            {% if warning.category == cat_code %}selected{% endif %}>
                      {{ cat_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="severity">مستوى الخطورة</label>
                  <select class="form-control" id="severity" name="severity">
                    {% for sev_code, sev_name in severities %}
                    <option value="{{ sev_code }}" 
                            {% if warning.severity == sev_code %}selected{% endif %}>
                      {{ sev_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="status">الحالة</label>
                  <select class="form-control" id="status" name="status">
                    {% for status_code, status_name in statuses %}
                    <option value="{{ status_code }}" 
                            {% if warning.status == status_code %}selected{% endif %}>
                      {{ status_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="title">عنوان الإنذار <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" id="title" name="title" 
                         value="{{ warning.title }}" placeholder="عنوان مختصر للإنذار..." required>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="description">وصف الإنذار <span class="text-danger">*</span></label>
                  <textarea class="form-control" id="description" name="description" rows="4" 
                            placeholder="وصف تفصيلي للمخالفة أو السلوك المؤدي للإنذار..." required>{{ warning.description }}</textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="incident_date">تاريخ الحادثة <span class="text-danger">*</span></label>
                  <input type="date" class="form-control" id="incident_date" name="incident_date" 
                         value="{{ warning.incident_date.strftime("%Y-%m-%d") if warning.incident_date else "-" }}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="deadline">الموعد النهائي للتصحيح</label>
                  <input type="date" class="form-control" id="deadline" name="deadline"
                         value="{% if warning.deadline %}{{ warning.deadline.strftime("%Y-%m-%d") if warning.deadline else "-" }}{% endif %}">
                  <small class="form-text text-muted">إذا كان هناك إجراء مطلوب</small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="follow_up_date">تاريخ المتابعة</label>
                  <input type="date" class="form-control" id="follow_up_date" name="follow_up_date"
                         value="{% if warning.follow_up_date %}{{ warning.follow_up_date.strftime("%Y-%m-%d") if warning.follow_up_date else "-" }}{% endif %}">
                  <small class="form-text text-muted">تاريخ مراجعة الإنذار</small>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="action_required">الإجراء المطلوب</label>
                  <textarea class="form-control" id="action_required" name="action_required" rows="3" 
                            placeholder="الإجراءات التصحيحية المطلوبة من الموظف...">{{ warning.action_required or '' }}</textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="resolution_notes">ملاحظات الحل</label>
                  <textarea class="form-control" id="resolution_notes" name="resolution_notes" rows="3" 
                            placeholder="ملاحظات حول حل الإنذار أو المتابعة...">{{ warning.resolution_notes or '' }}</textarea>
                  <small class="form-text text-muted">تستخدم عند تغيير حالة الإنذار إلى "تم الحل"</small>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="attachment">مرفق جديد (اختياري)</label>
                  <input type="file" class="form-control-file" id="attachment" name="attachment" 
                         accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                  <small class="form-text text-muted">
                    الملفات المسموحة: PDF, DOC, DOCX, JPG, PNG (حد أقصى 5 ميجابايت)
                  </small>
                  {% if warning.attachment_path %}
                  <div class="mt-2">
                    <small class="text-info">
                      <i class="fas fa-paperclip"></i>
                      يوجد مرفق حالي - سيتم استبداله عند رفع ملف جديد
                    </small>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- معلومات الإنذار -->
            <div class="row">
              <div class="col-12">
                <div class="card card-outline card-info">
                  <div class="card-header">
                    <h3 class="card-title">معلومات الإنذار</h3>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-4">
                        <strong>تاريخ الإنشاء:</strong><br>
                        <small class="text-muted">{{ warning.created_at.strftime("%Y-%m-%d %H:%M") if warning.created_at else "-" }}</small>
                      </div>
                      <div class="col-md-4">
                        <strong>منشئ الإنذار:</strong><br>
                        <small class="text-muted">
                          {% if warning.creator %}{{ warning.creator.username }}{% else %}النظام{% endif %}
                        </small>
                      </div>
                      <div class="col-md-4">
                        <strong>آخر تحديث:</strong><br>
                        <small class="text-muted">{{ warning.updated_at.strftime("%Y-%m-%d %H:%M") if warning.updated_at else "-" }}</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i>
              حفظ التعديلات
            </button>
            <a href="{{ url_for('warnings.index') }}" class="btn btn-secondary">
              <i class="fas fa-times"></i>
              إلغاء
            </a>
            <button type="button" class="btn btn-danger float-right" 
                                onclick="deleteWarning('{{ warning.id }}')">
                          <i class="fas fa-trash"></i>
                          حذف الإنذار
                        </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
// التحقق من حجم الملف
document.getElementById('attachment').addEventListener('change', function() {
  const file = this.files[0];
  if (file) {
    const maxSize = 5 * 1024 * 1024; // 5 ميجابايت
    if (file.size > maxSize) {
      alert('حجم الملف يجب أن يكون أقل من 5 ميجابايت');
      this.value = '';
    }
  }
});

// إظهار/إخفاء ملاحظات الحل حسب الحالة
document.getElementById('status').addEventListener('change', function() {
  const status = this.value;
  const resolutionNotesGroup = document.getElementById('resolution_notes').closest('.form-group');
  
  if (status === 'resolved') {
    resolutionNotesGroup.style.display = 'block';
    document.getElementById('resolution_notes').required = true;
  } else {
    resolutionNotesGroup.style.display = 'block'; // إبقاؤها ظاهرة دائماً
    document.getElementById('resolution_notes').required = false;
  }
});

// إرسال النموذج
document.getElementById('warningForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  // التحقق من صحة التواريخ
  const incidentDate = new Date(document.getElementById('incident_date').value);
  const deadline = document.getElementById('deadline').value;
  const followUpDate = document.getElementById('follow_up_date').value;
  
  if (deadline) {
    const deadlineDate = new Date(deadline);
    if (deadlineDate <= incidentDate) {
      alert('الموعد النهائي يجب أن يكون بعد تاريخ الحادثة');
      return;
    }
  }
  
  if (followUpDate) {
    const followUpDateObj = new Date(followUpDate);
    if (followUpDateObj <= incidentDate) {
      alert('تاريخ المتابعة يجب أن يكون بعد تاريخ الحادثة');
      return;
    }
  }
  
  // التحقق من ملاحظات الحل إذا كانت الحالة "تم الحل"
  const status = document.getElementById('status').value;
  const resolutionNotes = document.getElementById('resolution_notes').value.trim();
  
  if (status === 'resolved' && !resolutionNotes) {
    alert('ملاحظات الحل مطلوبة عند تغيير الحالة إلى "تم الحل"');
    return;
  }
  
  const formData = new FormData(this);
  
  // إظهار مؤشر التحميل
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
  submitBtn.disabled = true;
  
  fetch('{{ url_for("warnings.edit", warning_id=warning.id) }}', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // إظهار رسالة نجاح
      showAlert('success', data.message);
      
      // إعادة توجيه بعد ثانيتين
      setTimeout(() => {
        window.location.href = '{% url "warnings:index" %}';
      }, 2000);
    } else {
      showAlert('danger', data.message);
      
      // إعادة تفعيل الزر
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }
  })
  .catch(error => {
    showAlert('danger', 'حدث خطأ في الاتصال');
    
    // إعادة تفعيل الزر
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  });
});

// حذف الإنذار
function deleteWarning(id) {
  if (confirm('هل أنت متأكد من حذف الإنذار؟\nلا يمكن التراجع عن هذا الإجراء.')) {
    fetch(`/warnings/${id}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showAlert('success', data.message);
        setTimeout(() => {
          window.location.href = '{% url "warnings:index" %}';
        }, 2000);
      } else {
        showAlert('danger', data.message);
      }
    })
    .catch(error => {
      showAlert('danger', 'حدث خطأ في الاتصال');
    });
  }
}

function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert">
      <span>&times;</span>
    </button>
  `;
  
  // إدراج التنبيه في أعلى الصفحة
  const container = document.querySelector('.container-fluid');
  container.insertBefore(alertDiv, container.firstChild);
  
  // إزالة التنبيه بعد 5 ثوان
  setTimeout(() => {
    alertDiv.remove();
  }, 5000);
}
</script>
{% endblock %}
