{% extends "base.html" %} {% block title %}النسخ الاحتياطية - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-database me-2"></i>
    إدارة النسخ الاحتياطية
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <button type="button" class="btn btn-primary" onclick="createBackup()">
        <i class="fas fa-plus me-1"></i>
        إنشاء نسخة احتياطية
      </button>
      <a href="{{ {% url 'admin_index' %} }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        العودة للوحة التحكم
      </a>
    </div>
  </div>
</div>

<!-- إحصائيات النسخ الاحتياطية -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card text-white bg-primary">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h4>{{ backup_stats.total_count if backup_stats else 0 }}</h4>
            <p class="mb-0">إجمالي النسخ</p>
          </div>
          <div class="align-self-center">
            <i class="fas fa-database fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-success">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h4 id="total-size">
              {{ backup_stats.total_size if backup_stats else '0 Bytes' }}
            </h4>
            <p class="mb-0">إجمالي الحجم</p>
          </div>
          <div class="align-self-center">
            <i class="fas fa-hdd fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-info">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h4>{{ backup_stats.auto_count if backup_stats else 0 }}</h4>
            <p class="mb-0">نسخ تلقائية</p>
          </div>
          <div class="align-self-center">
            <i class="fas fa-clock fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-warning">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h4>{{ backup_stats.manual_count if backup_stats else 0 }}</h4>
            <p class="mb-0">نسخ يدوية</p>
          </div>
          <div class="align-self-center">
            <i class="fas fa-user fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- معلومات إضافية -->
<div class="row mb-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-info-circle me-2"></i>
          معلومات تفصيلية
        </h6>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-6">
            <small class="text-muted">متوسط حجم النسخة:</small>
            <div class="fw-bold" id="average-size">
              {{ backup_stats.average_size if backup_stats else '0 Bytes' }}
            </div>
          </div>
          <div class="col-6">
            <small class="text-muted">آخر نسخة احتياطية:</small>
            <div class="fw-bold">
              {% if backup_stats and backup_stats.latest_backup %} {{
              backup_stats.latest_backup.created_at|strftime("%Y-%m-%d %H:%M")
              }} {% else %} لا توجد نسخ {% endif %}
            </div>
          </div>
        </div>
        <hr class="my-3" />
        <div class="row">
          <div class="col-12">
            <small class="text-muted">استخدام مساحة التخزين:</small>
            <div class="progress mt-2" style="height: 8px">
              {% set storage_percentage = ((backup_stats.total_size_bytes if
              backup_stats else 0) / (100 * 1024 * 1024)) * 100 %} {% if
              storage_percentage > 100 %} {% set storage_percentage = 100 %} {%
              endif %}
              <div
                class="progress-bar {% if storage_percentage < 50 %}bg-success {% elif storage_percentage < 80 %}bg-warning {% else %}bg-danger {% endif %}"
                role="progressbar"
                data-width="{{ storage_percentage|default(0)|round(1) }}"
                aria-valuenow="{{ storage_percentage|default(0) }}"
                aria-valuemin="0"
                aria-valuemax="100"
              ></div>
            </div>
            <small class="text-muted"
              >{{ storage_percentage|round(1) }}% من 100 MB</small
            >
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-chart-pie me-2"></i>
          توزيع النسخ
        </h6>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-6">
            <small class="text-muted">نسبة النسخ التلقائية:</small>
            <div class="fw-bold">
              {% if backup_stats and backup_stats.total_count > 0 %} {%
              widthratio backup_stats.auto_count backup_stats.total_count 100
              %}% {% else %} 0% {% endif %}
            </div>
          </div>
          <div class="col-6">
            <small class="text-muted">نسبة النسخ اليدوية:</small>
            <div class="fw-bold">
              {% if backup_stats and backup_stats.total_count > 0 %} {%
              widthratio backup_stats.manual_count backup_stats.total_count 100
              %}% {% else %} 0% {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- قائمة النسخ الاحتياطية -->
<div class="card">
  <div class="card-header">
    <h5 class="mb-0">قائمة النسخ الاحتياطية</h5>
  </div>
  <div class="card-body">
    <div class="table-responsive">
      <table class="table table-hover">
        <thead>
          <tr>
            <th>اسم الملف</th>
            <th>الحجم</th>
            <th>تاريخ الإنشاء</th>
            <th>النوع</th>
            <th>الحالة</th>
            <th>الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          {% for backup in backups %}
          <tr>
            <td>
              <i class="fas fa-file-archive me-2 text-primary"></i>
              {{ backup.filename }}
            </td>
            <td>
              <span
                class="text-muted"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                title="{{ backup.size_bytes }} بايت"
              >
                <i class="fas fa-weight me-1"></i>
                {{ backup.size }}
              </span>
            </td>
            <td>{{ backup.created_at|strftime("%Y-%m-%d %H:%M") }}</td>
            <td>
              {% if backup.type == 'تلقائي' %}
              <span class="badge bg-success">{{ backup.type }}</span>
              {% else %}
              <span class="badge bg-primary">{{ backup.type }}</span>
              {% endif %}
            </td>
            <td>
              <span class="badge bg-success">مكتمل</span>
            </td>
            <td>
              <div class="btn-group btn-group-sm" role="group">
                <button
                  onclick="downloadBackup('{{ backup.filename }}')"
                  class="btn btn-outline-primary"
                  title="تحميل"
                >
                  <i class="fas fa-download"></i>
                </button>
                <button
                  onclick="restoreBackup('{{ backup.filename }}')"
                  class="btn btn-outline-warning"
                  title="استعادة"
                >
                  <i class="fas fa-undo"></i>
                </button>
                <button
                  onclick="deleteBackup('{{ backup.id }}', '{{ backup.filename }}')"
                  class="btn btn-outline-danger"
                  title="حذف"
                >
                  <i class="fas fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    {% if not backups %}
    <div class="text-center py-5">
      <i class="fas fa-database fa-3x text-muted mb-3"></i>
      <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
      <p class="text-muted">لم يتم إنشاء أي نسخ احتياطية بعد.</p>
      <button type="button" class="btn btn-primary" onclick="createBackup()">
        <i class="fas fa-plus me-1"></i>
        إنشاء أول نسخة احتياطية
      </button>
    </div>
    {% endif %}
  </div>
</div>

<!-- إعدادات النسخ الاحتياطية -->
<div class="card mt-4">
  <div class="card-header">
    <h5 class="mb-0">إعدادات النسخ الاحتياطية التلقائية</h5>
  </div>
  <div class="card-body">
    <form>
      <div class="row">
        <div class="col-md-4 mb-3">
          <label for="auto_backup" class="form-label">النسخ التلقائية</label>
          <select class="form-select" id="auto_backup">
            <option value="enabled" selected>مفعل</option>
            <option value="disabled">معطل</option>
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="backup_frequency" class="form-label">التكرار</label>
          <select class="form-select" id="backup_frequency">
            <option value="daily" selected>يومي</option>
            <option value="weekly">أسبوعي</option>
            <option value="monthly">شهري</option>
          </select>
        </div>
        <div class="col-md-4 mb-3">
          <label for="backup_time" class="form-label">الوقت</label>
          <input
            type="time"
            class="form-control"
            id="backup_time"
            value="02:00"
          />
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 mb-3">
          <label for="retention_days" class="form-label"
            >الاحتفاظ بالنسخ (أيام)</label
          >
          <input
            type="number"
            class="form-control"
            id="retention_days"
            value="30"
            min="1"
            max="365"
          />
        </div>
        <div class="col-md-6 mb-3">
          <label for="max_backups" class="form-label">الحد الأقصى للنسخ</label>
          <input
            type="number"
            class="form-control"
            id="max_backups"
            value="10"
            min="1"
            max="50"
          />
        </div>
      </div>

      <div class="d-grid gap-2 d-md-flex justify-content-md-end">
        <button
          type="button"
          class="btn btn-primary"
          onclick="saveBackupSettings()"
        >
          <i class="fas fa-save me-1"></i>
          حفظ الإعدادات
        </button>
      </div>
    </form>
  </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تأكيد الحذف</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <p>
          هل أنت متأكد من رغبتك في حذف النسخة الاحتياطية
          <strong id="backupName"></strong>؟
        </p>
        <div class="alert alert-danger">
          <i class="fas fa-exclamation-triangle me-2"></i>
          تحذير: لا يمكن التراجع عن هذا الإجراء.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إلغاء
        </button>
        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
          حذف
        </button>
      </div>
    </div>
  </div>
</div>

<!-- نافذة تأكيد الاستعادة -->
<div class="modal fade" id="restoreModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">تأكيد الاستعادة</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <p>
          هل أنت متأكد من رغبتك في استعادة النسخة الاحتياطية
          <strong id="restoreBackupName"></strong>؟
        </p>
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle me-2"></i>
          تحذير: سيتم استبدال البيانات الحالية بالبيانات من النسخة الاحتياطية.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إلغاء
        </button>
        <button
          type="button"
          class="btn btn-warning"
          onclick="confirmRestore()"
        >
          استعادة
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  let backupToDelete = null;
  let backupToRestore = null;

  function createBackup() {
    // إظهار مؤشر التحميل
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML =
      '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإنشاء...';
    btn.disabled = true;

    // إرسال طلب إنشاء النسخة الاحتياطية
    fetch("/admin/backup/create", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        btn.innerHTML = originalText;
        btn.disabled = false;

        if (data.success) {
          showAlert("success", data.message);
          // تحديث الإحصائيات والجدول بدلاً من إعادة تحميل الصفحة
          updateBackupStats();
          setTimeout(() => location.reload(), 1500);
        } else {
          showAlert("error", data.message || "حدث خطأ غير معروف");
        }
      })
      .catch((error) => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        showAlert(
          "error",
          "حدث خطأ أثناء إنشاء النسخة الاحتياطية: " + error.message
        );
        console.error("Error:", error);
      });
  }

  function downloadBackup(filename) {
    window.open(`/admin/backup/download/${filename}`, "_blank");
  }

  function restoreBackup(filename) {
    backupToRestore = filename;
    document.getElementById("restoreBackupName").textContent = filename;

    const restoreModal = new bootstrap.Modal(
      document.getElementById("restoreModal")
    );
    restoreModal.show();
  }

  function confirmRestore() {
    if (backupToRestore) {
      alert(
        `سيتم استعادة النسخة الاحتياطية: ${backupToRestore} (وظيفة تجريبية)`
      );

      const restoreModal = bootstrap.Modal.getInstance(
        document.getElementById("restoreModal")
      );
      restoreModal.hide();

      backupToRestore = null;
    }
  }

  function deleteBackup(backupId, filename) {
    backupToDelete = backupId;
    document.getElementById("backupName").textContent = filename;

    const deleteModal = new bootstrap.Modal(
      document.getElementById("deleteModal")
    );
    deleteModal.show();
  }

  function confirmDelete() {
    if (backupToDelete) {
      alert(`سيتم حذف النسخة الاحتياطية رقم ${backupToDelete} (وظيفة تجريبية)`);

      const deleteModal = bootstrap.Modal.getInstance(
        document.getElementById("deleteModal")
      );
      deleteModal.hide();

      backupToDelete = null;
      location.reload();
    }
  }

  function saveBackupSettings() {
    const settings = {
      auto_backup: document.getElementById("auto_backup").value,
      backup_frequency: document.getElementById("backup_frequency").value,
      backup_time: document.getElementById("backup_time").value,
      retention_days: document.getElementById("retention_days").value,
      max_backups: document.getElementById("max_backups").value,
    };

    showAlert("success", "تم حفظ إعدادات النسخ الاحتياطية بنجاح");
  }

  // تحديث الوظائف لاستخدام API الحقيقي
  function downloadBackup(filename) {
    window.open(`/admin/backup/download/${filename}`, "_blank");
  }

  function confirmRestore() {
    if (backupToRestore) {
      const restoreModal = bootstrap.Modal.getInstance(
        document.getElementById("restoreModal")
      );
      restoreModal.hide();

      showAlert("info", "جاري استعادة النسخة الاحتياطية...");

      fetch("/admin/backup/restore", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          filename: backupToRestore,
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            showAlert("success", data.message);
            setTimeout(() => location.reload(), 2000);
          } else {
            showAlert("error", data.message);
          }
        })
        .catch((error) => {
          showAlert("error", "حدث خطأ أثناء استعادة النسخة الاحتياطية");
          console.error("Error:", error);
        });

      backupToRestore = null;
    }
  }

  function confirmDelete() {
    if (backupToDelete) {
      const deleteModal = bootstrap.Modal.getInstance(
        document.getElementById("deleteModal")
      );
      deleteModal.hide();

      fetch("/admin/backup/delete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          filename: backupToDelete,
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            showAlert("success", data.message);
            // تحديث الإحصائيات بدلاً من إعادة تحميل الصفحة
            updateBackupStats();
            setTimeout(() => location.reload(), 1500);
          } else {
            showAlert("error", data.message);
          }
        })
        .catch((error) => {
          showAlert("error", "حدث خطأ أثناء حذف النسخة الاحتياطية");
          console.error("Error:", error);
        });

      backupToDelete = null;
    }
  }

  // دالة لإظهار التنبيهات
  function showAlert(type, message) {
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText =
      "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";

    const icon =
      type ===  "success"
        ? "check-circle"
        : type ===  "error"
        ? "exclamation-triangle"

        ? "exclamation-circle"
        : "info-circle":

    alertDiv.innerHTML = `
          <i class="fas fa-${icon} me-2"></i>
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }

  // دالة تحديث إحصائيات النسخ الاحتياطية
  function updateBackupStats() {
    fetch("/admin/backup/stats")
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // تحديث إجمالي الحجم
          const totalSizeElement = document.getElementById("total-size");
          if (totalSizeElement) {
            totalSizeElement.textContent = data.stats.total_size;
          }

          // تحديث عدد النسخ الإجمالي
          const totalCountElements = document.querySelectorAll(".total-count");
          totalCountElements.forEach((element) => {
            element.textContent = data.stats.total_count;
          });

          // تحديث عدد النسخ التلقائية
          const autoCountElements = document.querySelectorAll(".auto-count");
          autoCountElements.forEach((element) => {
            element.textContent = data.stats.auto_count;
          });

          // تحديث عدد النسخ اليدوية
          const manualCountElements =
            document.querySelectorAll(".manual-count");
          manualCountElements.forEach((element) => {
            element.textContent = data.stats.manual_count;
          });

          // تحديث متوسط الحجم
          const averageSizeElement = document.getElementById("average-size");
          if (averageSizeElement) {
            averageSizeElement.textContent = data.stats.average_size;
          }
        }
      })
      .catch((error) => {
        console.error("خطأ في تحديث الإحصائيات:", error);
      });
  }

  // تحديث الإحصائيات عند تحميل الصفحة
  document.addEventListener("DOMContentLoaded", function () {
    updateBackupStats();

    // تفعيل tooltips
    let tooltipTriggerList = [].slice.call(
      document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    let tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Set progress bar width from data attribute
    const progressBar = document.querySelector(".progress-bar[data-width]");
    if (progressBar) {
      const width = progressBar.getAttribute("data-width");
      progressBar.style.width = width + "%";
    }
  });
</script>
{% endblock %}
