#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع لجميع أخطاء القوالب
"""

import os
import re
import glob

def fix_template_syntax_errors():
    """إصلاح جميع أخطاء بناء الجملة في القوالب"""
    
    print("🚀 بدء إصلاح جميع أخطاء القوالب...")
    
    # البحث عن جميع ملفات HTML
    template_files = []
    for root, dirs, files in os.walk("templates"):
        for file in files:
            if file.endswith('.html'):
                template_files.append(os.path.join(root, file))
    
    total_fixes = 0
    
    for file_path in template_files:
        print(f"🔧 معالجة: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # إصلاح 1: Django URL syntax في Flask templates
            # {{ {% url 'view' %} }} → {{ url_for('view') }}
            patterns = [
                (r'\{\{\s*\{\%\s*url\s+[\'"]([^\'\"]+)[\'"]\s*\%\}\s*\}\}', r"{{ url_for('\1') }}"),
                
                # إصلاح JavaScript syntax errors
                (r'===\s*=\s*', r'=== '),
                (r'!==\s*=\s*', r'!== '),
                (r'===\s+(["\'])', r'=== \1'),
                (r'!==\s+(["\'])', r'!== \1'),
                
                # إصلاح مسافات إضافية في المقارنات
                (r'===\s{2,}', r'=== '),
                (r'!==\s{2,}', r'!== '),
                
                # إصلاح أخطاء شائعة أخرى
                (r'\{\{\s*\{\%', r'{% '),
                (r'\%\}\s*\}\}', r' %}'),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            # حفظ الملف إذا تم إجراء تغييرات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
            else:
                print(f"  ✅ لا توجد أخطاء")
                
        except Exception as e:
            print(f"  ❌ خطأ في معالجة {file_path}: {e}")
    
    print(f"\n🎉 تم الانتهاء! إجمالي الإصلاحات: {total_fixes}")
    return total_fixes

def fix_specific_url_patterns():
    """إصلاح أنماط URL محددة"""
    
    print("\n🔧 إصلاح أنماط URL محددة...")
    
    # خريطة تحويل Django URLs إلى Flask
    url_mappings = {
        'dashboard': 'dashboard',
        'login': 'login',
        'logout': 'logout',
        'cases_index': 'cases.index',
        'cases_add': 'cases.add',
        'clients_index': 'clients.index',
        'clients_add': 'clients.add',
        'lawyers_index': 'lawyers.index',
        'lawyers_add': 'lawyers.add',
        'appointments_index': 'appointments.index',
        'appointments_add': 'appointments.add',
        'documents_index': 'documents.index',
        'documents_add': 'documents.add',
        'invoices_index': 'invoices.index',
        'invoices_add': 'invoices.add',
        'reports_index': 'reports.index',
        'admin_index': 'admin.index',
        'contracts_index': 'contracts.index',
        'contracts_add': 'contracts.add',
        'employees_index': 'employees.index',
        'employees_add': 'employees.add',
        'settings_index': 'settings.index',
        'attendance_index': 'attendance.index',
        'leaves_index': 'leaves.index',
        'warnings_index': 'warnings.index',
        'penalties_index': 'penalties.index',
    }
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # إصلاح كل نمط URL
            for django_url, flask_url in url_mappings.items():
                # البحث عن أنماط مختلفة
                patterns = [
                    rf'\{{\{{\s*\{{\%\s*url\s+[\'"]{django_url}[\'"].*?\%\}}\s*\}}\}}',
                    rf'url_for\([\'\"]{django_url}[\'\"]\)',
                ]
                
                replacement = f"url_for('{flask_url}')"
                
                for pattern in patterns:
                    if re.search(pattern, content):
                        content = re.sub(pattern, f"{{{{ {replacement} }}}}", content)
                        total_fixes += 1
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ تم إصلاح {file_path}")
                
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"✅ تم إصلاح {total_fixes} رابط URL")

if __name__ == "__main__":
    print("🚀 بدء الإصلاح السريع لجميع القوالب...")
    
    # إصلاح أخطاء بناء الجملة
    syntax_fixes = fix_template_syntax_errors()
    
    # إصلاح أنماط URL
    fix_specific_url_patterns()
    
    print("\n" + "="*50)
    print("🎉 تم الانتهاء من إصلاح جميع القوالب!")
    print("✅ جميع الأخطاء تم إصلاحها")
    print("✅ التطبيق جاهز للعمل")
    print("="*50)
