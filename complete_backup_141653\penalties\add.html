{% extends "base.html" %}

{% block title %}إضافة جزاء جديد{% endblock %}

{% block content %}
<div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-header"><h3 class="card-title"><i class="fas fa-plus"></i>
            إضافة جزاء جديد
          </h3><div class="card-tools"><a href="{{ url_for('penalties.index' }}" class="btn btn-secondary btn-sm"><i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a></div></div><form id="penaltyForm" enctype="multipart/form-data"><div class="card-body"><div class="row"><div class="col-md-6"><div class="form-group"><label for="employee_id">الموظف <span class="text-danger">*</span></label><select class="form-control" id="employee_id" name="employee_id" required><option value="">اختر الموظف</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}">
                      {{ employee.full_name }} - {{ employee.employee_number }}
                    </option>
                    {% endfor %}
                  </select></div></div><div class="col-md-6"><div class="form-group"><label for="penalty_type">نوع الجزاء <span class="text-danger">*</span></label><select class="form-control" id="penalty_type" name="penalty_type" required><option value="">اختر نوع الجزاء</option>
                    {% for type_code, type_name in penalty_types %}
                    <option value="{{ type_code }}">{{ type_name }}</option>
                    {% endfor %}
                  </select></div></div></div><div class="row"><div class="col-md-6"><div class="form-group"><label for="category">الفئة <span class="text-danger">*</span></label><select class="form-control" id="category" name="category" required><option value="">اختر الفئة</option>
                    {% for cat_code, cat_name in categories %}
                    <option value="{{ cat_code }}">{{ cat_name }}</option>
                    {% endfor %}
                  </select></div></div><div class="col-md-6"><div class="form-group"><label for="severity">مستوى الخطورة</label><select class="form-control" id="severity" name="severity">
                    {% for sev_code, sev_name in severities %}
                    <option value="{{ sev_code }}" {% if sev_code == 'medium' %}selected{% endif %}>
                      {{ sev_name }}
                    </option>
                    {% endfor %}
                  </select></div></div></div><div class="row"><div class="col-12"><div class="form-group"><label for="title">عنوان الجزاء <span class="text-danger">*</span></label><input type="text" class="form-control" id="title" name="title" 
                         placeholder="عنوان مختصر للجزاء..." required></div></div></div><div class="row"><div class="col-12"><div class="form-group"><label for="description">وصف الجزاء <span class="text-danger">*</span></label><textarea class="form-control" id="description" name="description" rows="4" 
                            placeholder="وصف تفصيلي للمخالفة والجزاء المفروض..." required></textarea></div></div></div><div class="row"><div class="col-md-6"><div class="form-group"><label for="incident_date">تاريخ الحادثة <span class="text-danger">*</span></label><input type="date" class="form-control" id="incident_date" name="incident_date" 
                         value="{{ today }}" required></div></div><div class="col-md-6"><div class="form-group"><label for="penalty_date">تاريخ الجزاء <span class="text-danger">*</span></label><input type="date" class="form-control" id="penalty_date" name="penalty_date" 
                         value="{{ today }}" required></div></div></div><!-- تفاصيل الجزاء حسب النوع --><div class="row"><div class="col-md-6"><div class="form-group" id="amount_group" style="display: none;"><label for="amount">مبلغ الجزاء (ريال)</label><input type="number" class="form-control" id="amount" name="amount" 
                         step="0.01" min="0" placeholder="0.00"><small class="form-text text-muted">للجزاءات المالية فقط</small></div></div><div class="col-md-6"><div class="form-group" id="days_group" style="display: none;"><label for="days_count">عدد أيام الإيقاف</label><input type="number" class="form-control" id="days_count" name="days_count" 
                         min="1" placeholder="1"><small class="form-text text-muted">لجزاءات الإيقاف عن العمل</small></div></div></div><div class="row"><div class="col-12"><div class="form-group"><label for="notes">ملاحظات إضافية</label><textarea class="form-control" id="notes" name="notes" rows="3" 
                            placeholder="أي ملاحظات إضافية حول الجزاء..."></textarea></div></div></div><div class="row"><div class="col-md-6"><div class="form-group"><label for="attachment">مرفق (اختياري)</label><input type="file" class="form-control-file" id="attachment" name="attachment" 
                         accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"><small class="form-text text-muted">
                    الملفات المسموحة: PDF, DOC, DOCX, JPG, PNG (حد أقصى 5 ميجابايت)
                  </small></div></div></div><!-- معلومات إضافية --><div class="row"><div class="col-12"><div class="card card-outline card-danger"><div class="card-header"><h3 class="card-title">إرشادات مهمة</h3></div><div class="card-body"><ul class="list-unstyled"><li><i class="fas fa-exclamation-triangle text-danger"></i> تأكد من عدالة الجزاء وتناسبه مع المخالفة</li><li><i class="fas fa-exclamation-triangle text-danger"></i> الجزاءات المالية تحتاج موافقة الإدارة العليا</li><li><i class="fas fa-exclamation-triangle text-danger"></i> جزاءات الفصل تتطلب إجراءات قانونية خاصة</li><li><i class="fas fa-exclamation-triangle text-danger"></i> يجب توثيق جميع الجزاءات في ملف الموظف</li><li><i class="fas fa-exclamation-triangle text-danger"></i> للموظف حق الاعتراض على الجزاء خلال فترة محددة</li></ul></div></div></div></div></div><div class="card-footer"><button type="submit" class="btn btn-primary"><i class="fas fa-gavel"></i>
              فرض الجزاء
            </button><a href="{{ url_for('penalties.index' }}" class="btn btn-secondary"><i class="fas fa-times"></i>
              إلغاء
            </a></div></form></div></div></div></div><script>
// إظهار/إخفاء الحقول حسب نوع الجزاء
document.getElementById('penalty_type').addEventListener('change', function() {
  const penaltyType = this.value;
  const amountGroup = document.getElementById('amount_group');
  const daysGroup = document.getElementById('days_group');
  const amountInput = document.getElementById('amount');
  const daysInput = document.getElementById('days_count');
  
  // إخفاء جميع الحقول أولاً
  amountGroup.style.display = 'none';
  daysGroup.style.display = 'none';
  amountInput.required = false;
  daysInput.required = false;
  
  // إظهار الحقول المناسبة
  switch(penaltyType) {
    case 'financial':
      amountGroup.style.display = 'block';
      amountInput.required = true;
      break;
    case 'suspension':
      daysGroup.style.display = 'block';
      daysInput.required = true;
      break;
    case 'termination':
      // لا حاجة لحقول إضافية
      break;
    case 'demotion':
      // يمكن إضافة حقول خاصة بالتخفيض
      break;
    case 'warning':
      // لا حاجة لحقول إضافية
      break;
  }
});

// تحديث مستوى الخطورة حسب نوع الجزاء
document.getElementById('penalty_type').addEventListener('change', function() {
  const penaltyType = this.value;
  const severitySelect = document.getElementById('severity');
  
  // تحديد مستوى الخطورة الافتراضي حسب نوع الجزاء
  switch(penaltyType) {
    case 'termination':
      severitySelect.value = 'critical';
      break;
    case 'suspension':
      severitySelect.value = 'high';
      break;
    case 'financial':
      severitySelect.value = 'medium';
      break;
    case 'demotion':
      severitySelect.value = 'high';
      break;
    case 'warning':
      severitySelect.value = 'low';
      break;
    default:
      severitySelect.value = 'medium';
  }
});

// تحديث مستوى الخطورة حسب الفئة
document.getElementById('category').addEventListener('change', function() {
  const category = this.value;
  const severitySelect = document.getElementById('severity');
  
  // تحديد مستوى الخطورة الافتراضي حسب الفئة
  switch(category) {
    case 'safety':
      severitySelect.value = 'critical';
      break;
    case 'financial':
      severitySelect.value = 'high';
      break;
    case 'conduct':
      severitySelect.value = 'high';
      break;
    case 'attendance':
      severitySelect.value = 'medium';
      break;
    case 'performance':
      severitySelect.value = 'medium';
      break;
    case 'policy':
      severitySelect.value = 'medium';
      break;
    default:
      severitySelect.value = 'medium';
  }
});

// التحقق من حجم الملف
document.getElementById('attachment').addEventListener('change', function() {
  const file = this.files[0];
  if (file) {
    const maxSize = 5 * 1024 * 1024; // 5 ميجابايت
    if (file.size > maxSize) {
      alert('حجم الملف يجب أن يكون أقل من 5 ميجابايت');
      this.value = '';
    }
  }
});

// إرسال النموذج
document.getElementById('penaltyForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  // التحقق من صحة التواريخ
  const incidentDate = new Date(document.getElementById('incident_date').value);
  const penaltyDate = new Date(document.getElementById('penalty_date').value);
  
  if (penaltyDate < incidentDate) {
    alert('تاريخ الجزاء يجب أن يكون بعد أو في نفس تاريخ الحادثة');
    return;
  }
  
  // التحقق من البيانات المطلوبة حسب نوع الجزاء
  const penaltyType = document.getElementById('penalty_type').value;
  const amount = document.getElementById('amount').value;
  const daysCount = document.getElementById('days_count').value;
  
  if (penaltyType === 'financial' && (!amount || parseFloat(amount) <= 0) {
    alert('مبلغ الجزاء مطلوب للجزاءات المالية');
    return;
  }
  
  if (penaltyType === 'suspension' && (!daysCount || parseInt(daysCount) <= 0) {
    alert('عدد أيام الإيقاف مطلوب');
    return;
  }
  
  // تأكيد إضافي للجزاءات الخطيرة
  const severity = document.getElementById('severity').value;
  if (severity === 'critical' || penaltyType === 'termination') {
    if (!confirm('هذا جزاء خطير قد يؤثر على مستقبل الموظف.\nهل أنت متأكد من المتابعة؟') {
      return;
    }
  }
  
  const formData = new FormData(this);
  
  // إظهار مؤشر التحميل
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
  submitBtn.disabled = true;
  
  fetch('{{ url_for("penalties:add" %}', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json()
  .then(data => {
    if (data.success) {
      // إظهار رسالة نجاح
      showAlert('success', data.message);
      
      // إعادة توجيه بعد ثانيتين
      setTimeout() => {
        window.location.href = '{{ url_for("penalties:index" %}';
      }, 2000);
    } else {
      showAlert('danger', data.message);
      
      // إعادة تفعيل الزر
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }
  })
  .catch(error => {
    showAlert('danger', 'حدث خطأ في الاتصال');
    
    // إعادة تفعيل الزر
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  });
});

function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert"><span>&times;</span></button>
  `;
  
  // إدراج التنبيه في أعلى الصفحة
  const container = document.querySelector('.container-fluid');
  container.insertBefore(alertDiv, container.firstChild);
  
  // إزالة التنبيه بعد 5 ثوان
  setTimeout() => {
    alertDiv.remove();
  }, 5000);
}

// تعيين التاريخ الحالي كحد أقصى لتاريخ الحادثة
document.addEventListener('DOMContentLoaded', function() {
  const today = new Date().toISOString().split('T')[0];
  document.getElementById('incident_date').max = today;
  document.getElementById('penalty_date').min = document.getElementById('incident_date').value;
});

// تحديث الحد الأدنى لتاريخ الجزاء عند تغيير تاريخ الحادثة
document.getElementById('incident_date').addEventListener('change', function() {
  document.getElementById('penalty_date').min = this.value;
});
</script>
{% endblock %}
