{# مكون رأس التقرير للطباعة #}
<div class="print-header">
  <!-- شعار الشركة -->
  {% if show_logo != 'false' %}
  <div class="company-logo-container">
    <img
      src="{{ url_for('static', filename=logo_path or 'images/logo.svg' }}"
      alt="شعار الشركة"
      class="company-logo"
      onerror="this.src='/static/images/logo.svg'; this.onerror=function(){this.style.display='none';}"
    />
  </div>
  {% endif %}

  <!-- معلومات الشركة -->
  {% if show_company_info != 'false' %}
  <!-- اسم الشركة -->
  <div class="company-name">{{ company_name or "مكتب الشؤون القانونية" }}</div>

  <!-- وصف الشركة -->
  <div class="company-subtitle">
    {{ company_subtitle or "نظام إدارة الشؤون القانونية والموارد البشرية" }}
  </div>
  {% endif %}

  <!-- عنوان التقرير -->
  {% if report_title %}
  <div class="report-title">{{ report_title }}</div>
  {% endif %}

  <!-- معلومات التقرير -->
  <div class="report-info">
    <div class="report-period">
      {% if report_period %}
      <strong>فترة التقرير:</strong> {{ report_period }} {% elif start_date and end_date %} <strong>فترة التقرير:</strong> من {{ start_date }} إلى {{ end_date }} {% elif report_date %} <strong>تاريخ التقرير:</strong> {{ report_date }} {% endif %}
    </div>

    {% if show_print_time != 'false' %}
    <div class="print-date">
      <strong>تاريخ الطباعة:</strong>
      <span id="printDateTime"></span>
    </div>
    {% endif %}
  </div>
</div>

<!-- معلومات إضافية للتقرير -->
{% if employee %}
<div class="employee-summary no-print-header">
  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">معلومات الموظف</h5>
    </div>
    <div class="card-body">
      <div class="employee-info">
        <div class="info-section">
          <div class="info-title">البيانات الأساسية</div>
          <div class="info-row">
            <span class="info-label">رقم الموظف:</span>
            <span class="info-value">{{ employee.employee_number }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">الاسم:</span>
            <span class="info-value">{{ employee.full_name }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">القسم:</span>
            <span class="info-value"
              >{{ employee.department.name if employee.department else 'غير محدد' }}</span
            >
          </div>
          <div class="info-row">
            <span class="info-label">المنصب:</span>
            <span class="info-value"
              >{{ employee.position or 'غير محدد' }}</span
            >
          </div>
        </div>

        <div class="info-section">
          <div class="info-title">البيانات المالية</div>
          <div class="info-row">
            <span class="info-label">الراتب الأساسي:</span>
            <span class="info-value number"
              >{{ "{:,.2f}".format(employee.basic_salary if
              employee.basic_salary else '0.00' ) }} ريال</span
            >
          </div>
          <div class="info-row">
            <span class="info-label">البدلات:</span>
            <span class="info-value number"
              >{{ "{:,.2f}".format(employee.allowances if employee.allowances
              else '0.00' ) }} ريال</span
            >
          </div>
          <div class="info-row">
            <span class="info-label">إجمالي الراتب:</span>
            <span class="info-value number"
              >{{ "{:,.2f}".format(employee.total_salary if
              employee.total_salary else '0.00' ) }} ريال</span
            >
          </div>
          <div class="info-row">
            <span class="info-label">تاريخ التوظيف:</span>
            <span class="info-value date"
              >{{ employee.hire_date.hire_date else 'غير محدد'|strftime("%Y-%m-%d") if employee }}</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %}

<!-- ذيل التقرير -->
<div class="print-footer">
  <div>
    {{ company_name or "مكتب الشؤون القانونية" }} - نظام إدارة الموارد البشرية
    <br />
    تم إنشاء هذا التقرير تلقائياً بواسطة النظام
  </div>
</div>

<!-- JavaScript لتحديث وقت الطباعة -->
<script>
  document.addEventListener("DOMContentLoaded", function () {
    updatePrintDateTime();
  });

  // تحديث وقت الطباعة قبل الطباعة
  window.addEventListener("beforeprint", function () {
    updatePrintDateTime();
  });

  function updatePrintDateTime() {
    const now = new Date();
    const options = {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    };

    const dateTimeString =
      now.toLocaleDateString("ar-SA", options) +
      " " +
      now.toLocaleTimeString("ar-SA", { hour12: false });

    const printDateElement = document.getElementById("printDateTime");
    if (printDateElement) {
      printDateElement.textContent = dateTimeString;
    }
  }

  // تحديث الوقت كل ثانية
  setInterval(updatePrintDateTime, 1000);
</script>

<style>
  /* تنسيق خاص لرأس التقرير */
  .print-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .company-logo-container {
    margin-bottom: 15px;
  }

  .company-logo {
    max-height: 80px;
    max-width: 200px;
    object-fit: contain;
  }

  .company-name {
    font-size: 28px;
    font-weight: bold;
    color: #2c3e50;
    margin: 15px 0 10px 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }

  .company-subtitle {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 20px;
    font-style: italic;
  }

  .report-title {
    font-size: 24px;
    font-weight: bold;
    color: #495057;
    margin: 20px 0;
    padding: 10px;
    background-color: rgba(52, 73, 94, 0.1);
    border-radius: 5px;
    border-right: 4px solid #34495e;
  }

  .report-info {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .print-date,
  .report-period {
    font-size: 14px;
    color: #495057;
  }

  .print-date strong,
  .report-period strong {
    color: #2c3e50;
  }

  /* تحسينات للشاشة */
  @media screen {
    .print-header {
      display: none;
    }
  }

  /* تحسينات للطباعة */
  @media print {
    .print-header {
      display: block !important;
      background: white !important;
      box-shadow: none !important;
      border: 2px solid #333 !important;
      border-radius: 0 !important;
      page-break-inside: avoid;
    }

    .company-name {
      text-shadow: none !important;
    }

    .report-info {
      background-color: #f8f9fa !important;
      border: 1px solid #333 !important;
    }
  }
</style>
