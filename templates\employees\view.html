{% extends "base.html" %} {% block title %}{{ employee.full_name }} - تفاصيل
الموظف{% endblock %} {% block content %}
<!-- رأس التقرير للطباعة -->
{% include 'components/print_header.html' with context %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
><h1 class="h2"><i class="fas fa-user me-2"></i>
    تفاصيل الموظف: {{ employee.full_name }}
  </h1><div class="btn-toolbar mb-2 mb-md-0"><div class="btn-group me-2"><a
        href="{{ url_for('employees.index' }}"
        class="btn btn-outline-secondary"
  ><i class="fas fa-arrow-right me-1"></i>
        العودة للقائمة
      </a><a
        href="{{ url_for('employees.edit', id=employee.id }}"
        class="btn btn-warning"
  ><i class="fas fa-edit me-1"></i>
        تعديل
      </a><a
        href="{{ url_for('employees.documents', id=employee.id }}"
        class="btn btn-success"
  ><i class="fas fa-folder-open me-1"></i>
        المستندات
      </a><a
        href="{{ url_for('employees.employee_salary_report', employee_id=employee.id }}"
        class="btn btn-primary"
  ><i class="fas fa-money-bill-wave me-1"></i>
        تقرير الراتب
      </a><div class="btn-group"><button
          type="button"
          class="btn btn-secondary dropdown-toggle"
          data-bs-toggle="dropdown"
    ><i class="fas fa-plus me-1"></i>
          إضافة
        </button><ul class="dropdown-menu"><li><a
              class="dropdown-item"
              href="{{ url_for('attendance_add' }}?employee_id={{ employee.id }}"
        ><i class="fas fa-clock me-1"></i> تسجيل حضور
            </a></li><li><a
              class="dropdown-item"
              href="{{ url_for('leaves_add' }}?employee_id={{ employee.id }}"
        ><i class="fas fa-calendar-alt me-1"></i> طلب إجازة
            </a></li><li><a
              class="dropdown-item"
              href="{{ url_for('warnings_add' }}?employee_id={{ employee.id }}"
        ><i class="fas fa-exclamation-triangle me-1"></i> إضافة إنذار
            </a></li><li><a
              class="dropdown-item"
              href="{{ url_for('penalties_add' }}?employee_id={{ employee.id }}"
        ><i class="fas fa-gavel me-1"></i> إضافة جزاء
            </a></li></ul></div><button onclick="printEmployee()" class="btn btn-info"><i class="fas fa-print me-1"></i>
        طباعة
      </button></div></div></div><div class="row"><!-- معلومات الموظف الأساسية --><div class="col-md-8"><!-- البيانات الشخصية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-user me-2"></i>
          البيانات الشخصية
        </h5></div><div class="card-body"><div class="row"><div class="col-md-6"><table class="table table-borderless"><tr><td><strong>رقم الموظف:</strong></td><td>{{ employee.employee_number }}</td></tr><tr><td><strong>الاسم الكامل:</strong></td><td>{{ employee.full_name }}</td></tr>
              {% if employee.full_name_en %}
              <tr><td><strong>الاسم بالإنجليزية:</strong></td><td>{{ employee.full_name_en }}</td></tr>
              {% endif %}
              <tr><td><strong>رقم الهوية:</strong></td><td>{{ employee.national_id or 'غير محدد' }}</td></tr><tr><td><strong>تاريخ الميلاد:</strong></td><td>
                  {{ employee.birth_date.birth_date else 'غير محدد'|strftime("%Y-%m-%d") if employee }}
                </td></tr></table></div><div class="col-md-6"><table class="table table-borderless"><tr><td><strong>الجنس:</strong></td><td>
                  {% if employee.gender == 'male' %} ذكر {% elif employee.gender == 'female' %} أنثى {% else %} غير محدد {% endif %}
                </td></tr><tr><td><strong>الجنسية:</strong></td><td>{{ employee.nationality or 'غير محدد' }}</td></tr><tr><td><strong>الحالة الاجتماعية:</strong></td><td>
                  {% if employee.marital_status == 'single' %} أعزب {% elif employee.marital_status == 'married' %} متزوج {% elif employee.marital_status == 'divorced' %} مطلق {% elif employee.marital_status == 'widowed' %} أرمل {% else %} غير
                  محدد {% endif %}
                </td></tr><tr><td><strong>البريد الإلكتروني:</strong></td><td>
                  {% if employee.email %}
                  <a href="mailto:{{ employee.email }}">{{ employee.email }}</a>
                  {% else %}
                  <span class="text-muted">غير محدد</span>
                  {% endif %}
                </td></tr><tr><td><strong>رقم الهاتف:</strong></td><td>
                  {% if employee.phone %}
                  <a href="tel:{{ employee.phone }}">{{ employee.phone }}</a>
                  {% else %}
                  <span class="text-muted">غير محدد</span>
                  {% endif %}
                </td></tr></table></div></div>

        {% if employee.address %}
        <div class="row mt-3"><div class="col-12"><h6><strong>العنوان:</strong></h6><p class="text-muted">{{ employee.address }}</p></div></div>
        {% endif %}
      </div></div><!-- البيانات الوظيفية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>
          البيانات الوظيفية
        </h5></div><div class="card-body"><div class="row"><div class="col-md-6"><table class="table table-borderless"><tr><td><strong>القسم:</strong></td><td>
                  {{ employee.department.name if employee.department else 'غير محدد' }}
                </td></tr><tr><td><strong>المنصب:</strong></td><td>{{ employee.position or 'غير محدد' }}</td></tr>
              {% if employee.position_en %}
              <tr><td><strong>المنصب بالإنجليزية:</strong></td><td>{{ employee.position_en }}</td></tr>
              {% endif %}
              <tr><td><strong>نوع التوظيف:</strong></td><td>
                  {% if employee.employment_type == 'full_time' %}
                  <span class="badge bg-primary">دوام كامل</span>
                  {% elif employee.employment_type == 'part_time' %}
                  <span class="badge bg-info">دوام جزئي</span>
                  {% elif employee.employment_type == 'contract' %}
                  <span class="badge bg-warning">عقد</span>
                  {% elif employee.employment_type == 'intern' %}
                  <span class="badge bg-secondary">متدرب</span>
                  {% else %}
                  <span class="badge bg-light text-dark"
                >{{ employee.employment_type or 'غير محدد' }}</span>
                  {% endif %}
                </td></tr></table></div><div class="col-md-6"><table class="table table-borderless"><tr><td><strong>حالة التوظيف:</strong></td><td>
                  {% if employee.employment_status == 'active' %}
                  <span class="badge bg-success">نشط</span>
                  {% elif employee.employment_status == 'inactive' %}
                  <span class="badge bg-warning">غير نشط</span>
                  {% elif employee.employment_status == 'terminated' %}
                  <span class="badge bg-danger">منتهي الخدمة</span>
                  {% elif employee.employment_status == 'resigned' %}
                  <span class="badge bg-secondary">مستقيل</span>
                  {% else %}
                  <span class="badge bg-light text-dark"
                >{{ employee.employment_status or 'غير محدد' }}</span>
                  {% endif %}
                </td></tr><tr><td><strong>تاريخ التوظيف:</strong></td><td>
                  {{ employee.hire_date.hire_date else 'غير محدد'|strftime("%Y-%m-%d") if employee }}
                </td></tr>
              {% if employee.termination_date %}
              <tr><td><strong>تاريخ إنهاء الخدمة:</strong></td><td>{{ employee.termination_date.strftime("%Y-%m-%d") if employee.termination_date else "-" }}</td></tr>
              {% endif %}
              <tr><td><strong>تاريخ الإنشاء:</strong></td><td>
                  {{ employee.created_at.created_at else 'غير محدد'|strftime("%Y-%m-%d %H:%M") if employee }}
                </td></tr></table></div></div></div></div><!-- البيانات المالية -->
    {% if employee.basic_salary or employee.allowances %}
    <div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>
          البيانات المالية
        </h5></div><div class="card-body"><div class="row"><div class="col-md-6"><table class="table table-borderless"><tr><td><strong>الراتب الأساسي:</strong></td><td>
                  {{ "{:,.2f}".format(employee.basic_salary if
                  employee.basic_salary else '0.00' ) }} ريال
                </td></tr><tr><td><strong>البدلات:</strong></td><td>
                  {{ "{:,.2f}".format(employee.allowances if
                  employee.allowances else '0.00' ) }} ريال
                </td></tr><tr><td><strong>الخصومات:</strong></td><td class="text-danger">
                  {{ "{:,.2f}".format(employee.deductions if
                  employee.deductions else '0.00' ) }} ريال
                </td></tr><tr><td><strong>إجمالي الراتب:</strong></td><td><strong
                >{{ "{:,.2f}".format(employee.total_salary if
                    employee.total_salary else '0.00' ) }} ريال</strong></td></tr></table></div><div class="col-md-6"><table class="table table-borderless"><tr><td><strong>رقم الحساب البنكي:</strong></td><td>{{ employee.bank_account or 'غير محدد' }}</td></tr><tr><td><strong>اسم البنك:</strong></td><td>{{ employee.bank_name or 'غير محدد' }}</td></tr></table></div></div></div></div>
    {% endif %}

    <!-- تبويبات تفاصيل الموظف --><div class="card mb-4"><div class="card-header"><ul
          class="nav nav-tabs card-header-tabs"
          id="employeeDetailsTabs"
          role="tablist"
    ><li class="nav-item" role="presentation"><button
              class="nav-link active"
              id="attendance-tab"
              data-bs-toggle="tab"
              data-bs-target="#attendance"
              type="button"
              role="tab"
        ><i class="fas fa-clock me-1"></i>
              الحضور
            </button></li><li class="nav-item" role="presentation"><button
              class="nav-link"
              id="leaves-tab"
              data-bs-toggle="tab"
              data-bs-target="#leaves"
              type="button"
              role="tab"
        ><i class="fas fa-calendar-alt me-1"></i>
              الإجازات
            </button></li><li class="nav-item" role="presentation"><button
              class="nav-link"
              id="warnings-tab"
              data-bs-toggle="tab"
              data-bs-target="#warnings"
              type="button"
              role="tab"
        ><i class="fas fa-exclamation-triangle me-1"></i>
              الإنذارات
            </button></li><li class="nav-item" role="presentation"><button
              class="nav-link"
              id="penalties-tab"
              data-bs-toggle="tab"
              data-bs-target="#penalties"
              type="button"
              role="tab"
        ><i class="fas fa-gavel me-1"></i>
              الجزاءات
            </button></li></ul></div><div class="card-body"><div class="tab-content" id="employeeDetailsTabContent"><!-- تبويب الحضور --><div
            class="tab-pane fade show active"
            id="attendance"
            role="tabpanel"
      ><div class="d-flex justify-content-between align-items-center mb-3"><h6 class="mb-0">آخر سجلات الحضور</h6><a
                href="{{ url_for ('attendance.index', employee_id=employee.id }}"
                class="btn btn-sm btn-outline-primary"
          >
                عرض الكل
              </a></div>
            {% if employee.attendances %}
            <div class="table-responsive"><table class="table table-sm"><thead><tr><th>التاريخ</th><th>الحضور</th><th>الانصراف</th><th>الساعات</th><th>الحالة</th></tr></thead><tbody>
                 ) {% for attendance in employee.attendances|truncate(5) %}
                  <tr><td>{{ attendance.date.strftime("%Y-%m-%d") if attendance.date else "-" }}</td><td>
                      {{ attendance.check_in.check_in else '-'|strftime("%H:%M") if attendance }}
                    </td><td>
                      {{ attendance.check_out.check_out else '-'|strftime("%H:%M") if attendance }}
                    </td><td>
                      {{ "%.2f".format(attendance.hours_worked) if attendance.hours_worked else '-' }}
                    </td><td>
                      {% if attendance.status == 'present' %}
                      <span class="badge bg-success">حاضر</span>
                      {% elif attendance.status == 'absent' %}
                      <span class="badge bg-danger">غائب</span>
                      {% elif attendance.status == 'late' %}
                      <span class="badge bg-warning">متأخر</span>
                      {% else %}
                      <span class="badge bg-secondary"
                    >{{ attendance.status }}</span>
                      {% endif %}
                    </td></tr>
                  {% endfor %}
                </tbody></table></div>
            {% else %}
            <div class="text-center text-muted py-4"><i class="fas fa-clock fa-2x mb-2"></i><p>لا توجد سجلات حضور</p></div>
            {% endif %}
          </div><!-- تبويب الإجازات --><div class="tab-pane fade" id="leaves" role="tabpanel"><div class="d-flex justify-content-between align-items-center mb-3"><h6 class="mb-0">آخر طلبات الإجازة</h6><a
                href="{{ url_for ('leaves.index', employee_id=employee.id }}"
                class="btn btn-sm btn-outline-primary"
          >
                عرض الكل
              </a></div>
            {% if employee.leave_requests %}
            <div class="table-responsive"><table class="table table-sm"><thead><tr><th>النوع</th><th>من</th><th>إلى</th><th>الأيام</th><th>الحالة</th></tr></thead><tbody>
                 ) {% for leave in employee.leave_requests|truncate(5) %}
                  <tr><td>{{ leave.leave_type }}</td><td>{{ leave.start_date.strftime("%Y-%m-%d") if leave.start_date else "-" }}</td><td>{{ leave.end_date.strftime("%Y-%m-%d") if leave.end_date else "-" }}</td><td>{{ leave.days_count }}</td><td>
                      {% if leave.status == 'approved' %}
                      <span class="badge bg-success">موافق عليها</span>
                      {% elif leave.status == 'pending' %}
                      <span class="badge bg-warning">في الانتظار</span>
                      {% elif leave.status == 'rejected' %}
                      <span class="badge bg-danger">مرفوضة</span>
                      {% endif %}
                    </td></tr>
                  {% endfor %}
                </tbody></table></div>
            {% else %}
            <div class="text-center text-muted py-4"><i class="fas fa-calendar-alt fa-2x mb-2"></i><p>لا توجد طلبات إجازة</p></div>
            {% endif %}
          </div><!-- تبويب الإنذارات --><div class="tab-pane fade" id="warnings" role="tabpanel"><div class="d-flex justify-content-between align-items-center mb-3"><h6 class="mb-0">آخر الإنذارات</h6><a
                href="{{ url_for ('warnings.index', employee_id=employee.id }}"
                class="btn btn-sm btn-outline-primary"
          >
                عرض الكل
              </a></div>
            {% if employee.warnings %}
            <div class="table-responsive"><table class="table table-sm"><thead><tr><th>النوع</th><th>العنوان</th><th>التاريخ</th><th>الخطورة</th><th>الحالة</th></tr></thead><tbody>
                 ) {% for warning in employee.warnings|truncate(5) %}
                  <tr><td>{{ warning.warning_type }}</td><td>{{ warning.title }}</td><td>{{ warning.incident_date.strftime("%Y-%m-%d") if warning.incident_date else "-" }}</td><td>
                      {% if warning.severity == 'critical' %}
                      <span class="badge bg-danger">حرج</span>
                      {% elif warning.severity == 'high' %}
                      <span class="badge bg-warning">عالي</span>
                      {% elif warning.severity == 'medium' %}
                      <span class="badge bg-info">متوسط</span>
                      {% else %}
                      <span class="badge bg-success">منخفض</span>
                      {% endif %}
                    </td><td>
                      {% if warning.status == 'active' %}
                      <span class="badge bg-warning">نشط</span>
                      {% elif warning.status == 'resolved' %}
                      <span class="badge bg-success">محلول</span>
                      {% else %}
                      <span class="badge bg-secondary"
                    >{{ warning.status }}</span>
                      {% endif %}
                    </td></tr>
                  {% endfor %}
                </tbody></table></div>
            {% else %}
            <div class="text-center text-muted py-4"><i class="fas fa-exclamation-triangle fa-2x mb-2"></i><p>لا توجد إنذارات</p></div>
            {% endif %}
          </div><!-- تبويب الجزاءات --><div class="tab-pane fade" id="penalties" role="tabpanel"><div class="d-flex justify-content-between align-items-center mb-3"><h6 class="mb-0">آخر الجزاءات</h6><a
                href="{{ url_for ('penalties.index', employee_id=employee.id }}"
                class="btn btn-sm btn-outline-primary"
          >
                عرض الكل
              </a></div>
            {% if employee.penalties %}
            <div class="table-responsive"><table class="table table-sm"><thead><tr><th>النوع</th><th>العنوان</th><th>التاريخ</th><th>المبلغ/الأيام</th><th>الحالة</th></tr></thead><tbody>
                 ) {% for penalty in employee.penalties|truncate(5) %}
                  <tr><td>{{ penalty.penalty_type }}</td><td>{{ penalty.title }}</td><td>{{ penalty.penalty_date.strftime("%Y-%m-%d") if penalty.penalty_date else "-" }}</td><td>
                      {% if penalty.penalty_type == 'financial' and penalty.amount %} {{ "{:,.2f}".format(penalty.amount ) }}
                      ريال {% elif penalty.penalty_type == 'suspension' and penalty.days_count %} {{ penalty.days_count }} يوم {% else %} - {% endif %}
                    </td><td>
                      {% if penalty.status == 'active' %}
                      <span class="badge bg-warning">نشط</span>
                      {% elif penalty.status == 'completed' %}
                      <span class="badge bg-success">مكتمل</span>
                      {% elif penalty.status == 'cancelled' %}
                      <span class="badge bg-secondary">ملغي</span>
                      {% endif %}
                    </td></tr>
                  {% endfor %}
                </tbody></table></div>
            {% else %}
            <div class="text-center text-muted py-4"><i class="fas fa-gavel fa-2x mb-2"></i><p>لا توجد جزاءات</p></div>
            {% endif %}
          </div></div></div></div><!-- سجلات الحضور الأخيرة -->
    {% if recent_attendance %}
    <div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-clock me-2"></i>
          سجلات الحضور الأخيرة
        </h5></div><div class="card-body"><div class="table-responsive"><table class="table table-sm"><thead><tr><th>التاريخ</th><th>وقت الدخول</th><th>وقت الخروج</th><th>إجمالي الساعات</th><th>الحالة</th></tr></thead><tbody>
              {% for attendance in recent_attendance %}
              <tr><td>
                  {{ attendance.date.date else 'غير محدد'|strftime("%Y-%m-%d") if attendance }}
                </td><td>
                  {{ attendance.check_in_time.check_in_time else '-'|strftime("%H:%M") if attendance }}
                </td><td>
                  {{ attendance.check_out_time.check_out_time else '-'|strftime("%H:%M") if attendance }}
                </td><td>{{ attendance.total_hours or '-' }}</td><td>
                  {% if attendance.status == 'present' %}
                  <span class="badge bg-success">حاضر</span>
                  {% elif attendance.status == 'absent' %}
                  <span class="badge bg-danger">غائب</span>
                  {% elif attendance.status == 'late' %}
                  <span class="badge bg-warning">متأخر</span>
                  {% else %}
                  <span class="badge bg-secondary"
                >{{ attendance.status or 'غير محدد' }}</span>
                  {% endif %}
                </td></tr>
              {% endfor %}
            </tbody></table></div></div></div>
    {% endif %}
  </div><!-- الإحصائيات والمعلومات الجانبية --><div class="col-md-4"><!-- إحصائيات سريعة --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>
          إحصائيات الموظف
        </h5></div><div class="card-body"><div class="row text-center"><div class="col-6 mb-3"><div class="border rounded p-2"><h4 class="text-primary">{{ stats.attendance_count }}</h4><small class="text-muted">سجلات الحضور</small></div></div><div class="col-6 mb-3"><div class="border rounded p-2"><h4 class="text-warning">{{ stats.penalties_count }}</h4><small class="text-muted">الجزاءات</small></div></div><div class="col-6 mb-3"><div class="border rounded p-2"><h4 class="text-danger">{{ stats.warnings_count }}</h4><small class="text-muted">الإنذارات</small></div></div><div class="col-6 mb-3"><div class="border rounded p-2"><h4 class="text-info">{{ stats.documents_count }}</h4><small class="text-muted">المستندات</small></div></div></div></div></div><!-- إحصائيات الحضور --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-clock me-2"></i>
          إحصائيات الحضور
        </h5></div><div class="card-body"><div class="row"><div class="col-6"><div class="text-center"><h4 class="text-success">
                {{ employee.attendances|selectattr('status', 'equalto', 'present')|list|length }}
              </h4><small class="text-muted">أيام الحضور</small></div></div><div class="col-6"><div class="text-center"><h4 class="text-danger">
                {{ employee.attendances|selectattr('status', 'equalto', 'absent')|list|length }}
              </h4><small class="text-muted">أيام الغياب</small></div></div></div><div class="row mt-3"><div class="col-6"><div class="text-center"><h4 class="text-warning">
                {{ employee.attendances|selectattr('status', 'equalto', 'late')|list|length }}
              </h4><small class="text-muted">أيام التأخير</small></div></div><div class="col-6"><div class="text-center"><h4 class="text-info">
                {{ employee.attendances|map(attribute='hours_worked')|select|sum|round(1) or 0 }}
              </h4><small class="text-muted">إجمالي الساعات</small></div></div></div><div class="mt-3"><a
            href="{{ url_for ('attendance.index', employee_id=employee.id }}"
            class="btn btn-primary btn-sm"
      ><i class="fas fa-eye me-1"></i>
            عرض سجل الحضور
          </a><a
            href="{{ url_for('attendance_add' }}?employee_id={{ employee.id }}"
            class="btn btn-success btn-sm"
      ><i class="fas fa-plus me-1"></i>
            تسجيل حضور
          </a></div></div></div><!-- إحصائيات الإجازات --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>
          إحصائيات الإجازات
        </h5></div><div class="card-body"><div class="row"><div class="col-6"><div class="text-center"><h4 class="text-primary">
               ) {{ employee.leave_requests|selectattr('status', 'equalto', 'approved')|list|length }}
              </h4><small class="text-muted">إجازات موافق عليها</small></div></div><div class="col-6"><div class="text-center"><h4 class="text-warning">
                {{ employee.leave_requests|selectattr('status', 'equalto', 'pending')|list|length }}
              </h4><small class="text-muted">إجازات في الانتظار</small></div></div></div><div class="row mt-3"><div class="col-6"><div class="text-center"><h4 class="text-danger">
                {{ employee.leave_requests|selectattr('status', 'equalto', 'rejected')|list|length }}
              </h4><small class="text-muted">إجازات مرفوضة</small></div></div><div class="col-6"><div class="text-center"><h4 class="text-info">
                {{ employee.leave_requests|selectattr('status', 'equalto', 'approved')|map(attribute='days_count')|sum or 0 }}
              </h4><small class="text-muted">إجمالي أيام الإجازة</small></div></div></div><div class="mt-3"><a
            href="{{ url_for ('leaves.index', employee_id=employee.id }}"
            class="btn btn-primary btn-sm"
      ><i class="fas fa-eye me-1"></i>
            عرض الإجازات
          </a><a
            href="{{ url_for('leaves_add' }}?employee_id={{ employee.id }}"
            class="btn btn-success btn-sm"
      ><i class="fas fa-plus me-1"></i>
            طلب إجازة
          </a></div></div></div><!-- إحصائيات الإنذارات --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>
          إحصائيات الإنذارات
        </h5></div><div class="card-body"><div class="row"><div class="col-6"><div class="text-center"><h4 class="text-warning">
               ) {{ employee.warnings|selectattr('status', 'equalto', 'active')|list|length }}
              </h4><small class="text-muted">إنذارات نشطة</small></div></div><div class="col-6"><div class="text-center"><h4 class="text-success">
                {{ employee.warnings|selectattr('status', 'equalto', 'resolved')|list|length }}
              </h4><small class="text-muted">إنذارات محلولة</small></div></div></div><div class="row mt-3"><div class="col-6"><div class="text-center"><h4 class="text-danger">
                {{ employee.warnings|selectattr('severity', 'equalto', 'critical')|list|length }}
              </h4><small class="text-muted">إنذارات حرجة</small></div></div><div class="col-6"><div class="text-center"><h4 class="text-info">{{ employee.warnings|length }}</h4><small class="text-muted">إجمالي الإنذارات</small></div></div></div><div class="mt-3"><a
            href="{{ url_for ('warnings.index', employee_id=employee.id }}"
            class="btn btn-primary btn-sm"
      ><i class="fas fa-eye me-1"></i>
            عرض الإنذارات
          </a><a
            href="{{ url_for('warnings_add' }}?employee_id={{ employee.id }}"
            class="btn btn-warning btn-sm"
      ><i class="fas fa-plus me-1"></i>
            إضافة إنذار
          </a></div></div></div><!-- إحصائيات الجزاءات --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-gavel me-2"></i>
          إحصائيات الجزاءات
        </h5></div><div class="card-body"><div class="row"><div class="col-6"><div class="text-center"><h4 class="text-warning">
               ) {{ employee.penalties|selectattr('status', 'equalto', 'active')|list|length }}
              </h4><small class="text-muted">جزاءات نشطة</small></div></div><div class="col-6"><div class="text-center"><h4 class="text-success">
                {{ employee.penalties|selectattr('status', 'equalto', 'completed')|list|length }}
              </h4><small class="text-muted">جزاءات مكتملة</small></div></div></div><div class="row mt-3"><div class="col-6"><div class="text-center"><h4 class="text-danger">
                {{ employee.penalties|selectattr('penalty_type', 'equalto', 'financial')|map(attribute='amount')|select|sum|round(2) or 0 }}
              </h4><small class="text-muted">إجمالي الجزاءات المالية</small></div></div><div class="col-6"><div class="text-center"><h4 class="text-info">{{ employee.penalties|length }}</h4><small class="text-muted">إجمالي الجزاءات</small></div></div></div><div class="mt-3"><a
            href="{{ url_for('penalties.index', employee_id=employee.id }}"
            class="btn btn-primary btn-sm"
      ><i class="fas fa-eye me-1"></i>
            عرض الجزاءات
          </a><a
            href="{{ url_for('penalties_add' }}?employee_id={{ employee.id }}"
            class="btn btn-danger btn-sm"
      ><i class="fas fa-plus me-1"></i>
            إضافة جزاء
          </a></div></div></div><!-- إجراءات سريعة --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-tools me-2"></i>
          إجراءات سريعة
        </h5></div><div class="card-body"><div class="d-grid gap-2"><a
            href="{{ url_for('attendance_add' }}?employee_id={{ employee.id }}"
            class="btn btn-outline-primary"
      ><i class="fas fa-clock me-1"></i>
            تسجيل حضور
          </a><a
            href="{{ url_for('leaves_add' }}?employee_id={{ employee.id }}"
            class="btn btn-outline-info"
      ><i class="fas fa-calendar-alt me-1"></i>
            طلب إجازة
          </a><a
            href="{{ url_for('warnings_add' }}?employee_id={{ employee.id }}"
            class="btn btn-outline-warning"
      ><i class="fas fa-exclamation-triangle me-1"></i>
            إضافة إنذار
          </a><a
            href="{{ url_for('penalties_add' }}?employee_id={{ employee.id }}"
            class="btn btn-outline-danger"
      ><i class="fas fa-gavel me-1"></i>
            إضافة جزاء
          </a><a
            href="{{ url_for('employees.documents', id=employee.id }}"
            class="btn btn-outline-success"
      ><i class="fas fa-folder-open me-1"></i>
            إدارة المستندات
          </a></div></div></div><!-- معلومات النظام --><div class="card"><div class="card-header"><h5 class="mb-0"><i class="fas fa-info me-2"></i>
          معلومات النظام
        </h5></div><div class="card-body"><small class="text-muted"><p><strong>تاريخ الإنشاء:</strong><br />{{ employee.created_at.created_at else 'غير محدد'|strftime("%Y-%m-%d %H:%M:%S") if employee }}
          </p>
          {% if employee.updated_at %}
          <p><strong>آخر تحديث:</strong><br />{{ employee.updated_at.strftime("%Y-%m-%d %H:%M:%S") if employee.updated_at else "-" }}
          </p>
          {% endif %} {% if employee.creator %}
          <p><strong>أنشئ بواسطة:</strong><br />{{ employee.creator.full_name }}
          </p>
          {% endif %}
        </small></div></div></div></div><!-- التوقيعات للطباعة -->
{% include 'components/print_signatures.html' with context %}

<script>
  function printEmployee() {
    window.print();
  }
</script><style>
  @media print {
    .btn-toolbar,
    .no-print {
      display: none !important;
    }

    .card {
      border: 1px solid #ddd !important;
      box-shadow: none !important;
      break-inside: avoid;
    }

    .card-header {
      background-color: #f8f9fa !important;
      color: #000 !important;
    }
  }
</style>
{% endblock %}
