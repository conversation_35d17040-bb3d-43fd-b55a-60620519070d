#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أخطاء بناء الجملة في dashboard.html
"""

import re

def fix_dashboard_syntax():
    """إصلاح أخطاء بناء الجملة في dashboard.html"""
    
    print("🔧 إصلاح أخطاء بناء الجملة في dashboard.html...")
    
    try:
        with open('templates/dashboard.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes = 0
        
        # إصلاح الأسطر المقسمة خطأ
        patterns = [
            # إصلاح السطر المقسم للمحامي
            (r'\{\{\s*case\.client\.full_name\s*\}\}\s*\{\%\s*if\s+case\.lawyer\s*\%\}\s*•\s*<i[^>]*></i>\{\{\s*case\.lawyer\.user\.full_name\s*\}\}\s*\{\%\s*endif\s*\%\}',
             r'{{ case.client.full_name }}{% if case.lawyer %} • <i class="fas fa-user-tie me-1"></i>{{ case.lawyer.user.full_name }}{% endif %}'),
            
            # إصلاح السطر المقسم للتاريخ
            (r'\{\{\s*case\.created_at\.strftime\([^}]+\)\s+if\s+case\.created_at\s+else\s*"-"\s*\}\}',
             r'{{ case.created_at.strftime("%Y-%m-%d") if case.created_at else "-" }}'),
            
            # إصلاح الأسطر المقسمة عموماً
            (r'\{\{\s*([^}]+)\s+if\s+([^}]+)\s+else\s*([^}]+)\s*\}\}',
             r'{{ \1 if \2 else \3 }}'),
            
            # إصلاح الأقواس المقسمة
            (r'\{\{\s*([^}]+)\s*\}\}\s*\{\%',
             r'{{ \1 }} {%'),
            
            # إصلاح علامات Jinja المقسمة
            (r'\{\%\s*([^%]+)\s*\%\}\s*•\s*<i([^>]*)>\s*</i>\s*\{\{\s*([^}]+)\s*\}\}\s*\{\%\s*([^%]+)\s*\%\}',
             r'{% \1 %} • <i\2></i>{{ \3 }}{% \4 %}'),
        ]
        
        for pattern, replacement in patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            if matches:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
                fixes += len(matches)
        
        # إصلاحات محددة للمشاكل المعروفة
        specific_fixes = [
            # إصلاح السطر 325-327
            ('{{ case.client.full_name }} {%\n                if case.lawyer %} • <i class="fas fa-user-tie me-1"></i>{{\n                case.lawyer.user.full_name }}{% endif %}',
             '{{ case.client.full_name }}{% if case.lawyer %} • <i class="fas fa-user-tie me-1"></i>{{ case.lawyer.user.full_name }}{% endif %}'),
            
            # إصلاح السطر 332-333
            ('{{ case.created_at.strftime("%Y-%m-%d") if case.created_at else\n                "-" }}',
             '{{ case.created_at.strftime("%Y-%m-%d") if case.created_at else "-" }}'),
        ]
        
        for old, new in specific_fixes:
            if old in content:
                content = content.replace(old, new)
                fixes += 1
        
        # حفظ الملف إذا تم إجراء إصلاحات
        if content != original_content:
            with open('templates/dashboard.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ تم إصلاح {fixes} خطأ في dashboard.html")
            return True
        else:
            print("ℹ️ لا توجد أخطاء للإصلاح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح dashboard.html: {e}")
        return False

def validate_dashboard():
    """التحقق من صحة بناء الجملة"""
    
    print("\n🔍 التحقق من صحة بناء الجملة...")
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        template = env.get_template('dashboard.html')
        
        print("✅ بناء جملة dashboard.html صحيح!")
        return True
        
    except TemplateSyntaxError as e:
        print(f"❌ خطأ في بناء الجملة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إصلاح dashboard.html...")
    
    # إصلاح الأخطاء
    fixed = fix_dashboard_syntax()
    
    # التحقق من النتيجة
    is_valid = validate_dashboard()
    
    if is_valid:
        print("\n🎉 تم إصلاح dashboard.html بنجاح!")
        return True
    else:
        print("\n⚠️ لا تزال هناك أخطاء في dashboard.html")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
