{% extends "base.html" %} {% block title %}إضافة عميل جديد - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
><h1 class="h2"><i class="fas fa-user-plus me-2"></i>
    إضافة عميل جديد
  </h1><div class="btn-toolbar mb-2 mb-md-0"><a href="{{ url_for('clients.index' }}" class="btn btn-outline-secondary"><i class="fas fa-arrow-right me-1"></i>
      العودة للقائمة
    </a></div></div><form method="POST" id="clientForm"><div class="row"><div class="col-md-8"><!-- معلومات العميل الأساسية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-user me-2"></i>
            معلومات العميل الأساسية
          </h5></div><div class="card-body"><div class="row"><div class="col-md-6"><div class="mb-3"><label for="full_name" class="form-label"
              >الاسم الكامل <span class="text-danger">*</span></label><input
                  type="text"
                  class="form-control"
                  id="full_name"
                  name="full_name"
                  required
                /><div class="form-text">
                  أدخل الاسم الكامل للعميل أو اسم الشركة
                </div></div></div><div class="col-md-6"><div class="mb-3"><label for="client_type" class="form-label"
              >نوع العميل <span class="text-danger">*</span></label><select
                  class="form-select"
                  id="client_type"
                  name="client_type"
                  required
            ><option value="individual" selected>فرد</option><option value="company">شركة</option><option value="organization">مؤسسة</option></select></div></div></div><div class="row"><div class="col-md-6"><div class="mb-3"><label for="email" class="form-label">البريد الإلكتروني</label><input
                  type="email"
                  class="form-control"
                  id="email"
                  name="email"
                /><div class="form-text">
                  سيتم استخدامه للتواصل وإرسال الإشعارات
                </div></div></div><div class="col-md-6"><div class="mb-3"><label for="phone" class="form-label">رقم الهاتف</label><input
                  type="tel"
                  class="form-control"
                  id="phone"
                  name="phone"
                  placeholder="+966501234567"
                /><div class="form-text">يفضل تضمين رمز الدولة</div></div></div></div><div class="row"><div class="col-md-6"><div class="mb-3"><label for="national_id" class="form-label"
              >رقم الهوية/السجل التجاري</label><input
                  type="text"
                  class="form-control"
                  id="national_id"
                  name="national_id"
                /><div class="form-text">رقم الهوية الوطنية أو السجل التجاري</div></div></div><div class="col-md-6"><div class="mb-3"><label class="form-label">رمز العميل</label><input
                  type="text"
                  class="form-control"
                  value="سيتم إنشاؤه تلقائياً"
                  readonly
                /><div class="form-text">سيتم إنشاء رمز فريد للعميل تلقائياً</div></div></div></div><div class="mb-3"><label for="address" class="form-label">العنوان</label><textarea
              class="form-control"
              id="address"
              name="address"
              rows="3"
              placeholder="أدخل العنوان الكامل للعميل"
        ></textarea></div><div class="mb-3"><label for="notes" class="form-label">ملاحظات</label><textarea
              class="form-control"
              id="notes"
              name="notes"
              rows="3"
              placeholder="أي ملاحظات إضافية حول العميل"
        ></textarea></div></div></div></div><div class="col-md-4"><!-- معلومات إضافية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>
            معلومات إضافية
          </h5></div><div class="card-body"><div class="alert alert-info"><h6 class="alert-heading"><i class="fas fa-lightbulb me-1"></i>
              نصائح مهمة:
            </h6><ul class="mb-0"><li>تأكد من صحة البريد الإلكتروني</li><li>رقم الهاتف مهم للتواصل السريع</li><li>رقم الهوية يجب أن يكون فريد</li><li>العنوان مفيد للمراسلات الرسمية</li><li>الملاحظات تساعد في التذكير</li></ul></div><div class="alert alert-warning"><h6 class="alert-heading"><i class="fas fa-exclamation-triangle me-1"></i>
              تنبيه:
            </h6><p class="mb-0">
              تأكد من دقة البيانات المدخلة حيث سيتم استخدامها في جميع المعاملات
              والمستندات القانونية.
            </p></div></div></div><!-- أزرار الحفظ --><div class="card"><div class="card-body"><div class="d-grid gap-2"><button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i>
              حفظ العميل
            </button><a
              href="{{ url_for('clients.index' }}"
              class="btn btn-outline-secondary"
        ><i class="fas fa-times me-1"></i>
              إلغاء
            </a></div></div></div></div></div></form><script>
  // التحقق من صحة النموذج
  document
    .getElementById("clientForm")
    .addEventListener("submit", function (e) {
      const fullName = document.getElementById("full_name").value.trim();

      if (!fullName) {
        e.preventDefault();
        alert("يرجى إدخال اسم العميل");
        document.getElementById("full_name").focus();
        return false;
      }
    });

  // تحديث النصوص حسب نوع العميل
  document
    .getElementById("client_type")
    .addEventListener("change", function () {
      const clientType = this.value;
      const nationalIdLabel = document.querySelector(
        'label[for="national_id"]'
      );
      const fullNameLabel = document.querySelector('label[for="full_name"]');

      if (clientType === "individual") {
        nationalIdLabel.textContent = "رقم الهوية الوطنية";
        fullNameLabel.textContent = "الاسم الكامل";
      } else if (clientType === "company") {
        nationalIdLabel.textContent = "رقم السجل التجاري";
        fullNameLabel.textContent = "اسم الشركة";
      } else if (clientType === "organization") {
        nationalIdLabel.textContent = "رقم التسجيل";
        fullNameLabel.textContent = "اسم المؤسسة";
      }
    });
</script>
{% endblock %}
