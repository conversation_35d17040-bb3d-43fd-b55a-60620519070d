{% extends "base.html" %}

{% block title %}إضافة عقد جديد - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2"><i class="fas fa-plus me-2"></i>
        إضافة عقد جديد
    </h1><div class="btn-toolbar mb-2 mb-md-0"><a href="{{ url_for('contracts.index' }}" class="btn btn-outline-secondary"><i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a></div></div><form method="POST" enctype="multipart/form-data" id="contractForm"><div class="row"><!-- العمود الأيسر --><div class="col-md-8"><!-- معلومات العقد الأساسية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>
                        معلومات العقد الأساسية
                    </h5></div><div class="card-body"><div class="row"><div class="col-md-6"><div class="mb-3"><label for="title" class="form-label">عنوان العقد <span class="text-danger">*</span></label><input type="text" class="form-control" id="title" name="title" required></div></div><div class="col-md-6"><div class="mb-3"><label for="title_en" class="form-label">عنوان العقد (بالإنجليزية)</label><input type="text" class="form-control" id="title_en" name="title_en"></div></div></div><div class="row"><div class="col-md-6"><div class="mb-3"><label for="contract_type" class="form-label">نوع العقد <span class="text-danger">*</span></label><select class="form-select" id="contract_type" name="contract_type" required><option value="">اختر نوع العقد</option><option value="استشارة قانونية">استشارة قانونية</option><option value="توكيل قضائي">توكيل قضائي</option><option value="صياغة عقود">صياغة عقود</option><option value="تمثيل قانوني">تمثيل قانوني</option><option value="خدمات قانونية شاملة">خدمات قانونية شاملة</option><option value="أخرى">أخرى</option></select></div></div><div class="col-md-6"><div class="mb-3"><label for="contract_type_en" class="form-label">نوع العقد (بالإنجليزية)</label><input type="text" class="form-control" id="contract_type_en" name="contract_type_en"></div></div></div><div class="row"><div class="col-md-6"><div class="mb-3"><label for="client_id" class="form-label">العميل <span class="text-danger">*</span></label><select class="form-select" id="client_id" name="client_id" required><option value="">اختر العميل</option>
                                    {% for client in clients %}
                                    <option value="{{ client.id }}">{{ client.full_name }} - {{ client.email }}</option>
                                    {% endfor %}
                                </select></div></div><div class="col-md-6"><div class="mb-3"><label for="assigned_lawyer_id" class="form-label">المحامي المسؤول</label><select class="form-select" id="assigned_lawyer_id" name="assigned_lawyer_id"><option value="">اختر المحامي</option>
                                    {% for lawyer in lawyers %}
                                    <option value="{{ lawyer.id }}">{{ lawyer.user.full_name }} - {{ lawyer.specialization }}</option>
                                    {% endfor %}
                                </select></div></div></div><div class="mb-3"><label for="description" class="form-label">وصف العقد</label><textarea class="form-control" id="description" name="description" rows="3"></textarea></div><div class="mb-3"><label for="description_en" class="form-label">وصف العقد (بالإنجليزية)</label><textarea class="form-control" id="description_en" name="description_en" rows="3"></textarea></div></div></div><!-- بنود العقد --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-list-ul me-2"></i>
                        بنود العقد والشروط
                    </h5></div><div class="card-body"><div class="mb-3"><label for="terms_and_conditions" class="form-label">البنود والشروط</label><textarea class="form-control" id="terms_and_conditions" name="terms_and_conditions" rows="6" 
                                  placeholder="اكتب بنود العقد والشروط هنا..."></textarea></div><div class="mb-3"><label for="terms_and_conditions_en" class="form-label">البنود والشروط (بالإنجليزية)</label><textarea class="form-control" id="terms_and_conditions_en" name="terms_and_conditions_en" rows="6" 
                                  placeholder="Enter contract terms and conditions in English..."></textarea></div><div class="mb-3"><label for="payment_terms" class="form-label">شروط الدفع</label><textarea class="form-control" id="payment_terms" name="payment_terms" rows="3" 
                                  placeholder="حدد شروط وطريقة الدفع..."></textarea></div><div class="mb-3"><label for="payment_terms_en" class="form-label">شروط الدفع (بالإنجليزية)</label><textarea class="form-control" id="payment_terms_en" name="payment_terms_en" rows="3" 
                                  placeholder="Specify payment terms and method..."></textarea></div></div></div><!-- ملاحظات إضافية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-sticky-note me-2"></i>
                        ملاحظات إضافية
                    </h5></div><div class="card-body"><div class="mb-3"><label for="notes" class="form-label">ملاحظات</label><textarea class="form-control" id="notes" name="notes" rows="3"></textarea></div><div class="mb-3"><label for="notes_en" class="form-label">ملاحظات (بالإنجليزية)</label><textarea class="form-control" id="notes_en" name="notes_en" rows="3"></textarea></div><div class="mb-3"><label for="tags" class="form-label">علامات للبحث</label><input type="text" class="form-control" id="tags" name="tags" 
                               placeholder="علامات مفصولة بفواصل للمساعدة في البحث"><div class="form-text">مثال: عقد، استشارة، قانوني، تجاري</div></div></div></div></div><!-- العمود الأيمن --><div class="col-md-4"><!-- التواريخ والمدة --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-calendar me-2"></i>
                        التواريخ والمدة
                    </h5></div><div class="card-body"><div class="mb-3"><label for="start_date" class="form-label">تاريخ البداية <span class="text-danger">*</span></label><input type="date" class="form-control" id="start_date" name="start_date" required></div><div class="mb-3"><label for="end_date" class="form-label">تاريخ النهاية</label><input type="date" class="form-control" id="end_date" name="end_date"></div><div class="mb-3"><label for="duration_months" class="form-label">المدة (بالأشهر)</label><input type="number" class="form-control" id="duration_months" name="duration_months" min="1"><div class="form-text">سيتم حساب تاريخ النهاية تلقائياً</div></div></div></div><!-- المبالغ المالية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-money-bill me-2"></i>
                        المبالغ المالية
                    </h5></div><div class="card-body"><div class="mb-3"><label for="total_amount" class="form-label">إجمالي المبلغ <span class="text-danger">*</span></label><input type="number" class="form-control" id="total_amount" name="total_amount" 
                               step="0.01" min="0" required></div><div class="mb-3"><label for="currency" class="form-label">العملة</label><select class="form-select" id="currency" name="currency"><option value="SAR" selected>ريال سعودي (SAR)</option><option value="USD">دولار أمريكي (USD)</option><option value="EUR">يورو (EUR)</option><option value="AED">درهم إماراتي (AED)</option></select></div></div></div><!-- الحالة والأولوية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-cog me-2"></i>
                        الحالة والأولوية
                    </h5></div><div class="card-body"><div class="mb-3"><label for="status" class="form-label">حالة العقد</label><select class="form-select" id="status" name="status"><option value="draft" selected>مسودة</option><option value="active">نشط</option><option value="completed">مكتمل</option><option value="cancelled">ملغي</option></select></div><div class="mb-3"><label for="priority" class="form-label">الأولوية</label><select class="form-select" id="priority" name="priority"><option value="low">منخفضة</option><option value="medium" selected>متوسطة</option><option value="high">عالية</option></select></div></div></div><!-- رفع ملف PDF --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-file-pdf me-2"></i>
                        ملف العقد
                    </h5></div><div class="card-body"><div class="mb-3"><label for="pdf_file" class="form-label">رفع ملف PDF</label><input type="file" class="form-control" id="pdf_file" name="pdf_file" accept=".pdf"><div class="form-text">يجب أن يكون الملف من نوع PDF فقط</div></div></div></div><!-- أزرار الحفظ --><div class="card"><div class="card-body"><div class="d-grid gap-2"><button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i>
                            حفظ العقد
                        </button><a href="{{ url_for('contracts.index' }}" class="btn btn-outline-secondary"><i class="fas fa-times me-1"></i>
                            إلغاء
                        </a></div></div></div></div></div></form><script>
// حساب تاريخ النهاية تلقائياً عند تغيير المدة
document.getElementById('duration_months').addEventListener('change', function() {
    const startDate = document.getElementById('start_date').value;
    const durationMonths = parseInt(this.value);
    
    if (startDate && durationMonths) {
        const start = new Date(startDate);
        const end = new Date(start);
        end.setMonth(end.getMonth() + durationMonths);
        
        document.getElementById('end_date').value = end.toISOString().split('T')[0];
    }
});

// حساب المدة تلقائياً عند تغيير تاريخ النهاية
document.getElementById('end_date').addEventListener('change', function() {
    const startDate = document.getElementById('start_date').value;
    const endDate = this.value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        const diffTime = Math.abs(end - start);
        const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30);
        
        document.getElementById('duration_months').value = diffMonths;
    }
});

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('contractForm').addEventListener('submit', function(e) {
    const requiredFields = ['title', 'contract_type', 'client_id', 'start_date', 'total_amount'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim() {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
    }
});
</script>
{% endblock %}
