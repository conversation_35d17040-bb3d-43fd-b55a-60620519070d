{% extends "base.html" %}

{% block title %}الجزاءات{% endblock %}

{% block content %}
<div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-header"><h3 class="card-title"><i class="fas fa-gavel"></i>
            إدارة الجزاءات
          </h3><div class="card-tools"><a href="{{ url_for('penalties_add' }}" class="btn btn-primary btn-sm"><i class="fas fa-plus"></i>
              إضافة جزاء جديد
            </a><a href="{{ url_for('penalties_report' }}" class="btn btn-info btn-sm"><i class="fas fa-chart-bar"></i>
              تقرير الجزاءات
            </a></div></div><!-- إحصائيات سريعة --><div class="card-body"><div class="row mb-3"><div class="col-md-2"><div class="info-box bg-info"><span class="info-box-icon"><i class="fas fa-list"></i></span><div class="info-box-content"><span class="info-box-text">إجمالي الجزاءات</span><span class="info-box-number">{{ stats.total_penalties }}</span></div></div></div><div class="col-md-2"><div class="info-box bg-warning"><span class="info-box-icon"><i class="fas fa-exclamation"></i></span><div class="info-box-content"><span class="info-box-text">نشطة</span><span class="info-box-number">{{ stats.active_penalties }}</span></div></div></div><div class="col-md-2"><div class="info-box bg-success"><span class="info-box-icon"><i class="fas fa-check"></i></span><div class="info-box-content"><span class="info-box-text">مكتملة</span><span class="info-box-number">{{ stats.completed_penalties }}</span></div></div></div><div class="col-md-2"><div class="info-box bg-primary"><span class="info-box-icon"><i class="fas fa-money-bill"></i></span><div class="info-box-content"><span class="info-box-text">جزاءات مالية</span><span class="info-box-number">{{ stats.financial_penalties }}</span></div></div></div><div class="col-md-2"><div class="info-box bg-danger"><span class="info-box-icon"><i class="fas fa-dollar-sign"></i></span><div class="info-box-content"><span class="info-box-text">إجمالي المبالغ</span><span class="info-box-number">{{ "{:,.0f}".format(stats.total_amount ) }}</span></div></div></div><div class="col-md-2"><div class="info-box bg-secondary"><span class="info-box-icon"><i class="fas fa-calendar"></i></span><div class="info-box-content"><span class="info-box-text">هذا الشهر</span><span class="info-box-number">{{ stats.this_month }}</span></div></div></div></div><!-- فلاتر البحث --><div class="card card-outline card-primary collapsed-card"><div class="card-header"><h3 class="card-title"><i class="fas fa-filter"></i>
                فلاتر البحث
              </h3><div class="card-tools"><button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fas fa-plus"></i></button></div></div><div class="card-body"><form method="GET" action="{{ url_for ('penalties.index' }}"><div class="row"><div class="col-md-3"><div class="form-group"><label for="search">البحث</label><input type="text" class="form-control" id="search" name="search" 
                             value="{{ search }}" placeholder="اسم الموظف أو عنوان الجزاء..."></div></div><div class="col-md-3"><div class="form-group"><label for="employee_id">الموظف</label><select class="form-control" id="employee_id" name="employee_id"><option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" 
                                {% if employee_id == employee.id %}selected{% endif %}>
                          {{ employee.full_name }}
                        </option>
                        {% endfor %}
                      </select></div></div><div class="col-md-2"><div class="form-group"><label for="penalty_type">نوع الجزاء</label><select class="form-control" id="penalty_type" name="penalty_type"><option value="">جميع الأنواع</option>
                        {% for type_code, type_name in penalty_types %}
                        <option value="{{ type_code }}" {% if penalty_type == type_code %}selected{% endif %}>
                          {{ type_name }}
                        </option>
                        {% endfor %}
                      </select></div></div><div class="col-md-2"><div class="form-group"><label for="status">الحالة</label><select class="form-control" id="status" name="status"><option value="">جميع الحالات</option>
                        {% for status_code, status_name in statuses %}
                        <option value="{{ status_code }}" {% if status == status_code %}selected{% endif %}>
                          {{ status_name }}
                        </option>
                        {% endfor %}
                      </select></div></div><div class="col-md-2"><div class="form-group"><label for="payment_status">حالة الدفع</label><select class="form-control" id="payment_status" name="payment_status"><option value="">جميع الحالات</option>
                        {% for pay_status_code, pay_status_name in payment_statuses %}
                        <option value="{{ pay_status_code }}" {% if payment_status == pay_status_code %}selected{% endif %}>
                          {{ pay_status_name }}
                        </option>
                        {% endfor %}
                      </select></div></div></div><div class="row"><div class="col-12"><button type="submit" class="btn btn-primary"><i class="fas fa-search"></i>
                      بحث
                    </button><a href="{{ url_for('penalties.index' }}" class="btn btn-secondary"><i class="fas fa-undo"></i>
                      إعادة تعيين
                    </a></div></div></form></div></div><!-- جدول الجزاءات --><div class="table-responsive"><table class="table table-bordered table-striped"><thead><tr><th>الموظف</th><th>نوع الجزاء</th><th>العنوان</th><th>تاريخ الجزاء</th><th>المبلغ/الأيام</th><th>الخطورة</th><th>الحالة</th><th>حالة الدفع</th><th>الإجراءات</th></tr></thead><tbody>
                {% for penalty in penalties %}
                <tr><td><strong>{{ penalty.employee.full_name }}</strong><br><small class="text-muted">{{ penalty.employee.employee_number }}</small></td><td>
                    {% for type_code, type_name in penalty_types %}
                      {% if penalty.penalty_type == type_code %}
                        <span class="badge badge-info">{{ type_name }}</span>
                        
                      {% endif %}
                   ) {% endfor %}
                  </td><td><strong>{{ penalty.title }}</strong><br><small class="text-muted">{{ penalty.description|truncate(50) }}{% if penalty.description|length > 50 %}...{% endif %}</small></td><td>{{ penalty.penalty_date.strftime("%Y-%m-%d") if penalty.penalty_date else "-" }}</td><td>
                    {% if penalty.penalty_type == 'financial' and penalty.amount %}
                      <strong class="text-danger">{{ "{:,.2f}".format(penalty.amount ) }} ريال</strong>
                    {% elif penalty.penalty_type == 'suspension' and penalty.days_count %}
                      <strong class="text-warning">{{ penalty.days_count }} يوم</strong>
                    {% else %}
                      <span class="text-muted">-</span>
                    {% endif %}
                  </td><td>
                    {% if penalty.severity == 'low' %}
                      <span class="badge badge-success">منخفض</span>
                    {% elif penalty.severity == 'medium' %}
                      <span class="badge badge-warning">متوسط</span>
                    {% elif penalty.severity == 'high' %}
                      <span class="badge badge-danger">عالي</span>
                    {% elif penalty.severity == 'critical' %}
                      <span class="badge badge-dark">حرج</span>
                    {% endif %}
                  </td><td>
                    {% if penalty.status == 'active' %}
                      <span class="badge badge-warning">نشط</span>
                    {% elif penalty.status == 'completed' %}
                      <span class="badge badge-success">مكتمل</span>
                    {% elif penalty.status == 'cancelled' %}
                      <span class="badge badge-secondary">ملغي</span>
                    {% endif %}
                  </td><td>
                    {% if penalty.penalty_type == 'financial' %}
                      {% if penalty.payment_status == 'pending' %}
                        <span class="badge badge-warning">في الانتظار</span>
                      {% elif penalty.payment_status == 'paid' %}
                        <span class="badge badge-success">مدفوع</span>
                      {% elif penalty.payment_status == 'waived' %}
                        <span class="badge badge-info">معفى</span>
                      {% endif %}
                    {% else %}
                      <span class="text-muted">-</span>
                    {% endif %}
                  </td><td><div class="btn-group btn-group-sm">
                      {% if penalty.penalty_type == 'financial' and penalty.payment_status == 'pending' %}
                        <button type="button" class="btn btn-success btn-sm"
                                onclick="markPaid('{{ penalty.id }}')" title="تسديد"><i class="fas fa-dollar-sign"></i></button>
                      {% endif %}
                      <a href="{{ url_for('penalties.edit', penalty_id=penalty.id }}" 
                         class="btn btn-warning btn-sm" title="تعديل"><i class="fas fa-edit"></i></a><button type="button" class="btn btn-info btn-sm"
                              onclick="viewPenalty('{{ penalty.id }}')" title="عرض"><i class="fas fa-eye"></i></button><button type="button" class="btn btn-danger btn-sm"
                              onclick="deletePenalty('{{ penalty.id }}')" title="حذف"><i class="fas fa-trash"></i></button></div></td></tr>
                {% else %}
                <tr><td colspan="9" class="text-center text-muted"><i class="fas fa-inbox fa-2x mb-2"></i><br>
                    لا توجد جزاءات
                  </td></tr>
                {% endfor %}
              </tbody></table></div></div></div></div></div></div><!-- نافذة عرض تفاصيل الجزاء --><div class="modal fade" id="viewPenaltyModal" tabindex="-1"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h4 class="modal-title">تفاصيل الجزاء</h4><button type="button" class="close" data-dismiss="modal"><span>&times;</span></button></div><div class="modal-body" id="penaltyDetails"><!-- سيتم تحميل التفاصيل هنا --></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button></div></div></div></div><script>
// عرض تفاصيل الجزاء
function viewPenalty(id) {
  // هنا يمكن إضافة استدعاء AJAX لجلب تفاصيل الجزاء
  $('#viewPenaltyModal').modal('show');
}

// تسديد جزاء مالي
function markPaid(id) {
  if (confirm('هل أنت متأكد من تسديد هذا الجزاء المالي؟') {
    fetch(`/penalties/${id}/mark-paid`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json()
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('خطأ: ' + data.message);
      }
    })
    .catch(error => {
      alert('حدث خطأ في الاتصال');
    });
  }
}

// حذف الجزاء
function deletePenalty(id) {
  if (confirm('هل أنت متأكد من حذف الجزاء؟\nلا يمكن التراجع عن هذا الإجراء.') {
    fetch(`/penalties/${id}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json()
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('خطأ: ' + data.message);
      }
    })
    .catch(error => {
      alert('حدث خطأ في الاتصال');
    });
  }
}

// تحسين عرض الجدول
document.addEventListener('DOMContentLoaded', function() {
  // إضافة ألوان للصفوف حسب الخطورة
  const rows = document.querySelectorAll('tbody tr');
  rows.forEach(row => {
    const severityBadge = row.querySelector('.badge');
    if (severityBadge) {
      if (severityBadge.textContent.includes('حرج') {
        row.style.backgroundColor = '#f8d7da';
      } else if (severityBadge.textContent.includes('عالي') {
        row.style.backgroundColor = '#fff3cd';
      }
    }
  });
});
</script>
{% endblock %}
