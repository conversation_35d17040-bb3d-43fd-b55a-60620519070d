from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta

bp = Blueprint('appointments', __name__, url_prefix='/appointments')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        # في حالة عدم وجود app context
        return {}

@bp.route('/')
@login_required
def index():
    """عرض جميع المواعيد"""
    try:
        return render('appointments/index.html')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/calendar')
@login_required
def calendar():
    """عرض تقويم المواعيد"""
    month = request.GET.get('month', datetime.now().month, type=int)
    year = request.GET.get('year', datetime.now().year, type=int)
    
    # الحصول على المواعيد للشهر المحدد
    start_date = datetime(year, month, 1)
    if month == 12:
        end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
    else:
        end_date = datetime(year, month + 1, 1) - timedelta(days=1)
    
    appointments = Appointment.query.filter(
        Appointment.appointment_date >= start_date,
        Appointment.appointment_date <= end_date
    ).all()
    
    return render('appointments/calendar.html', 
                         appointments=appointments, 
                         month=month, 
                         year=year)

@bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة موعد جديد"""
    if request.method == 'POST':
        try:
            appointment = Appointment(
                client_id=request.POST['client_id'],
                lawyer_id=request.POST['lawyer_id'],
                case_id=request.POST.get('case_id') if request.POST.get('case_id') else None,
                appointment_type=request.POST['appointment_type'],
                appointment_date=datetime.strptime(
                    f"{request.POST['appointment_date']} {request.POST['appointment_time']}", 
                    '%Y-%m-%d %H:%M'
                ),
                notes=request.POST.get('notes')
            )
            
            db.add(appointment)
            db.save()
            
            flash('تم إضافة الموعد بنجاح', 'success')
            return redirect(url_for('appointments.view', id=appointment.id))
            
        except Exception as e:
            db.delete()
            flash(f'حدث خطأ أثناء إضافة الموعد: {str(e)}', 'error')
    
    clients = Client.query.filter_by(is_active=True).all()
    lawyers = Lawyer.query.filter_by(is_active=True).all()
    
    return render('appointments/add.html', clients=clients, lawyers=lawyers)

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الموعد"""
    appointment = Appointment.query.get_or_404(id)
    return render('appointments/view.html', appointment=appointment)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل الموعد"""
    appointment = Appointment.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            appointment.client_id = request.POST['client_id']
            appointment.lawyer_id = request.POST['lawyer_id']
            appointment.case_id = request.POST.get('case_id') if request.POST.get('case_id') else None
            appointment.appointment_type = request.POST['appointment_type']
            appointment.appointment_date = datetime.strptime(
                f"{request.POST['appointment_date']} {request.POST['appointment_time']}", 
                '%Y-%m-%d %H:%M'
            )
            appointment.status = request.POST['status']
            appointment.notes = request.POST.get('notes')
            appointment.updated_at = datetime.utcnow()
            
            db.save()
            
            flash('تم تحديث الموعد بنجاح', 'success')
            return redirect(url_for('appointments.view', id=appointment.id))
            
        except Exception as e:
            db.delete()
            flash(f'حدث خطأ أثناء تحديث الموعد: {str(e)}', 'error')
    
    clients = Client.query.filter_by(is_active=True).all()
    lawyers = Lawyer.query.filter_by(is_active=True).all()
    cases = Case.query.filter_by(client_id=appointment.client_id, is_active=True).all()
    
    return render('appointments/edit.html', 
                         appointment=appointment, 
                         clients=clients, 
                         lawyers=lawyers, 
                         cases=cases)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف الموعد"""
    appointment = Appointment.query.get_or_404(id)
    
    try:
        db.delete(appointment)
        db.save()
        
        flash('تم حذف الموعد بنجاح', 'success')
        
    except Exception as e:
        db.delete()
        flash(f'حدث خطأ أثناء حذف الموعد: {str(e)}', 'error')
    
    return redirect(url_for('appointments.index'))

@bp.route('/today')
@login_required
def today():
    """مواعيد اليوم"""
    today = datetime.now().date()
    appointments = Appointment.query.filter(
        db.func.date(Appointment.appointment_date) == today
    ).order_by(Appointment.appointment_date).all()
    
    return render('appointments/today.html', appointments=appointments)

@bp.route('/upcoming')
@login_required
def upcoming():
    """المواعيد القادمة"""
    now = datetime.now()
    appointments = Appointment.query.filter(
        Appointment.appointment_date >= now,
        Appointment.status == 'scheduled'
    ).order_by(Appointment.appointment_date).limit(10).all()
    
    return render('appointments/upcoming.html', appointments=appointments)

@bp.route('/api/client_cases/<int:client_id>')
@login_required
def api_client_cases(client_id):
    """الحصول على قضايا العميل عبر API"""
    cases = Case.query.filter_by(client_id=client_id, is_active=True).all()
    
    results = []
    for case in cases:
        results.append({
            'id': case.id,
            'text': f"{case.case_number} - {case.case_title}"
        })
    
    return JsonResponse(results)
