#!/usr/bin/env python3
"""
سكريبت تحديث قاعدة البيانات لإضافة عمود الخصومات
"""

import sqlite3
import os
from datetime import datetime

def update_database():
    """تحديث قاعدة البيانات لإضافة عمود الخصومات"""

    # مسار قاعدة البيانات
    possible_paths = [
        'legal_system.db',
        'instance/legal_system.db',
        os.path.join('instance', 'legal_system.db')
    ]

    db_path = None

    # البحث عن ملف قاعدة البيانات
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            print(f"🔍 تم العثور على قاعدة البيانات: {db_path}")
            break

    if not db_path:
        # البحث في مجلد instance
        if os.path.exists('instance'):
            instance_files = os.listdir('instance')
            db_files = [f for f in instance_files if f.endswith('.db')]
            if db_files:
                db_path = os.path.join('instance', db_files[0])
                print(f"🔍 تم العثور على قاعدة البيانات: {db_path}")

        if not db_path:
            print("❌ لم يتم العثور على أي ملف قاعدة بيانات")
            print("📁 الملفات في المجلد الحالي:")
            for f in os.listdir('.')[:10]:
                print(f"  - {f}")
            if os.path.exists('instance'):
                print("📁 الملفات في مجلد instance:")
                for f in os.listdir('instance')[:10]:
                    print(f"  - {f}")
            return False

    print(f"🔍 استخدام قاعدة البيانات: {db_path}")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من وجود جدول الموظفين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='employees'")
        if not cursor.fetchone():
            print("❌ جدول الموظفين غير موجود في قاعدة البيانات")
            print("📋 الجداول الموجودة:")
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            for table in tables:
                print(f"  - {table[0]}")
            conn.close()
            return False

        # التحقق من وجود عمود الخصومات
        cursor.execute("PRAGMA table_info(employees)")
        columns = [column[1] for column in cursor.fetchall()]

        deductions_exists = 'deductions' in columns

        if not deductions_exists:
            print("🔄 إضافة عمود الخصومات...")

            # إضافة عمود الخصومات
            cursor.execute("""
                ALTER TABLE employees
                ADD COLUMN deductions DECIMAL(10,2) DEFAULT 0.0
            """)

            # تحديث الرواتب الإجمالية الموجودة
            print("🔄 تحديث الرواتب الإجمالية...")
            cursor.execute("""
                UPDATE employees
                SET total_salary = COALESCE(basic_salary, 0) + COALESCE(allowances, 0) - COALESCE(deductions, 0)
                WHERE total_salary IS NULL OR total_salary = 0
            """)
        else:
            print("✅ عمود الخصومات موجود بالفعل")

        # التحقق من وجود الجداول الجديدة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [table[0] for table in cursor.fetchall()]

        new_tables = ['attendance', 'leave_requests', 'warnings', 'penalties']
        tables_to_create = [table for table in new_tables if table not in existing_tables]

        if tables_to_create:
            print(f"🔄 إنشاء الجداول الجديدة: {', '.join(tables_to_create)}")

            # إنشاء جدول الحضور
            if 'attendance' in tables_to_create:
                cursor.execute("""
                    CREATE TABLE attendance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        employee_id INTEGER NOT NULL,
                        date DATE NOT NULL,
                        check_in TIME,
                        check_out TIME,
                        break_start TIME,
                        break_end TIME,
                        hours_worked DECIMAL(4,2),
                        overtime_hours DECIMAL(4,2) DEFAULT 0.0,
                        status VARCHAR(20) DEFAULT 'present',
                        notes TEXT,
                        location VARCHAR(100),
                        ip_address VARCHAR(45),
                        is_manual BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        created_by INTEGER,
                        FOREIGN KEY (employee_id) REFERENCES employees (id),
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                """)
                print("✅ تم إنشاء جدول الحضور")

            # إنشاء جدول طلبات الإجازة
            if 'leave_requests' in tables_to_create:
                cursor.execute("""
                    CREATE TABLE leave_requests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        employee_id INTEGER NOT NULL,
                        leave_type VARCHAR(50) NOT NULL,
                        start_date DATE NOT NULL,
                        end_date DATE NOT NULL,
                        days_count INTEGER NOT NULL,
                        reason TEXT NOT NULL,
                        status VARCHAR(20) DEFAULT 'pending',
                        approved_by INTEGER,
                        approval_date DATETIME,
                        approval_notes TEXT,
                        attachment_path VARCHAR(255),
                        is_paid BOOLEAN DEFAULT 1,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (employee_id) REFERENCES employees (id),
                        FOREIGN KEY (approved_by) REFERENCES users (id)
                    )
                """)
                print("✅ تم إنشاء جدول طلبات الإجازة")

            # إنشاء جدول الإنذارات
            if 'warnings' in tables_to_create:
                cursor.execute("""
                    CREATE TABLE warnings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        employee_id INTEGER NOT NULL,
                        warning_type VARCHAR(50) NOT NULL,
                        category VARCHAR(50) NOT NULL,
                        title VARCHAR(200) NOT NULL,
                        description TEXT NOT NULL,
                        incident_date DATE NOT NULL,
                        severity VARCHAR(20) DEFAULT 'medium',
                        action_required TEXT,
                        deadline DATE,
                        status VARCHAR(20) DEFAULT 'active',
                        follow_up_date DATE,
                        resolution_notes TEXT,
                        attachment_path VARCHAR(255),
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        created_by INTEGER,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (employee_id) REFERENCES employees (id),
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                """)
                print("✅ تم إنشاء جدول الإنذارات")

            # إنشاء جدول الجزاءات
            if 'penalties' in tables_to_create:
                cursor.execute("""
                    CREATE TABLE penalties (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        employee_id INTEGER NOT NULL,
                        penalty_type VARCHAR(50) NOT NULL,
                        category VARCHAR(50) NOT NULL,
                        title VARCHAR(200) NOT NULL,
                        description TEXT NOT NULL,
                        incident_date DATE NOT NULL,
                        penalty_date DATE NOT NULL,
                        amount DECIMAL(10,2),
                        days_count INTEGER,
                        severity VARCHAR(20) DEFAULT 'medium',
                        status VARCHAR(20) DEFAULT 'active',
                        payment_status VARCHAR(20) DEFAULT 'pending',
                        notes TEXT,
                        attachment_path VARCHAR(255),
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        created_by INTEGER,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (employee_id) REFERENCES employees (id),
                        FOREIGN KEY (created_by) REFERENCES users (id)
                    )
                """)
                print("✅ تم إنشاء جدول الجزاءات")
        else:
            print("✅ جميع الجداول موجودة بالفعل")

        # حفظ التغييرات
        conn.save()

        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM employees WHERE deductions IS NOT NULL")
        employee_count = cursor.fetchone()[0]

        print(f"✅ تم تحديث قاعدة البيانات بنجاح")
        print(f"📊 عدد الموظفين: {employee_count}")

        # عرض إحصائيات الجداول الجديدة
        for table in new_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"📊 عدد السجلات في جدول {table}: {count}")
            except:
                pass

        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def verify_update():
    """التحقق من نجاح التحديث"""

    # البحث عن قاعدة البيانات
    possible_paths = [
        'legal_system.db',
        'instance/legal_system.db',
        os.path.join('instance', 'legal_system.db')
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print("❌ لم يتم العثور على قاعدة البيانات للتحقق")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من بنية الجدول
        cursor.execute("PRAGMA table_info(employees)")
        columns = cursor.fetchall()
        
        print("\n📋 بنية جدول الموظفين:")
        for column in columns:
            if column[1] in ['basic_salary', 'allowances', 'deductions', 'total_salary']:
                print(f"  - {column[1]}: {column[2]} (افتراضي: {column[4]})")
        
        # عرض عينة من البيانات
        cursor.execute("""
            SELECT employee_number, full_name, basic_salary, allowances, deductions, total_salary 
            FROM employees 
            LIMIT 5
        """)
        
        employees = cursor.fetchall()
        
        if employees:
            print("\n📊 عينة من بيانات الموظفين:")
            print("رقم الموظف | الاسم | الراتب الأساسي | البدلات | الخصومات | الإجمالي")
            print("-" * 80)
            for emp in employees:
                print(f"{emp[0]} | {emp[1][:20]} | {emp[2] or 0:.2f} | {emp[3] or 0:.2f} | {emp[4] or 0:.2f} | {emp[5] or 0:.2f}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")

if __name__ == "__main__":
    print("🚀 بدء تحديث قاعدة البيانات...")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    if update_database():
        print("\n🎉 تم التحديث بنجاح!")
        verify_update()
    else:
        print("\n❌ فشل في التحديث")
    
    print("\n" + "=" * 50)
    print("انتهى التحديث")
