#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع لتحويل db.session.commit() إلى db.session.commit()
"""

import os
import re
import glob

def fix_db_save():
    """تحويل جميع استخدامات db.session.commit() إلى db.session.commit()"""
    
    print("🔧 إصلاح db.session.commit() إلى db.session.commit()...")
    
    # البحث عن جميع ملفات Python
    python_files = []
    for pattern in ['*.py', 'routes/*.py']:
        python_files.extend(glob.glob(pattern))
    
    total_fixes = 0
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # أنماط الإصلاح
            patterns = [
                # db.session.commit() → db.session.commit()
                (r'db\.save\(\)', 'db.session.commit()'),
                
                # db.session.add() → db.session.add()
                (r'db\.add\(', 'db.session.add('),
                
                # db.session.rollback() → db.session.rollback()
                (r'db\.delete\(\)', 'db.session.rollback()'),
                
                # db.session.query() → db.session.query()
                (r'db\.query\(', 'db.session.query('),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            # حفظ الملف إذا تم إجراء تغييرات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
            else:
                print(f"  ✅ {file_path}: لا توجد أخطاء")
                
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} خطأ db.save/add/delete")
    return total_fixes

def validate_db_syntax():
    """التحقق من صحة بناء الجملة لقاعدة البيانات"""
    
    print("\n🔍 التحقق من صحة بناء الجملة لقاعدة البيانات...")
    
    python_files = []
    for pattern in ['*.py', 'routes/*.py']:
        python_files.extend(glob.glob(pattern))
    
    errors = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن أخطاء شائعة
            error_patterns = [
                (r'db\.save\(\)', 'استخدام db.session.commit() بدلاً من db.session.commit()'),
                (r'db\.add\(', 'استخدام db.session.add() بدلاً من db.session.add()'),
                (r'db\.delete\(\)', 'استخدام db.session.rollback() بدلاً من db.session.rollback()'),
                (r'db\.query\(', 'استخدام db.session.query() بدلاً من db.session.query()'),
            ]
            
            for pattern, error_type in error_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    errors.append(f"{file_path}: {error_type} ({len(matches)} مرة)")
        
        except Exception as e:
            errors.append(f"{file_path}: خطأ في القراءة - {e}")
    
    if errors:
        print("❌ تم العثور على أخطاء:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ جميع ملفات قاعدة البيانات صحيحة!")
        return True

if __name__ == "__main__":
    print("🚀 بدء إصلاح أخطاء قاعدة البيانات...")
    
    # إصلاح الأخطاء
    fixes = fix_db_save()
    
    # التحقق من النتائج
    is_valid = validate_db_syntax()
    
    print("\n" + "="*50)
    print(f"🎉 تم إصلاح {fixes} خطأ إجمالي")
    if is_valid:
        print("✅ جميع ملفات قاعدة البيانات تعمل بشكل صحيح")
    else:
        print("⚠️ لا تزال هناك بعض الأخطاء")
    print("="*50)
