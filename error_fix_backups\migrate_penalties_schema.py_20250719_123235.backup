#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ترحيل مخطط جدول الجزاءات إلى الشكل الجديد
"""

import sqlite3
import os
from datetime import datetime

def migrate_penalties_schema():
    """ترحيل مخطط جدول الجزاءات"""
    
    db_path = 'legal_system.db'
    
    if not os.path.exists(db_path):
        print(f"❌ ملف قاعدة البيانات غير موجود: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"🔍 فحص قاعدة البيانات: {db_path}")
        
        # التحقق من وجود جدول الجزاءات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='penalties'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ جدول الجزاءات غير موجود")
            return False
        
        # فحص الأعمدة الحالية
        cursor.execute("PRAGMA table_info(penalties)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        print(f"📋 الأعمدة الحالية: {', '.join(column_names)}")
        
        # التحقق من وجود الأعمدة المطلوبة
        required_columns = ['penalty_type', 'title', 'description', 'incident_date', 'amount', 'days_count']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if not missing_columns:
            print("✅ جميع الأعمدة المطلوبة موجودة")
            return True
        
        print(f"🔄 الأعمدة المفقودة: {', '.join(missing_columns)}")
        
        # حفظ البيانات الموجودة
        cursor.execute("SELECT * FROM penalties")
        existing_data = cursor.fetchall()
        print(f"📊 عدد السجلات الموجودة: {len(existing_data)}")
        
        # إنشاء نسخة احتياطية من الجدول
        backup_table_name = f"penalties_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute(f"CREATE TABLE {backup_table_name} AS SELECT * FROM penalties")
        print(f"💾 تم إنشاء نسخة احتياطية: {backup_table_name}")
        
        # حذف الجدول القديم
        cursor.execute("DROP TABLE penalties")
        print("🗑️ تم حذف الجدول القديم")
        
        # إنشاء الجدول الجديد بالمخطط الصحيح
        cursor.execute("""
            CREATE TABLE penalties (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                penalty_type VARCHAR(50) NOT NULL,
                category VARCHAR(50) NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                incident_date DATE NOT NULL,
                penalty_date DATE NOT NULL,
                amount DECIMAL(10,2),
                days_count INTEGER,
                severity VARCHAR(20) DEFAULT 'medium',
                status VARCHAR(20) DEFAULT 'active',
                payment_status VARCHAR(20) DEFAULT 'pending',
                notes TEXT,
                attachment_path VARCHAR(255),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)
        print("✅ تم إنشاء الجدول الجديد بالمخطط الصحيح")
        
        # ترحيل البيانات الموجودة (إذا كانت موجودة)
        if existing_data:
            print("🔄 ترحيل البيانات الموجودة...")
            
            for row in existing_data:
                # تحويل البيانات من المخطط القديم إلى الجديد
                try:
                    # استخراج البيانات من المخطط القديم
                    old_id = row[0]
                    employee_id = row[1]
                    penalty_number = row[2] if len(row) > 2 else None
                    old_penalty_type = row[3] if len(row) > 3 else 'general'
                    violation_desc = row[4] if len(row) > 4 else ''
                    penalty_desc = row[5] if len(row) > 5 else ''
                    violation_date = row[6] if len(row) > 6 else None
                    penalty_date = row[7] if len(row) > 7 else None
                    effective_date = row[8] if len(row) > 8 else None
                    deduction_amount = row[9] if len(row) > 9 else None
                    deduction_days = row[10] if len(row) > 10 else None
                    status = row[11] if len(row) > 11 else 'active'
                    severity = row[12] if len(row) > 12 else 'medium'
                    created_at = row[13] if len(row) > 13 else None
                    updated_at = row[14] if len(row) > 14 else None
                    created_by = row[15] if len(row) > 15 else None
                    
                    # تحويل إلى المخطط الجديد
                    new_penalty_type = 'financial' if deduction_amount else 'suspension' if deduction_days else 'warning'
                    category = 'general'
                    title = penalty_desc or violation_desc or 'جزاء'
                    description = f"{violation_desc}\n{penalty_desc}".strip()
                    incident_date = violation_date or penalty_date
                    amount = deduction_amount
                    days_count = deduction_days
                    payment_status = 'pending' if new_penalty_type == 'financial' else None
                    
                    # إدراج البيانات في الجدول الجديد
                    cursor.execute("""
                        INSERT INTO penalties (
                            employee_id, penalty_type, category, title, description,
                            incident_date, penalty_date, amount, days_count, severity,
                            status, payment_status, created_at, updated_at, created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        employee_id, new_penalty_type, category, title, description,
                        incident_date, penalty_date, amount, days_count, severity,
                        status, payment_status, created_at, updated_at, created_by
                    ))
                    
                except Exception as e:
                    print(f"⚠️ خطأ في ترحيل السجل {old_id}: {e}")
            
            print(f"✅ تم ترحيل {len(existing_data)} سجل")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة
        cursor.execute("PRAGMA table_info(penalties)")
        new_columns = cursor.fetchall()
        new_column_names = [column[1] for column in new_columns]
        
        print(f"📋 الأعمدة الجديدة: {', '.join(new_column_names)}")
        
        cursor.execute("SELECT COUNT(*) FROM penalties")
        count = cursor.fetchone()[0]
        print(f"📊 عدد السجلات بعد الترحيل: {count}")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 بدء ترحيل مخطط جدول الجزاءات...")
    print("=" * 60)
    
    success = migrate_penalties_schema()
    
    print("=" * 60)
    if success:
        print("✅ تم ترحيل مخطط جدول الجزاءات بنجاح")
    else:
        print("❌ فشل في ترحيل مخطط جدول الجزاءات")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
