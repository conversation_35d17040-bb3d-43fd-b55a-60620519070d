#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص شامل لعدم تشابه العلاقات والقوالب والنماذج
"""

import os
import re
import glob
from collections import defaultdict, Counter
import hashlib

def check_model_relationships():
    """فحص العلاقات في النماذج"""
    
    print("🔍 فحص العلاقات في النماذج...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استخراج العلاقات
        relationships = {
            'foreign_keys': [],
            'relationships': [],
            'back_references': []
        }
        
        # البحث عن ForeignKey
        fk_pattern = r'(\w+)\s*=\s*db\.Column\([^,]*db\.ForeignKey\([\'"]([^\'\"]+)[\'"]\)'
        foreign_keys = re.findall(fk_pattern, content)
        relationships['foreign_keys'] = foreign_keys
        
        # البحث عن relationship
        rel_pattern = r'(\w+)\s*=\s*db\.relationship\([\'"]([^\'\"]+)[\'"](?:,\s*backref=[\'"]([^\'\"]+)[\'"])?'
        relations = re.findall(rel_pattern, content)
        relationships['relationships'] = relations
        
        # البحث عن backref
        backref_pattern = r'backref=[\'"]([^\'\"]+)[\'"]'
        backrefs = re.findall(backref_pattern, content)
        relationships['back_references'] = backrefs
        
        return relationships
        
    except Exception as e:
        print(f"❌ خطأ في فحص العلاقات: {e}")
        return {'foreign_keys': [], 'relationships': [], 'back_references': []}

def check_template_similarity():
    """فحص تشابه القوالب"""
    
    print("\n🔍 فحص تشابه القوالب...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    template_hashes = {}
    template_structures = {}
    
    for template_file in template_files:
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # حساب hash للمحتوى
            content_hash = hashlib.md5(content.encode()).hexdigest()
            template_hashes[template_file] = content_hash
            
            # استخراج البنية الأساسية
            structure = extract_template_structure(content)
            template_structures[template_file] = structure
            
        except Exception as e:
            print(f"❌ خطأ في قراءة {template_file}: {e}")
    
    return template_hashes, template_structures

def extract_template_structure(content):
    """استخراج البنية الأساسية للقالب"""
    
    structure = {
        'extends': [],
        'blocks': [],
        'includes': [],
        'forms': [],
        'tables': [],
        'urls': []
    }
    
    # البحث عن extends
    extends_pattern = r'\{\%\s*extends\s+[\'"]([^\'\"]+)[\'"]\s*\%\}'
    structure['extends'] = re.findall(extends_pattern, content)
    
    # البحث عن blocks
    block_pattern = r'\{\%\s*block\s+(\w+)\s*\%\}'
    structure['blocks'] = re.findall(block_pattern, content)
    
    # البحث عن includes
    include_pattern = r'\{\%\s*include\s+[\'"]([^\'\"]+)[\'"]\s*\%\}'
    structure['includes'] = re.findall(include_pattern, content)
    
    # البحث عن forms
    form_pattern = r'<form[^>]*>'
    structure['forms'] = len(re.findall(form_pattern, content))
    
    # البحث عن tables
    table_pattern = r'<table[^>]*>'
    structure['tables'] = len(re.findall(table_pattern, content))
    
    # البحث عن URLs
    url_pattern = r'url_for\([\'"]([^\'\"]+)[\'"]\)'
    structure['urls'] = re.findall(url_pattern, content)
    
    return structure

def check_form_similarity():
    """فحص تشابه النماذج (Forms)"""
    
    print("\n🔍 فحص تشابه النماذج...")
    
    # البحث عن ملفات النماذج
    form_files = []
    for pattern in ['forms.py', '**/forms.py', 'routes/*.py']:
        form_files.extend(glob.glob(pattern, recursive=True))
    
    form_classes = {}
    form_fields = defaultdict(list)
    
    for form_file in form_files:
        if os.path.exists(form_file):
            try:
                with open(form_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # البحث عن كلاسات النماذج
                class_pattern = r'class\s+(\w+Form)\([^)]*\):(.*?)(?=class\s+\w+|$)'
                classes = re.findall(class_pattern, content, re.DOTALL)
                
                for class_name, class_content in classes:
                    # استخراج الحقول
                    field_pattern = r'(\w+)\s*=\s*(\w+Field)\('
                    fields = re.findall(field_pattern, class_content)
                    
                    form_classes[f"{form_file}::{class_name}"] = fields
                    
                    for field_name, field_type in fields:
                        form_fields[field_name].append(f"{form_file}::{class_name}")
                
            except Exception as e:
                print(f"❌ خطأ في قراءة {form_file}: {e}")
    
    return form_classes, form_fields

def check_route_similarity():
    """فحص تشابه المسارات"""
    
    print("\n🔍 فحص تشابه المسارات...")
    
    route_files = glob.glob("routes/*.py") + ['app.py']
    route_patterns = {}
    route_names = defaultdict(list)
    
    for route_file in route_files:
        if os.path.exists(route_file):
            try:
                with open(route_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # البحث عن route decorators
                route_pattern = r'@\w+\.route\([\'"]([^\'\"]+)[\'"](?:,\s*methods=\[([^\]]+)\])?\)'
                routes = re.findall(route_pattern, content)
                
                # البحث عن أسماء الدوال
                func_pattern = r'def\s+(\w+)\([^)]*\):'
                functions = re.findall(func_pattern, content)
                
                route_patterns[route_file] = {
                    'routes': routes,
                    'functions': functions
                }
                
                for func_name in functions:
                    route_names[func_name].append(route_file)
                
            except Exception as e:
                print(f"❌ خطأ في قراءة {route_file}: {e}")
    
    return route_patterns, route_names

def analyze_similarities():
    """تحليل أوجه التشابه"""
    
    print("\n📊 تحليل أوجه التشابه...")
    
    # جمع البيانات
    relationships = check_model_relationships()
    template_hashes, template_structures = check_template_similarity()
    form_classes, form_fields = check_form_similarity()
    route_patterns, route_names = check_route_similarity()
    
    similarities = {
        'duplicate_templates': [],
        'similar_template_structures': [],
        'duplicate_forms': [],
        'duplicate_routes': [],
        'duplicate_relationships': [],
        'warnings': []
    }
    
    # فحص القوالب المكررة
    hash_groups = defaultdict(list)
    for template, hash_val in template_hashes.items():
        hash_groups[hash_val].append(template)
    
    for hash_val, templates in hash_groups.items():
        if len(templates) > 1:
            similarities['duplicate_templates'].append(templates)
    
    # فحص البنى المتشابهة للقوالب
    structure_groups = defaultdict(list)
    for template, structure in template_structures.items():
        structure_key = str(sorted(structure.items()))
        structure_groups[structure_key].append(template)
    
    for structure_key, templates in structure_groups.items():
        if len(templates) > 1:
            similarities['similar_template_structures'].append(templates)
    
    # فحص النماذج المكررة
    form_signatures = defaultdict(list)
    for form_name, fields in form_classes.items():
        field_signature = str(sorted(fields))
        form_signatures[field_signature].append(form_name)
    
    for signature, forms in form_signatures.items():
        if len(forms) > 1:
            similarities['duplicate_forms'].append(forms)
    
    # فحص المسارات المكررة
    for func_name, files in route_names.items():
        if len(files) > 1:
            similarities['duplicate_routes'].append({
                'function': func_name,
                'files': files
            })
    
    # فحص العلاقات المكررة
    fk_counter = Counter([fk[1] for fk in relationships['foreign_keys']])
    for fk, count in fk_counter.items():
        if count > 5:  # أكثر من 5 مراجع
            similarities['warnings'].append(f"مفتاح خارجي مستخدم كثيراً: {fk} ({count} مرة)")
    
    return similarities

def generate_similarity_report():
    """إنشاء تقرير التشابه"""
    
    print("📋 إنشاء تقرير التشابه...")
    
    similarities = analyze_similarities()
    
    report = []
    report.append("=" * 60)
    report.append("📊 تقرير فحص التشابه في العلاقات والقوالب والنماذج")
    report.append("=" * 60)
    
    # ملخص عام
    total_issues = (len(similarities['duplicate_templates']) + 
                   len(similarities['similar_template_structures']) + 
                   len(similarities['duplicate_forms']) + 
                   len(similarities['duplicate_routes']))
    
    report.append(f"\n📈 ملخص عام:")
    report.append(f"  - إجمالي مشاكل التشابه: {total_issues}")
    report.append(f"  - قوالب مكررة: {len(similarities['duplicate_templates'])}")
    report.append(f"  - بنى قوالب متشابهة: {len(similarities['similar_template_structures'])}")
    report.append(f"  - نماذج مكررة: {len(similarities['duplicate_forms'])}")
    report.append(f"  - مسارات مكررة: {len(similarities['duplicate_routes'])}")
    report.append(f"  - تحذيرات: {len(similarities['warnings'])}")
    
    # القوالب المكررة
    if similarities['duplicate_templates']:
        report.append(f"\n❌ قوالب مكررة ({len(similarities['duplicate_templates'])}):")
        for i, templates in enumerate(similarities['duplicate_templates'], 1):
            report.append(f"  {i}. {' = '.join(templates)}")
    
    # البنى المتشابهة
    if similarities['similar_template_structures']:
        report.append(f"\n⚠️ بنى قوالب متشابهة ({len(similarities['similar_template_structures'])}):")
        for i, templates in enumerate(similarities['similar_template_structures'], 1):
            report.append(f"  {i}. {' ≈ '.join(templates)}")
    
    # النماذج المكررة
    if similarities['duplicate_forms']:
        report.append(f"\n❌ نماذج مكررة ({len(similarities['duplicate_forms'])}):")
        for i, forms in enumerate(similarities['duplicate_forms'], 1):
            report.append(f"  {i}. {' = '.join(forms)}")
    
    # المسارات المكررة
    if similarities['duplicate_routes']:
        report.append(f"\n❌ مسارات مكررة ({len(similarities['duplicate_routes'])}):")
        for i, route_info in enumerate(similarities['duplicate_routes'], 1):
            report.append(f"  {i}. دالة '{route_info['function']}' في: {', '.join(route_info['files'])}")
    
    # التحذيرات
    if similarities['warnings']:
        report.append(f"\n⚠️ تحذيرات ({len(similarities['warnings'])}):")
        for warning in similarities['warnings']:
            report.append(f"  - {warning}")
    
    # النتيجة النهائية
    if total_issues == 0:
        report.append(f"\n🎉 ممتاز! لا يوجد تشابه مشكوك فيه")
        report.append(f"✅ جميع العلاقات والقوالب والنماذج فريدة")
    else:
        report.append(f"\n⚠️ تم العثور على {total_issues} حالة تشابه")
        report.append(f"💡 يُنصح بمراجعة الحالات المكررة")
    
    return "\n".join(report)

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء فحص التشابه الشامل...")
    
    # إنشاء التقرير
    report = generate_similarity_report()
    
    # طباعة التقرير
    print(report)
    
    # حفظ التقرير
    with open('similarity_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 تم حفظ التقرير في: similarity_report.txt")
    
    # التحقق من النتائج
    similarities = analyze_similarities()
    total_issues = (len(similarities['duplicate_templates']) + 
                   len(similarities['similar_template_structures']) + 
                   len(similarities['duplicate_forms']) + 
                   len(similarities['duplicate_routes']))
    
    if total_issues == 0:
        print("\n🎉 ممتاز! لا يوجد تشابه مشكوك فيه")
        return True
    else:
        print(f"\n⚠️ تم العثور على {total_issues} حالة تشابه تحتاج مراجعة")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
