{% extends "base.html" %} {% block title %}إضافة موظف جديد - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-user-plus me-2"></i>
    إضافة موظف جديد
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <a
      href="{{ url_for('employees.index' }}"
      class="btn btn-outline-secondary"
    >
      <i class="fas fa-arrow-right me-1"></i>
      العودة للقائمة
    </a>
  </div>
</div>

<form method="POST" id="employeeForm">
  <div class="row">
    <div class="col-md-8">
      <!-- البيانات الشخصية -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-user me-2"></i>
            البيانات الشخصية
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="full_name" class="form-label"
                  >الاسم الكامل <span class="text-danger">*</span></label>
                <input
                  type="text"
                  class="form-control"
                  id="full_name"
                  name="full_name"
                  required
                />
                <div class="form-text">أدخل الاسم الكامل للموظف</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="full_name_en" class="form-label"
                  >الاسم بالإنجليزية</label>
                <input
                  type="text"
                  class="form-control"
                  id="full_name_en"
                  name="full_name_en"
                />
                <div class="form-text">الاسم باللغة الإنجليزية (اختياري)</div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="national_id" class="form-label"
                  >رقم الهوية الوطنية</label>
                <input
                  type="text"
                  class="form-control"
                  id="national_id"
                  name="national_id"
                  maxlength="10"
                />
                <div class="form-text">رقم الهوية الوطنية (10 أرقام)</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                <input
                  type="date"
                  class="form-control"
                  id="birth_date"
                  name="birth_date"
                />
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label for="gender" class="form-label">الجنس</label>
                <select class="form-select" id="gender" name="gender">
                  <option value="">اختر الجنس</option>
                  <option value="male">ذكر</option>
                  <option value="female">أنثى</option>
                </select>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="nationality" class="form-label">الجنسية</label>
                <input
                  type="text"
                  class="form-control"
                  id="nationality"
                  name="nationality"
                  value="سعودي"
                />
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="marital_status" class="form-label"
                  >الحالة الاجتماعية</label>
                <select
                  class="form-select"
                  id="marital_status"
                  name="marital_status"
                >
                  <option value="">اختر الحالة</option>
                  <option value="single">أعزب</option>
                  <option value="married">متزوج</option>
                  <option value="divorced">مطلق</option>
                  <option value="widowed">أرمل</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- معلومات الاتصال -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-phone me-2"></i>
            معلومات الاتصال
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input
                  type="email"
                  class="form-control"
                  id="email"
                  name="email"
                />
                <div class="form-text">سيتم استخدامه للتواصل الرسمي</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="phone" class="form-label">رقم الهاتف</label>
                <input
                  type="tel"
                  class="form-control"
                  id="phone"
                  name="phone"
                  placeholder="+966501234567"
                />
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="mobile" class="form-label">رقم الجوال</label>
                <input
                  type="tel"
                  class="form-control"
                  id="mobile"
                  name="mobile"
                  placeholder="+966501234567"
                />
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label for="address" class="form-label">العنوان</label>
            <textarea
              class="form-control"
              id="address"
              name="address"
              rows="3"
              placeholder="أدخل العنوان الكامل للموظف"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- البيانات الوظيفية -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-briefcase me-2"></i>
            البيانات الوظيفية
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="department_id" class="form-label"
                  >القسم <span class="text-danger">*</span></label>
                <select
                  class="form-select"
                  id="department_id"
                  name="department_id"
                  required
                >
                  <option value="">اختر القسم</option>
                  {% for dept in departments %}
                  <option value="{{ dept.id }}">{{ dept.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="position" class="form-label"
                  >المنصب <span class="text-danger">*</span></label>
                <input
                  type="text"
                  class="form-control"
                  id="position"
                  name="position"
                  required
                />
                <div class="form-text">المسمى الوظيفي للموظف</div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="position_en" class="form-label"
                  >المنصب بالإنجليزية</label>
                <input
                  type="text"
                  class="form-control"
                  id="position_en"
                  name="position_en"
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="employment_type" class="form-label"
                  >نوع التوظيف <span class="text-danger">*</span></label>
                <select
                  class="form-select"
                  id="employment_type"
                  name="employment_type"
                  required
                >
                  <option value="">اختر نوع التوظيف</option>
                  <option value="full_time">دوام كامل</option>
                  <option value="part_time">دوام جزئي</option>
                  <option value="contract">عقد</option>
                  <option value="intern">متدرب</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="hire_date" class="form-label"
                  >تاريخ التوظيف <span class="text-danger">*</span></label>
                <input
                  type="date"
                  class="form-control"
                  id="hire_date"
                  name="hire_date"
                  required
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">رقم الموظف</label>
                <input
                  type="text"
                  class="form-control"
                  value="سيتم إنشاؤه تلقائياً"
                  readonly
                />
                <div class="form-text">سيتم إنشاء رقم فريد للموظف تلقائياً</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- البيانات المالية -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-money-bill-wave me-2"></i>
            البيانات المالية
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label for="basic_salary" class="form-label"
                  >الراتب الأساسي</label>
                <input
                  type="number"
                  class="form-control"
                  id="basic_salary"
                  name="basic_salary"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                />
                <div class="form-text">بالريال السعودي</div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="allowances" class="form-label">البدلات</label>
                <input
                  type="number"
                  class="form-control"
                  id="allowances"
                  name="allowances"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                />
                <div class="form-text">إجمالي البدلات</div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label for="deductions" class="form-label">الخصومات</label>
                <input
                  type="number"
                  class="form-control"
                  id="deductions"
                  name="deductions"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                />
                <div class="form-text">إجمالي الخصومات</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label for="total_salary" class="form-label"
                  >إجمالي الراتب</label>
                <input
                  type="number"
                  class="form-control"
                  id="total_salary"
                  name="total_salary"
                  readonly
                />
                <div class="form-text">يحسب تلقائياً</div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="bank_account" class="form-label"
                  >رقم الحساب البنكي</label>
                <input
                  type="text"
                  class="form-control"
                  id="bank_account"
                  name="bank_account"
                />
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="bank_name" class="form-label">اسم البنك</label>
                <input
                  type="text"
                  class="form-control"
                  id="bank_name"
                  name="bank_name"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <!-- معلومات إضافية -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>
            معلومات إضافية
          </h5>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <h6 class="alert-heading">
              <i class="fas fa-lightbulb me-1"></i>
              نصائح مهمة:
            </h6>
            <ul class="mb-0">
              <li>تأكد من صحة البريد الإلكتروني</li>
              <li>رقم الهوية يجب أن يكون فريد</li>
              <li>اختر القسم المناسب للموظف</li>
              <li>حدد نوع التوظيف بدقة</li>
              <li>تاريخ التوظيف مطلوب</li>
            </ul>
          </div>

          <div class="alert alert-warning">
            <h6 class="alert-heading">
              <i class="fas fa-exclamation-triangle me-1"></i>
              تنبيه:
            </h6>
            <p class="mb-0">
              تأكد من دقة البيانات المدخلة حيث سيتم استخدامها في جميع المعاملات
              والتقارير.
            </p>
          </div>
        </div>
      </div>

      <!-- أزرار الحفظ -->
      <div class="card">
        <div class="card-body">
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-1"></i>
              حفظ الموظف
            </button>
            <a
              href="{{ url_for('employees.index' }}"
              class="btn btn-outline-secondary"
            >
              <i class="fas fa-times me-1"></i>
              إلغاء
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>

<script>
  // التحقق من صحة النموذج
  document
    .getElementById("employeeForm")
    .addEventListener("submit", function (e) {
      const fullName = document.getElementById("full_name").value.trim();
      const departmentId = document.getElementById("department_id").value;
      const position = document.getElementById("position").value.trim();
      const employmentType = document.getElementById("employment_type").value;
      const hireDate = document.getElementById("hire_date").value;

      if (
        !fullName ||
        !departmentId ||
        !position ||
        !employmentType ||
        !hireDate
      ) {
        e.preventDefault();
        showAlert("danger", "يرجى ملء جميع الحقول المطلوبة");
        return false;
      }

      // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
      const email = document.getElementById("email").value.trim();
      if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email) {
          e.preventDefault();
          showAlert("danger", "يرجى إدخال بريد إلكتروني صحيح");
          document.getElementById("email").focus();
          return false;
        }
      }

      // التحقق من رقم الهوية إذا تم إدخاله
      const nationalId = document.getElementById("national_id").value.trim();
      if (nationalId && nationalId.length !== 10) {
        e.preventDefault();
        showAlert("danger", "رقم الهوية يجب أن يكون 10 أرقام");
        document.getElementById("national_id").focus();
        return false;
      }
    });

  // حساب إجمالي الراتب تلقائياً
  function calculateTotalSalary() {
    const basicSalary =
      parseFloat(document.getElementById("basic_salary").value) || 0;
    const allowances =
      parseFloat(document.getElementById("allowances").value) || 0;
    const deductions =
      parseFloat(document.getElementById("deductions").value) || 0;
    const totalSalary = basicSalary + allowances - deductions;
    document.getElementById("total_salary").value = totalSalary.toFixed(2);
  }

  document
    .getElementById("basic_salary")
    .addEventListener("input", calculateTotalSalary);
  document
    .getElementById("allowances")
    .addEventListener("input", calculateTotalSalary);
  document
    .getElementById("deductions")
    .addEventListener("input", calculateTotalSalary);

  // تعيين تاريخ اليوم كتاريخ افتراضي للتوظيف
  document.addEventListener("DOMContentLoaded", function () {
    const today = new Date().toISOString().split("T")[0];
    document.getElementById("hire_date").value = today;
  });
</script>
{% endblock %}
