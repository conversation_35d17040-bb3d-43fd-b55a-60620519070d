{% extends "base.html" %}

{% block title %}تقرير الجزاءات{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-chart-bar"></i>
            {{ report_title }}
          </h3>
          <div class="card-tools">
            <button type="button" class="btn btn-primary btn-sm" onclick="printReport()">
              <i class="fas fa-print"></i>
              طباعة التقرير
            </button>
            <a href="{% url 'penalties:index' %}" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a>
          </div>
        </div>

        <div class="card-body">
          <!-- فلاتر التقرير -->
          <div class="card card-outline card-primary collapsed-card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-filter"></i>
                فلاتر التقرير
              </h3>
              <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
            <div class="card-body">
              <form method="GET" action="{% url 'penalties:report' %}">
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="employee_id">الموظف</label>
                      <select class="form-control" id="employee_id" name="employee_id">
                        <option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" 
                                {% if employee_id == employee.id %}selected{% endif %}>
                          {{ employee.full_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="department_id">القسم</label>
                      <select class="form-control" id="department_id" name="department_id">
                        <option value="">جميع الأقسام</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" 
                                {% if department_id == department.id %}selected{% endif %}>
                          {{ department.name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="penalty_type">نوع الجزاء</label>
                      <select class="form-control" id="penalty_type" name="penalty_type">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in penalty_types %}
                        <option value="{{ type_code }}" {% if penalty_type == type_code %}selected{% endif %}>
                          {{ type_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="date_from">من تاريخ</label>
                      <input type="date" class="form-control" id="date_from" name="date_from" 
                             value="{{ date_from }}">
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="date_to">إلى تاريخ</label>
                      <input type="date" class="form-control" id="date_to" name="date_to" 
                             value="{{ date_to }}">
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-search"></i>
                      تحديث التقرير
                    </button>
                    <a href="{% url 'penalties:report' %}" class="btn btn-secondary">
                      <i class="fas fa-undo"></i>
                      إعادة تعيين
                    </a>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- إحصائيات التقرير -->
          <div class="row mb-4">
            <div class="col-md-2">
              <div class="info-box bg-info">
                <span class="info-box-icon"><i class="fas fa-list"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">إجمالي الجزاءات</span>
                  <span class="info-box-number">{{ stats.total_penalties }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-warning">
                <span class="info-box-icon"><i class="fas fa-exclamation"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">نشطة</span>
                  <span class="info-box-number">{{ stats.active_penalties }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-success">
                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">مكتملة</span>
                  <span class="info-box-number">{{ stats.completed_penalties }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-primary">
                <span class="info-box-icon"><i class="fas fa-money-bill"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">جزاءات مالية</span>
                  <span class="info-box-number">{{ stats.financial_penalties_count }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-danger">
                <span class="info-box-icon"><i class="fas fa-dollar-sign"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">إجمالي المبالغ</span>
                  <span class="info-box-number">{{ stats.total_amount|round(0)|default(0) }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-secondary">
                <span class="info-box-icon"><i class="fas fa-credit-card"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">مدفوع</span>
                  <span class="info-box-number">{{ stats.paid_amount|round(0)|default(0) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- إحصائيات حسب النوع والفئة -->
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="card card-outline card-success">
                <div class="card-header">
                  <h3 class="card-title">إحصائيات حسب نوع الجزاء</h3>
                </div>
                <div class="card-body">
                  {% for type_code, type_data in stats.type_stats %}
                  <div class="progress-group">
                    {{ type_data.name }}
                    <span class="float-right">
                      <b>{{ type_data.count }}</b>/{{ stats.total_penalties }}
                      {% if type_data.amount > 0 %}
                        <small>({{ type_data.amount|round(0)|default(0) }} ريال)</small>
                      {% endif %}
                    </span>
                    <div class="progress progress-sm">
                      <div class="progress-bar bg-primary"
                           data-count="{{ type_data.count }}"
                           data-total="{{ stats.total_penalties }}"
                           style="width: 0%"></div>
                    </div>
                  </div>
                  {% endfor %}
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card card-outline card-warning">
                <div class="card-header">
                  <h3 class="card-title">إحصائيات حسب الفئة</h3>
                </div>
                <div class="card-body">
                  {% for cat_code, cat_data in stats.category_stats %}
                  <div class="progress-group">
                    {{ cat_data.name }}
                    <span class="float-right"><b>{{ cat_data.count }}</b>/{{ stats.total_penalties }}</span>
                    <div class="progress progress-sm">
                      <div class="progress-bar bg-warning"
                           data-count="{{ cat_data.count }}"
                           data-total="{{ stats.total_penalties }}"
                           style="width: 0%"></div>
                    </div>
                  </div>
                  {% endfor %}
                </div>
              </div>
            </div>
          </div>

          <!-- جدول التقرير -->
          <div id="reportContent">
            <div class="report-header text-center mb-4" style="display: none;">
              <h2>{{ report_title }}</h2>
              <p><strong>الفترة:</strong> {{ report_period }}</p>
              <p><strong>تاريخ التقرير:</strong> {{ report_date }}</p>
              <hr>
            </div>

            <div class="table-responsive">
              <table class="table table-bordered table-striped">
                <thead>
                  <tr>
                    <th>الموظف</th>
                    <th>القسم</th>
                    <th>نوع الجزاء</th>
                    <th>العنوان</th>
                    <th>تاريخ الجزاء</th>
                    <th>المبلغ/الأيام</th>
                    <th>الخطورة</th>
                    <th>الحالة</th>
                    <th class="no-print">الوصف</th>
                  </tr>
                </thead>
                <tbody>
                  {% for penalty in penalties %}
                  <tr>
                    <td>
                      <strong>{{ penalty.employee.full_name }}</strong><br>
                      <small class="text-muted">{{ penalty.employee.employee_number }}</small>
                    </td>
                    <td>
                      {% if penalty.employee.department %}
                        {{ penalty.employee.department.name }}
                      {% else %}
                        <span class="text-muted">-</span>
                      {% endif %}
                    </td>
                    <td>
                      {% for type_code, type_name in penalty_types %}
                        {% if penalty.penalty_type == type_code %}
                          {{ type_name }}
                        {% endif %}
                      {% endfor %}
                    </td>
                    <td><strong>{{ penalty.title }}</strong></td>
                    <td>{{ penalty.penalty_date.strftime("Y-m-d") if penalty.penalty_date else "-" }}</td>
                    <td>
                      {% if penalty.penalty_type == 'financial' and penalty.amount %}
                        {{ penalty.amount|round(2)|default(0) }} ريال
                        {% if penalty.payment_status == 'paid' %}
                          <br><small class="text-success">(مدفوع)</small>
                        {% elif penalty.payment_status == 'pending' %}
                          <br><small class="text-warning">(في الانتظار)</small>
                        {% elif penalty.payment_status == 'waived' %}
                          <br><small class="text-info">(معفى)</small>
                        {% endif %}
                      {% elif penalty.penalty_type == 'suspension' and penalty.days_count %}
                        {{ penalty.days_count }} يوم
                      {% else %}
                        -
                      {% endif %}
                    </td>
                    <td>
                      {% if penalty.severity == 'low' %}
                        منخفض
                      {% elif penalty.severity == 'medium' %}
                        متوسط
                      {% elif penalty.severity == 'high' %}
                        عالي
                      {% elif penalty.severity == 'critical' %}
                        حرج
                      {% endif %}
                    </td>
                    <td>
                      {% if penalty.status == 'active' %}
                        نشط
                      {% elif penalty.status == 'completed' %}
                        مكتمل
                      {% elif penalty.status == 'cancelled' %}
                        ملغي
                      {% endif %}
                    </td>
                    <td class="no-print">
                      <small>{{ penalty.description|truncate(100) }}</small>
                    </td>
                  </tr>
                  {% else %}
                  <tr>
                    <td colspan="9" class="text-center text-muted">
                      <i class="fas fa-inbox fa-2x mb-2"></i><br>
                      لا توجد جزاءات في الفترة المحددة
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>

            <!-- ملخص الإحصائيات للطباعة -->
            <div class="print-only mt-4">
              <h4>ملخص الإحصائيات:</h4>
              <div class="row">
                <div class="col-md-6">
                  <ul class="list-unstyled">
                    <li><strong>إجمالي الجزاءات:</strong> {{ stats.total_penalties }}</li>
                    <li><strong>الجزاءات النشطة:</strong> {{ stats.active_penalties }}</li>
                    <li><strong>الجزاءات المكتملة:</strong> {{ stats.completed_penalties }}</li>
                    <li><strong>الجزاءات الملغية:</strong> {{ stats.cancelled_penalties }}</li>
                  </ul>
                </div>
                <div class="col-md-6">
                  <ul class="list-unstyled">
                    <li><strong>الجزاءات المالية:</strong> {{ stats.financial_penalties_count }}</li>
                    <li><strong>إجمالي المبالغ:</strong> {{ stats.total_amount|round(2)|default(0) }} ريال</li>
                    <li><strong>المبالغ المدفوعة:</strong> {{ stats.paid_amount|round(2)|default(0) }} ريال</li>
                    <li><strong>المبالغ المعلقة:</strong> {{ stats.pending_amount|round(2)|default(0) }} ريال</li>
                  </ul>
                </div>
              </div>

              <h5>إحصائيات حسب النوع:</h5>
              <ul class="list-unstyled">
                {% for type_code, type_data in stats.type_stats %}
                <li><strong>{{ type_data.name }}:</strong> {{ type_data.count }}
                  {% if type_data.amount > 0 %}({{ type_data.amount|round(2)|default(0) }} ريال){% endif %}
                </li>
                {% endfor %}
              </ul>

              <h5>إحصائيات حسب الفئة:</h5>
              <ul class="list-unstyled">
                {% for cat_code, cat_data in stats.category_stats %}
                <li><strong>{{ cat_data.name }}:</strong> {{ cat_data.count }}</li>
                {% endfor %}
              </ul>

              <h5>إحصائيات حسب الخطورة:</h5>
              <ul class="list-unstyled">
                {% for sev_code, sev_data in stats.severity_stats %}
                <li><strong>{{ sev_data.name }}:</strong> {{ sev_data.count }}</li>
                {% endfor %}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .report-header {
    display: block !important;
  }
  
  .card-header,
  .card-tools,
  .btn,
  .sidebar,
  .main-header,
  .main-footer {
    display: none !important;
  }
  
  .content-wrapper {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .card {
    border: none !important;
    box-shadow: none !important;
  }
  
  .table {
    font-size: 12px;
  }
  
  .progress {
    display: none !important;
  }
}

.print-only {
  display: none;
}
</style>

<script>
function printReport() {
  // إظهار عناصر الطباعة
  document.querySelector('.report-header').style.display = 'block';
  
  // طباعة الصفحة
  window.print();
  
  // إخفاء عناصر الطباعة بعد الطباعة
  setTimeout(() => {
    document.querySelector('.report-header').style.display = 'none';
  }, 1000);
}

// تحسين عرض التقرير
document.addEventListener('DOMContentLoaded', function() {
  // إضافة أرقام الصفوف
  const rows = document.querySelectorAll('tbody tr');
  rows.forEach((row, index) => {
    if (!row.querySelector('td[colspan]')) {
      const firstCell = row.querySelector('td');
      if (firstCell) {
        firstCell.innerHTML = `<small class="text-muted">${index + 1}.</small> ${firstCell.innerHTML}`;
      }
    }
  });

  // تطبيق عرض progress bars من data attributes
  const progressBars = document.querySelectorAll('.progress-bar[data-count][data-total]');
  progressBars.forEach(bar => {
    const count = parseInt(bar.getAttribute('data-count')) || 0;
    const total = parseInt(bar.getAttribute('data-total')) || 1;
    const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
    bar.style.width = percentage + '%';
  });
});
</script>
{% endblock %}
