#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استعلام الجزاءات
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from django.shortcuts import Flask
    from flask_sqlalchemy import SQLAlchemy
    from dotenv import load_dotenv
    
    # تحميل متغيرات البيئة
    load_dotenv()
    
    # إنشاء تطبيق Flask
    app = Flask(__name__)
    
    # إعدادات التطبيق
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///legal_system.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    print(f"🔍 SQLALCHEMY_DATABASE_URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
    
    # تهيئة قاعدة البيانات
    db = SQLAlchemy(app)
    
    # إنشاء النماذج
    from models import create_models
    models_dict = create_models(db)
    Penalty = models_dict['Penalty']
    Employee = models_dict['Employee']
    
    with app.app_context():
        try:
            print("🔍 اختبار استعلام الجزاءات...")
            
            # اختبار الاستعلام الذي يسبب المشكلة
            employee_id = 6  # نفس المعرف من رسالة الخطأ
            
            print(f"🔍 البحث عن جزاءات الموظف رقم: {employee_id}")
            
            # محاولة تنفيذ الاستعلام
            penalties = Penalty.query.filter_by(employee_id=employee_id).order_by(Penalty.penalty_date.desc()).all()
            
            print(f"✅ تم تنفيذ الاستعلام بنجاح")
            print(f"📊 عدد الجزاءات الموجودة: {len(penalties)}")
            
            # عرض تفاصيل الجزاءات
            for penalty in penalties:
                print(f"  - الجزاء {penalty.id}: {penalty.title} ({penalty.category})")
            
            # اختبار استعلام عام
            print("\n🔍 اختبار استعلام عام للجزاءات...")
            all_penalties = Penalty.query.all()
            print(f"📊 إجمالي عدد الجزاءات: {len(all_penalties)}")
            
            for penalty in all_penalties:
                print(f"  - الجزاء {penalty.id}: {penalty.title} (الموظف: {penalty.employee_id}, الفئة: {penalty.category})")
            
            # اختبار الأعمدة
            print("\n🔍 اختبار الوصول للأعمدة...")
            if all_penalties:
                penalty = all_penalties[0]
                print(f"  - ID: {penalty.id}")
                print(f"  - Employee ID: {penalty.employee_id}")
                print(f"  - Penalty Type: {penalty.penalty_type}")
                print(f"  - Category: {penalty.category}")
                print(f"  - Title: {penalty.title}")
                print(f"  - Description: {penalty.description}")
                print(f"  - Amount: {penalty.amount}")
                print(f"  - Status: {penalty.status}")
            
        except Exception as e:
            print(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            import traceback
            traceback.print_exc()
            
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
