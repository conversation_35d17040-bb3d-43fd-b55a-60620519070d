{% extends "base.html" %} {% block title %}{{ client.full_name }} - تفاصيل
العميل{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2">
    <i class="fas fa-user me-2"></i>
    تفاصيل العميل: {{ client.full_name }}
  </h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <a
        href="{{ url_for('clients.index' }}"
        class="btn btn-outline-secondary"
      >
        <i class="fas fa-arrow-right me-1"></i>
        العودة للقائمة
      </a>
      <a
        href="{{ url_for('clients.edit', id=client.id }}"
        class="btn btn-warning"
      >
        <i class="fas fa-edit me-1"></i>
        تعديل
      </a>
      <a
        href="{{ url_for('clients.files', id=client.id }}"
        class="btn btn-success"
      >
        <i class="fas fa-folder-open me-1"></i>
        الملفات
      </a>
      <button onclick="printClient()" class="btn btn-info">
        <i class="fas fa-print me-1"></i>
        طباعة
      </button>
    </div>
  </div>
</div>

<div class="row">
  <!-- معلومات العميل الأساسية -->
  <div class="col-md-8">
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-info-circle me-2"></i>
          معلومات العميل الأساسية
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-borderless">
              <tr>
                <td><strong>رمز العميل:</strong></td>
                <td>{{ client.client_code }}</td>
              </tr>
              <tr>
                <td><strong>الاسم الكامل:</strong></td>
                <td>{{ client.full_name }}</td>
              </tr>
              <tr>
                <td><strong>نوع العميل:</strong></td>
                <td>
                  {% if client.client_type == 'individual' %}
                  <span class="badge bg-info">فرد</span>
                  {% elif client.client_type == 'company' %}
                  <span class="badge bg-warning">شركة</span>
                  {% elif client.client_type == 'organization' %}
                  <span class="badge bg-secondary">مؤسسة</span>
                  {% else %}
                  <span class="badge bg-light text-dark"
                    >{{ client.client_type or 'غير محدد' }}</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>البريد الإلكتروني:</strong></td>
                <td>
                  {% if client.email %}
                  <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                  {% else %}
                  <span class="text-muted">غير محدد</span>
                  {% endif %}
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-borderless">
              <tr>
                <td><strong>رقم الهاتف:</strong></td>
                <td>
                  {% if client.phone %}
                  <a href="tel:{{ client.phone }}">{{ client.phone }}</a>
                  {% else %}
                  <span class="text-muted">غير محدد</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>رقم الهوية:</strong></td>
                <td>{{ client.national_id or 'غير محدد' }}</td>
              </tr>
              <tr>
                <td><strong>الحالة:</strong></td>
                <td>
                  {% if client.is_active %}
                  <span class="badge bg-success">نشط</span>
                  {% else %}
                  <span class="badge bg-danger">غير نشط</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>تاريخ الإنشاء:</strong></td>
                <td>
                  {{ client.created_at.created_at else 'غير محدد'|strftime("%Y-%m-%d %H:%M") if client }}
                </td>
              </tr>
            </table>
          </div>
        </div>

        {% if client.address %}
        <div class="row mt-3">
          <div class="col-12">
            <h6><strong>العنوان:</strong></h6>
            <p class="text-muted">{{ client.address }}</p>
          </div>
        </div>
        {% endif %} {% if client.notes %}
        <div class="row mt-3">
          <div class="col-12">
            <h6><strong>ملاحظات:</strong></h6>
            <p class="text-muted">{{ client.notes }}</p>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- قضايا العميل -->
    {% if cases %}
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-gavel me-2"></i>
          قضايا العميل ({{ cases|length }}
        </h5>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>رقم القضية</th>
                <th>عنوان القضية</th>
                <th>نوع القضية</th>
                <th>الحالة</th>
                <th>تاريخ الإنشاء</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {% for case in cases %}
              <tr>
                <td><strong>{{ case.case_number }}</strong></td>
                <td>{{ case.case_title }}</td>
                <td>{{ case.case_type or 'غير محدد' }}</td>
                <td>
                  {% if case.case_status == 'active' %}
                  <span class="badge bg-success">نشطة</span>
                  {% elif case.case_status == 'closed' %}
                  <span class="badge bg-secondary">مغلقة</span>
                  {% elif case.case_status == 'pending' %}
                  <span class="badge bg-warning">معلقة</span>
                  {% else %}
                  <span class="badge bg-light text-dark"
                    >{{ case.case_status or 'غير محدد' }}</span>
                  {% endif %}
                </td>
                <td>
                  {{ case.created_at.created_at else 'غير محدد'|strftime("%Y-%m-%d") if case }}
                </td>
                <td>
                  <a
                    href="{{ url_for('cases.view', id=case.id }}"
                    class="btn btn-sm btn-outline-primary"
                  >
                    <i class="fas fa-eye"></i>
                  </a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- عقود العميل -->
    {% if contracts %}
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-file-contract me-2"></i>
          عقود العميل ({{ contracts|length }}
        </h5>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>رقم العقد</th>
                <th>عنوان العقد</th>
                <th>النوع</th>
                <th>المبلغ</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {% for contract in contracts %}
              <tr>
                <td><strong>{{ contract.contract_number }}</strong></td>
                <td>{{ contract.title }}</td>
                <td>{{ contract.contract_type or 'غير محدد' }}</td>
                <td>
                  {{ "{:,.0f}".format(contract.amount if contract.amount else
                  'غير محدد' ) }} ريال
                </td>
                <td>
                  {% if contract.status == 'active' %}
                  <span class="badge bg-success">نشط</span>
                  {% elif contract.status == 'completed' %}
                  <span class="badge bg-info">مكتمل</span>
                  {% elif contract.status == 'cancelled' %}
                  <span class="badge bg-danger">ملغي</span>
                  {% else %}
                  <span class="badge bg-warning"
                    >{{ contract.status or 'مسودة' }}</span>
                  {% endif %}
                </td>
                <td>
                  <a
                    href="{{ url_for('contracts.view', id=contract.id }}"
                    class="btn btn-sm btn-outline-primary"
                  >
                    <i class="fas fa-eye"></i>
                  </a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- ملفات العميل -->
    {% if client_files %}
    <div class="card mb-4">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-folder-open me-2"></i>
            ملفات العميل (آخر 5 ملفات)
          </h5>
          <a
            href="{{ url_for('clients.files', id=client.id }}"
            class="btn btn-sm btn-outline-primary"
          >
            عرض جميع الملفات
          </a>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-sm">
            <thead>
              <tr>
                <th>اسم الملف</th>
                <th>النوع</th>
                <th>الحجم</th>
                <th>تاريخ الرفع</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {% for file in client_files %}
              <tr>
                <td>
                  <i
                    class="fas fa-file-{{ get_file_icon(file.file_type }} me-2"
                  ></i>
                  {{ file.original_filename }}
                </td>
                <td>
                  <span class="badge bg-secondary"
                    >{{ file.file_type.upper() if file.file_type else 'N/A' }}</span>
                </td>
                <td>{{ format_file_size(file.file_size }}</td>
                <td>
                  {{ file.created_at.created_at else 'غير محدد'|strftime("%Y-%m-%d") if file }}
                </td>
                <td>
                  <a
                    href="{{ url_for('clients.download_file', client_id=client.id, file_id=file.id }}"
                    class="btn btn-sm btn-outline-primary"
                    title="تحميل"
                  >
                    <i class="fas fa-download"></i>
                  </a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- الإحصائيات والمعلومات الجانبية -->
  <div class="col-md-4">
    <!-- إحصائيات سريعة -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-bar me-2"></i>
          إحصائيات العميل
        </h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-6 mb-3">
            <div class="border rounded p-2">
              <h4 class="text-primary">{{ stats.cases_count }}</h4>
              <small class="text-muted">إجمالي القضايا</small>
            </div>
          </div>
          <div class="col-6 mb-3">
            <div class="border rounded p-2">
              <h4 class="text-success">{{ stats.active_cases }}</h4>
              <small class="text-muted">القضايا النشطة</small>
            </div>
          </div>
          <div class="col-6 mb-3">
            <div class="border rounded p-2">
              <h4 class="text-info">{{ stats.contracts_count }}</h4>
              <small class="text-muted">العقود</small>
            </div>
          </div>
          <div class="col-6 mb-3">
            <div class="border rounded p-2">
              <h4 class="text-warning">{{ stats.appointments_count }}</h4>
              <small class="text-muted">المواعيد</small>
            </div>
          </div>
          <div class="col-6 mb-3">
            <div class="border rounded p-2">
              <h4 class="text-dark">{{ stats.files_count }}</h4>
              <small class="text-muted">الملفات</small>
            </div>
          </div>
          <div class="col-6 mb-3">
            <div class="border rounded p-2">
              <h4 class="text-secondary">{{ stats.active_contracts }}</h4>
              <small class="text-muted">العقود النشطة</small>
            </div>
          </div>
        </div>

        <hr />

        <div class="text-center">
          <h6>الإحصائيات المالية</h6>
          <p>
            <strong>إجمالي الفواتير:</strong> {{ "{:,.0f}".format(stats.total_amount ) }} ريال
          </p>
          <p>
            <strong>المبلغ المدفوع:</strong> {{ "{:,.0f}".format(stats.paid_amount ) }} ريال
          </p>
          <p>
            <strong>المبلغ المعلق:</strong> {{ "{:,.0f}".format(stats.pending_amount ) }} ريال
          </p>
        </div>
      </div>
    </div>

    <!-- إجراءات سريعة -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-tools me-2"></i>
          إجراءات سريعة
        </h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a
            href="{{ url_for('cases.add' }}?client_id={{ client.id }}"
            class="btn btn-outline-primary"
          >
            <i class="fas fa-plus me-1"></i>
            إضافة قضية جديدة
          </a>
          <a
            href="{{ url_for('contracts.add' }}?client_id={{ client.id }}"
            class="btn btn-outline-success"
          >
            <i class="fas fa-file-contract me-1"></i>
            إضافة عقد جديد
          </a>
          <a
            href="{{ url_for('appointments.add' }}?client_id={{ client.id }}"
            class="btn btn-outline-info"
          >
            <i class="fas fa-calendar-plus me-1"></i>
            حجز موعد
          </a>
          <a
            href="{{ url_for('invoices.add' }}?client_id={{ client.id }}"
            class="btn btn-outline-warning"
          >
            <i class="fas fa-file-invoice me-1"></i>
            إنشاء فاتورة
          </a>
        </div>
      </div>
    </div>

    <!-- معلومات النظام -->
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-info me-2"></i>
          معلومات النظام
        </h5>
      </div>
      <div class="card-body">
        <small class="text-muted">
          <p>
            <strong>تاريخ الإنشاء:</strong><br />{{ client.created_at.created_at else 'غير محدد'|strftime("%Y-%m-%d %H:%M:%S") if client }}
          </p>
          {% if client.updated_at %}
          <p>
            <strong>آخر تحديث:</strong><br />{{ client.updated_at.strftime("%Y-%m-%d %H:%M:%S") if client.updated_at else "-" }}
          </p>
          {% endif %}
        </small>
      </div>
    </div>
  </div>
</div>

<script>
  function printClient() {
    window.print();
  }
</script>

<style>
  @media print {
    .btn-toolbar,
    .no-print {
      display: none !important;
    }

    .card {
      border: 1px solid #ddd !important;
      box-shadow: none !important;
      break-inside: avoid;
    }

    .card-header {
      background-color: #f8f9fa !important;
      color: #000 !important;
    }
  }
</style>
{% endblock %}
