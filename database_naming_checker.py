#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص مسميات قواعد البيانات للتأكد من عدم التشابه والتضارب
"""

import os
import re
import sqlite3
from collections import defaultdict, Counter

def extract_table_names_from_models():
    """استخراج أسماء الجداول من ملف النماذج"""
    
    print("🔍 فحص أسماء الجداول في models.py...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن __tablename__
        table_pattern = r"__tablename__\s*=\s*['\"]([^'\"]+)['\"]"
        tables = re.findall(table_pattern, content)
        
        # البحث عن أسماء الكلاسات
        class_pattern = r"class\s+(\w+)\(.*db\.Model.*\):"
        classes = re.findall(class_pattern, content)
        
        return {
            'tables': tables,
            'classes': classes
        }
        
    except Exception as e:
        print(f"❌ خطأ في قراءة models.py: {e}")
        return {'tables': [], 'classes': []}

def check_database_tables():
    """فحص الجداول الموجودة في قاعدة البيانات"""
    
    print("\n🔍 فحص الجداول في قاعدة البيانات...")
    
    db_files = ['legal_system.db', 'instance/legal_system.db']
    all_tables = []
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                print(f"📋 {db_file}: {len(tables)} جدول")
                for table in tables:
                    print(f"  - {table}")
                
                all_tables.extend(tables)
                conn.close()
                
            except Exception as e:
                print(f"❌ خطأ في فحص {db_file}: {e}")
    
    return all_tables

def check_field_names():
    """فحص أسماء الحقول في النماذج"""
    
    print("\n🔍 فحص أسماء الحقول...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن تعريفات الحقول
        field_pattern = r"(\w+)\s*=\s*db\.Column"
        fields = re.findall(field_pattern, content)
        
        # تجميع الحقول حسب التكرار
        field_counts = Counter(fields)
        
        return field_counts
        
    except Exception as e:
        print(f"❌ خطأ في فحص الحقول: {e}")
        return Counter()

def check_foreign_keys():
    """فحص المفاتيح الخارجية"""
    
    print("\n🔍 فحص المفاتيح الخارجية...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن ForeignKey
        fk_pattern = r"db\.ForeignKey\(['\"]([^'\"]+)['\"]"
        foreign_keys = re.findall(fk_pattern, content)
        
        return foreign_keys
        
    except Exception as e:
        print(f"❌ خطأ في فحص المفاتيح الخارجية: {e}")
        return []

def analyze_naming_conflicts():
    """تحليل تضارب الأسماء"""
    
    print("\n📊 تحليل تضارب الأسماء...")
    
    # جمع البيانات
    model_data = extract_table_names_from_models()
    db_tables = check_database_tables()
    field_counts = check_field_names()
    foreign_keys = check_foreign_keys()
    
    conflicts = []
    warnings = []
    
    # فحص تضارب أسماء الجداول
    table_counts = Counter(model_data['tables'])
    for table, count in table_counts.items():
        if count > 1:
            conflicts.append(f"جدول مكرر: {table} ({count} مرة)")
    
    # فحص تضارب أسماء الكلاسات
    class_counts = Counter(model_data['classes'])
    for class_name, count in class_counts.items():
        if count > 1:
            conflicts.append(f"كلاس مكرر: {class_name} ({count} مرة)")
    
    # فحص الحقول المكررة (تحذيرات فقط)
    common_fields = ['id', 'created_at', 'updated_at', 'is_active']
    for field, count in field_counts.items():
        if field not in common_fields and count > 3:
            warnings.append(f"حقل مكرر كثيراً: {field} ({count} مرة)")
    
    # فحص المفاتيح الخارجية
    fk_counts = Counter(foreign_keys)
    for fk, count in fk_counts.items():
        if count > 5:
            warnings.append(f"مفتاح خارجي مستخدم كثيراً: {fk} ({count} مرة)")
    
    return {
        'conflicts': conflicts,
        'warnings': warnings,
        'tables': model_data['tables'],
        'classes': model_data['classes'],
        'db_tables': db_tables,
        'field_counts': field_counts,
        'foreign_keys': foreign_keys
    }

def generate_naming_report():
    """إنشاء تقرير شامل عن الأسماء"""
    
    print("📋 إنشاء تقرير شامل...")
    
    analysis = analyze_naming_conflicts()
    
    report = []
    report.append("=" * 60)
    report.append("📊 تقرير فحص مسميات قواعد البيانات")
    report.append("=" * 60)
    
    # ملخص عام
    report.append(f"\n📈 ملخص عام:")
    report.append(f"  - عدد الجداول في النماذج: {len(analysis['tables'])}")
    report.append(f"  - عدد الكلاسات: {len(analysis['classes'])}")
    report.append(f"  - عدد الجداول في قاعدة البيانات: {len(analysis['db_tables'])}")
    report.append(f"  - عدد الحقول الفريدة: {len(analysis['field_counts'])}")
    report.append(f"  - عدد المفاتيح الخارجية: {len(analysis['foreign_keys'])}")
    
    # التضارب
    if analysis['conflicts']:
        report.append(f"\n❌ تضارب في الأسماء ({len(analysis['conflicts'])}):")
        for conflict in analysis['conflicts']:
            report.append(f"  - {conflict}")
    else:
        report.append(f"\n✅ لا يوجد تضارب في الأسماء")
    
    # التحذيرات
    if analysis['warnings']:
        report.append(f"\n⚠️ تحذيرات ({len(analysis['warnings'])}):")
        for warning in analysis['warnings']:
            report.append(f"  - {warning}")
    else:
        report.append(f"\n✅ لا توجد تحذيرات")
    
    # تفاصيل الجداول
    report.append(f"\n📋 أسماء الجداول:")
    for table in sorted(set(analysis['tables'])):
        report.append(f"  - {table}")
    
    # الحقول الأكثر استخداماً
    report.append(f"\n🔤 الحقول الأكثر استخداماً:")
    for field, count in analysis['field_counts'].most_common(10):
        report.append(f"  - {field}: {count} مرة")
    
    return "\n".join(report)

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء فحص مسميات قواعد البيانات...")
    
    # إنشاء التقرير
    report = generate_naming_report()
    
    # طباعة التقرير
    print(report)
    
    # حفظ التقرير
    with open('database_naming_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 تم حفظ التقرير في: database_naming_report.txt")
    
    # التحقق من النتائج
    analysis = analyze_naming_conflicts()
    
    if not analysis['conflicts']:
        print("\n🎉 ممتاز! لا يوجد تضارب في مسميات قواعد البيانات")
        return True
    else:
        print(f"\n⚠️ تم العثور على {len(analysis['conflicts'])} تضارب يحتاج إصلاح")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
