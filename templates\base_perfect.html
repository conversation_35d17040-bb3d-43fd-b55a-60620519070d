<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الشؤون القانونية{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .navbar-brand { font-weight: bold; }
        .card { 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            border: none; 
            border-radius: 15px;
        }
        .stats-card { 
            transition: all 0.3s ease; 
            cursor: pointer;
        }
        .stats-card:hover { 
            transform: translateY(-10px) scale(1.02); 
            box-shadow: 0 20px 40px rgba(0,0,0,0.3); 
        }
        .stats-number { 
            font-size: 2.5rem; 
            font-weight: bold; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .stats-label { 
            font-size: 1rem; 
            opacity: 0.9; 
        }
        .container-fluid {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark shadow-lg">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-balance-scale me-2"></i>نظام الشؤون القانونية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        {% block content %}{% endblock %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>