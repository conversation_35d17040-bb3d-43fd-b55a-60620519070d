#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل وكامل لجميع ملفات HTML
"""

import os
import re
import glob
import shutil
from datetime import datetime

def comprehensive_html_fix():
    """إصلاح شامل لجميع ملفات HTML"""
    
    print("🔥 بدء الإصلاح الشامل لجميع ملفات HTML...")
    
    # إنشاء نسخة احتياطية
    backup_dir = f"complete_backup_{datetime.now().strftime('%H%M%S')}"
    try:
        if os.path.exists('templates'):
            shutil.copytree('templates', backup_dir)
            print(f"💾 نسخة احتياطية: {backup_dir}")
    except:
        pass
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # إصلاحات شاملة وكاملة
            
            # 1. إصلاح JavaScript
            content = re.sub(r'if\s*\(\s*!([^)]+)\s+\{', r'if (!\1) {', content)
            content = re.sub(r'if\s*\(\s*([^)]+)\s+\{', r'if (\1) {', content)
            content = re.sub(r'while\s*\(\s*([^)]+)\s+\{', r'while (\1) {', content)
            content = re.sub(r'for\s*\(\s*([^)]+)\s+\{', r'for (\1) {', content)
            content = re.sub(r'function\s*\(\s*([^)]*)\s+\{', r'function (\1) {', content)
            
            # 2. إصلاح الأقواس المفقودة في Jinja2
            content = re.sub(r'\{\{\s*([^}]+?)\s+\}\}', r'{{ \1 }}', content)
            content = re.sub(r'\{\%\s*([^%]+?)\s+\%\}', r'{% \1 %}', content)
            
            # 3. إصلاح الأقواس المفقودة في الدوال
            content = re.sub(r'\|default\(([^)]*)\s+\}\}', r'|default(\1) }}', content)
            content = re.sub(r'\.format\(([^)]*)\s+\}\}', r'.format(\1) }}', content)
            content = re.sub(r'\.strftime\(([^)]*)\s+\}\}', r'.strftime(\1) }}', content)
            content = re.sub(r'\|round\(([^)]*)\s+\}\}', r'|round(\1) }}', content)
            content = re.sub(r'\|truncate\(([^)]*)\s+\}\}', r'|truncate(\1) }}', content)
            
            # 4. إصلاح علامات HTML المكسورة
            content = re.sub(r'</(\w+)\s*>', r'</\1>', content)
            content = re.sub(r'<(\w+)([^>]*?)>\s*>', r'<\1\2>', content)
            content = re.sub(r'<\s*>', r'', content)
            content = re.sub(r'>\s*<\s*', r'><', content)
            
            # 5. إصلاح التعبيرات الشرطية المقسمة
            content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s*\n\s*([^}]+?)\s*\}\}', 
                           r'{{ \1 if \2 else \3 }}', content, flags=re.MULTILINE)
            content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', 
                           r'{{ \1 if \2 else \3 }}', content)
            
            # 6. إصلاح الأسطر المقسمة
            content = re.sub(r'\{\{\s*([^}]+?)\n\s*([^}]+?)\s*\}\}', r'{{ \1 \2 }}', content, flags=re.MULTILINE)
            content = re.sub(r'\{\%\s*([^%]+?)\n\s*([^%]+?)\s*\%\}', r'{% \1 \2 %}', content, flags=re.MULTILINE)
            
            # 7. إصلاح الأقواس المضاعفة
            content = re.sub(r'\{\{\s*\{\{', r'{{', content)
            content = re.sub(r'\}\}\s*\}\}', r'}}', content)
            content = re.sub(r'\{\%\s*\{\%', r'{%', content)
            content = re.sub(r'\%\}\s*\%\}', r'%}', content)
            
            # 8. إصلاح المسافات الإضافية
            content = re.sub(r'\{\{\s{2,}', r'{{ ', content)
            content = re.sub(r'\s{2,}\}\}', r' }}', content)
            content = re.sub(r'\{\%\s{2,}', r'{% ', content)
            content = re.sub(r'\s{2,}\%\}', r' %}', content)
            
            # 9. إصلاحات نصية مباشرة شاملة
            direct_fixes = [
                # JavaScript fixes
                ('if (!regex.test(username) {', 'if (!regex.test(username)) {'),
                ('if (condition {', 'if (condition) {'),
                ('while (condition {', 'while (condition) {'),
                ('for (condition {', 'for (condition) {'),
                ('function (params {', 'function (params) {'),
                
                # الأقواس المفقودة
                ('|default(0 }}', '|default(0) }}'),
                ('|default(1 }}', '|default(1) }}'),
                ('|default("" }}', '|default("") }}'),
                ('|default(\'\' }}', '|default(\'\') }}'),
                ('.format(total_revenue }}', '.format(total_revenue) }}'),
                ('.format(pending_revenue }}', '.format(pending_revenue) }}'),
                ('.format(total_cases }}', '.format(total_cases) }}'),
                ('.format(active_cases }}', '.format(active_cases) }}'),
                ('.format(completed_cases }}', '.format(completed_cases) }}'),
                ('.format(total_clients }}', '.format(total_clients) }}'),
                ('.format(total_employees }}', '.format(total_employees) }}'),
                ('.format(paid_invoices }}', '.format(paid_invoices) }}'),
                ('.format(pending_invoices }}', '.format(pending_invoices) }}'),
                ('.format(overdue_invoices }}', '.format(overdue_invoices) }}'),
                
                # علامات HTML مكسورة
                ('</small>', '</small>'),
                ('</div>', '</div>'),
                ('</span>', '</span>'),
                ('</p>', '</p>'),
                ('</h1>', '</h1>'),
                ('</h2>', '</h2>'),
                ('</h3>', '</h3>'),
                ('</h4>', '</h4>'),
                ('</h5>', '</h5>'),
                ('</h6>', '</h6>'),
                ('</a>', '</a>'),
                ('</button>', '</button>'),
                ('</form>', '</form>'),
                ('</table>', '</table>'),
                ('</tr>', '</tr>'),
                ('</td>', '</td>'),
                ('</th>', '</th>'),
                ('</li>', '</li>'),
                ('</ul>', '</ul>'),
                ('</ol>', '</ol>'),
                ('</script>', '</script>'),
                ('</style>', '</style>'),
                
                # أقواس إضافية
                ('))', ')'),
                ('((', '('),
                ('}})', '}}'),
                ('{{{', '{{'),
                ('%%}', '%}'),
                ('{%%', '{%'),
                
                # مسافات إضافية
                ('  }}', ' }}'),
                ('{{  ', '{{ '),
                ('  %}', ' %}'),
                ('{%  ', '{% '),
                ('  >', '>'),
                ('<  ', '<'),
                
                # علامات مختلطة
                ('{% url ', '{{ url_for('),
                (' %}"', ') }}"'),
                ('{% static ', '{{ url_for("static", filename='),
                
                # تعبيرات شرطية مقسمة
                ('if user.email else\n                user.username', 'if user.email else user.username'),
                ('if user.full_name else\n                user.username', 'if user.full_name else user.username'),
                ('if employee.user.email else\n                employee.user.username', 'if employee.user.email else employee.user.username'),
                ('if case.created_at else\n                "-"', 'if case.created_at else "-"'),
                ('if contract.end_date else\n                "-"', 'if contract.end_date else "-"'),
                
                # إصلاحات خاصة
                ('> >', '>'),
                ('< <', '<'),
                ('>&nbsp;', '> '),
                ('&nbsp;<', ' <'),
                
                # إصلاحات dashboard محددة
                ('case.created_at else "-")', 'case.created_at else "-"'),
                ('employee.hire_date else "-")', 'employee.hire_date else "-"'),
                ('contract.start_date else "-")', 'contract.start_date else "-"'),
                
                # إصلاحات فلاتر
                ('|date:"', '|strftime("'),
                ('|floatformat:', '|round('),
                ('|truncatechars:', '|truncate('),
                ('|default:', '|default('),
                
                # إصلاح format مضاعف
                ('format(format(', 'format('),
                ('.format(.format(', '.format('),
                
                # إصلاحات CSS
                ('};', '};'),
                ('});', '});'),
                ('{;', '{'),
                (';}', '}'),
                (';;', ';'),
                
                # إصلاحات عامة
                ('else "-"  }}', 'else "-" }}'),
                ('case.lawyer  %}', 'case.lawyer %}'),
                ('endif  %}', 'endif %}'),
                ('case.client.full_name  }}', 'case.client.full_name }}'),
                ('case.lawyer.user.full_name   }}', 'case.lawyer.user.full_name }}'),
            ]
            
            for old, new in direct_fixes:
                content = content.replace(old, new)
            
            # حفظ الملف إذا تم تغييره
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  🔧 {file_path}: تم إصلاحه")
                total_fixes += 1
            
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} ملف HTML")
    return total_fixes

def validate_all_html():
    """التحقق من جميع ملفات HTML"""
    
    print("\n🔍 التحقق من جميع ملفات HTML...")
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        template_files = glob.glob("templates/**/*.html", recursive=True)
        
        valid_count = 0
        error_count = 0
        errors = []
        
        for file_path in template_files:
            try:
                relative_path = os.path.relpath(file_path, 'templates').replace('\\', '/')
                template = env.get_template(relative_path)
                valid_count += 1
                
            except TemplateSyntaxError as e:
                error_count += 1
                error_msg = str(e)[:80] + "..." if len(str(e)) > 80 else str(e)
                errors.append(f"{relative_path}: {error_msg}")
            except Exception as e:
                error_count += 1
                error_msg = str(e)[:80] + "..." if len(str(e)) > 80 else str(e)
                errors.append(f"{relative_path}: {error_msg}")
        
        print(f"📊 نتائج التحقق:")
        print(f"  ✅ ملفات صحيحة: {valid_count}")
        print(f"  ❌ ملفات بها أخطاء: {error_count}")
        
        if errors and len(errors) <= 5:
            print(f"\n❌ الأخطاء المتبقية:")
            for error in errors:
                print(f"  - {error}")
        elif errors:
            print(f"\n❌ أول 3 أخطاء من {len(errors)} خطأ:")
            for error in errors[:3]:
                print(f"  - {error}")
        
        return error_count == 0, errors
        
    except ImportError:
        print("❌ لا يمكن التحقق من بناء الجملة")
        return False, ["لا يمكن التحقق"]

def create_perfect_templates():
    """إنشاء قوالب مثالية تعمل بشكل مضمون"""
    
    print("\n✨ إنشاء قوالب مثالية...")
    
    # قالب أساسي مثالي
    perfect_base = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الشؤون القانونية{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .navbar-brand { font-weight: bold; }
        .card { 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            border: none; 
            border-radius: 15px;
        }
        .stats-card { 
            transition: all 0.3s ease; 
            cursor: pointer;
        }
        .stats-card:hover { 
            transform: translateY(-10px) scale(1.02); 
            box-shadow: 0 20px 40px rgba(0,0,0,0.3); 
        }
        .stats-number { 
            font-size: 2.5rem; 
            font-weight: bold; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .stats-label { 
            font-size: 1rem; 
            opacity: 0.9; 
        }
        .container-fluid {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark shadow-lg">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-balance-scale me-2"></i>نظام الشؤون القانونية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        {% block content %}{% endblock %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>'''
    
    # لوحة تحكم مثالية
    perfect_dashboard = '''{% extends "base_perfect.html" %}
{% block title %}لوحة التحكم المثالية{% endblock %}
{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-5 text-white text-center">
            <i class="fas fa-tachometer-alt me-3"></i>لوحة التحكم المثالية
        </h1>
    </div>
</div>

<div class="row g-4 mb-5">
    <div class="col-md-3">
        <div class="card stats-card bg-gradient text-white h-100" style="background: linear-gradient(45deg, #667eea, #764ba2);">
            <div class="card-body text-center p-4">
                <i class="fas fa-gavel fa-4x mb-3"></i>
                <div class="stats-number">{{ total_cases|default(0) }}</div>
                <div class="stats-label">إجمالي القضايا</div>
                <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                <small class="d-block">
                    النشطة: {{ active_cases|default(0) }} | المكتملة: {{ completed_cases|default(0) }}
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-gradient text-white h-100" style="background: linear-gradient(45deg, #f093fb, #f5576c);">
            <div class="card-body text-center p-4">
                <i class="fas fa-users fa-4x mb-3"></i>
                <div class="stats-number">{{ total_clients|default(0) }}</div>
                <div class="stats-label">إجمالي العملاء</div>
                <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                <small class="d-block">العملاء المسجلين في النظام</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-gradient text-white h-100" style="background: linear-gradient(45deg, #4facfe, #00f2fe);">
            <div class="card-body text-center p-4">
                <i class="fas fa-user-tie fa-4x mb-3"></i>
                <div class="stats-number">{{ total_employees|default(0) }}</div>
                <div class="stats-label">إجمالي الموظفين</div>
                <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                <small class="d-block">الموظفين العاملين</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-gradient text-white h-100" style="background: linear-gradient(45deg, #fa709a, #fee140);">
            <div class="card-body text-center p-4">
                <i class="fas fa-file-invoice fa-4x mb-3"></i>
                <div class="stats-number">{{ total_invoices|default(0) }}</div>
                <div class="stats-label">إجمالي الفواتير</div>
                <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                <small class="d-block">
                    مدفوعة: {{ paid_invoices|default(0) }} | معلقة: {{ pending_invoices|default(0) }}
                </small>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>الإحصائيات المالية</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h3 class="text-success">{{ total_revenue|default(0) }}</h3>
                        <small class="text-muted">إجمالي الإيرادات</small>
                    </div>
                    <div class="col-6">
                        <h3 class="text-warning">{{ pending_revenue|default(0) }}</h3>
                        <small class="text-muted">الإيرادات المعلقة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>مرحباً بك</h5>
            </div>
            <div class="card-body text-center">
                <i class="fas fa-heart fa-3x text-danger mb-3"></i>
                <h4>أهلاً وسهلاً</h4>
                <p class="text-muted">نظام الشؤون القانونية يعمل بشكل مثالي</p>
                <div class="badge bg-success fs-6">النظام مستقر ✓</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
    
    # حفظ القوالب المثالية
    perfect_templates = {
        'templates/base_perfect.html': perfect_base,
        'templates/dashboard_perfect.html': perfect_dashboard,
    }
    
    for file_path, content in perfect_templates.items():
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✨ تم إنشاء {file_path}")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء {file_path}: {e}")

def main():
    """الدالة الرئيسية للإصلاح الشامل"""
    
    print("🔥 بدء الإصلاح الشامل والكامل لجميع ملفات HTML...")
    
    # إصلاح شامل
    fixed_files = comprehensive_html_fix()
    
    # التحقق الشامل
    is_valid, errors = validate_all_html()
    
    # إنشاء قوالب مثالية
    create_perfect_templates()
    
    print("\n" + "="*60)
    print("🔥 تقرير الإصلاح الشامل والكامل")
    print("="*60)
    print(f"🔧 ملفات مُصلحة: {fixed_files}")
    print(f"📁 ملفات صحيحة: {len(glob.glob('templates/**/*.html', recursive=True)) - len(errors) if errors else 'جميع الملفات'}")
    print(f"❌ أخطاء متبقية: {len(errors) if errors else 0}")
    print(f"✨ قوالب مثالية: ✅ تم إنشاؤها")
    
    if is_valid:
        print("\n🎉 تم الإصلاح الشامل بنجاح!")
        print("✅ جميع ملفات HTML تعمل بشكل مثالي")
    else:
        print(f"\n⚠️ لا تزال هناك {len(errors)} أخطاء")
        print("✨ استخدم القوالب المثالية للعمل الفوري")
    
    print("="*60)
    
    return is_valid

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
