#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح فوري وشامل لجميع أخطاء ملفات HTML
"""

import os
import re
import glob
import shutil
from datetime import datetime

def emergency_html_fix():
    """إصلاح فوري لجميع أخطاء HTML"""
    
    print("🚨 بدء الإصلاح الفوري لجميع أخطاء HTML...")
    
    # إنشاء نسخة احتياطية سريعة
    backup_dir = f"html_backup_{datetime.now().strftime('%H%M%S')}"
    try:
        if os.path.exists('templates'):
            shutil.copytree('templates', backup_dir)
            print(f"💾 نسخة احتياطية: {backup_dir}")
    except:
        pass
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # إصلاحات فورية وشاملة
            
            # 1. إصلاح الأقواس المفقودة في Jinja2
            content = re.sub(r'\{\{\s*([^}]+?)\s+\}\}', r'{{ \1 }}', content)
            content = re.sub(r'\{\%\s*([^%]+?)\s+\%\}', r'{% \1 %}', content)
            
            # 2. إصلاح الأقواس المفقودة في الدوال
            content = re.sub(r'\|default\(([^)]*)\s+\}\}', r'|default(\1) }}', content)
            content = re.sub(r'\.format\(([^)]*)\s+\}\}', r'.format(\1) }}', content)
            content = re.sub(r'\.strftime\(([^)]*)\s+\}\}', r'.strftime(\1) }}', content)
            content = re.sub(r'\|round\(([^)]*)\s+\}\}', r'|round(\1) }}', content)
            content = re.sub(r'\|truncate\(([^)]*)\s+\}\}', r'|truncate(\1) }}', content)
            
            # 3. إصلاح علامات HTML المكسورة
            content = re.sub(r'</(\w+)\s*>', r'</\1>', content)
            content = re.sub(r'<(\w+)([^>]*?)>\s*>', r'<\1\2>', content)
            content = re.sub(r'<\s*>', r'', content)
            content = re.sub(r'>\s*<\s*', r'><', content)
            
            # 4. إصلاح التعبيرات الشرطية المقسمة
            content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s*\n\s*([^}]+?)\s*\}\}', 
                           r'{{ \1 if \2 else \3 }}', content, flags=re.MULTILINE)
            content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', 
                           r'{{ \1 if \2 else \3 }}', content)
            
            # 5. إصلاح الأسطر المقسمة
            content = re.sub(r'\{\{\s*([^}]+?)\n\s*([^}]+?)\s*\}\}', r'{{ \1 \2 }}', content, flags=re.MULTILINE)
            content = re.sub(r'\{\%\s*([^%]+?)\n\s*([^%]+?)\s*\%\}', r'{% \1 \2 %}', content, flags=re.MULTILINE)
            
            # 6. إصلاح الأقواس المضاعفة
            content = re.sub(r'\{\{\s*\{\{', r'{{', content)
            content = re.sub(r'\}\}\s*\}\}', r'}}', content)
            content = re.sub(r'\{\%\s*\{\%', r'{%', content)
            content = re.sub(r'\%\}\s*\%\}', r'%}', content)
            
            # 7. إصلاح المسافات الإضافية
            content = re.sub(r'\{\{\s{2,}', r'{{ ', content)
            content = re.sub(r'\s{2,}\}\}', r' }}', content)
            content = re.sub(r'\{\%\s{2,}', r'{% ', content)
            content = re.sub(r'\s{2,}\%\}', r' %}', content)
            
            # 8. إصلاحات نصية مباشرة للمشاكل الشائعة
            direct_fixes = [
                # الأقواس المفقودة
                ('|default(0 }}', '|default(0) }}'),
                ('|default(1 }}', '|default(1) }}'),
                ('|default("" }}', '|default("") }}'),
                ('|default(\'\' }}', '|default(\'\') }}'),
                ('.format(total_revenue }}', '.format(total_revenue) }}'),
                ('.format(pending_revenue }}', '.format(pending_revenue) }}'),
                ('.format(total_cases }}', '.format(total_cases) }}'),
                ('.format(active_cases }}', '.format(active_cases) }}'),
                ('.format(completed_cases }}', '.format(completed_cases) }}'),
                ('.format(total_clients }}', '.format(total_clients) }}'),
                ('.format(total_employees }}', '.format(total_employees) }}'),
                ('.format(paid_invoices }}', '.format(paid_invoices) }}'),
                ('.format(pending_invoices }}', '.format(pending_invoices) }}'),
                ('.format(overdue_invoices }}', '.format(overdue_invoices) }}'),
                
                # علامات HTML مكسورة
                ('</small>', '</small>'),
                ('</div>', '</div>'),
                ('</span>', '</span>'),
                ('</p>', '</p>'),
                ('</h1>', '</h1>'),
                ('</h2>', '</h2>'),
                ('</h3>', '</h3>'),
                ('</h4>', '</h4>'),
                ('</h5>', '</h5>'),
                ('</h6>', '</h6>'),
                ('</a>', '</a>'),
                ('</button>', '</button>'),
                ('</form>', '</form>'),
                ('</table>', '</table>'),
                ('</tr>', '</tr>'),
                ('</td>', '</td>'),
                ('</th>', '</th>'),
                ('</li>', '</li>'),
                ('</ul>', '</ul>'),
                ('</ol>', '</ol>'),
                
                # أقواس إضافية
                ('))', ')'),
                ('((', '('),
                ('}})', '}}'),
                ('{{{', '{{'),
                ('%%}', '%}'),
                ('{%%', '{%'),
                
                # مسافات إضافية
                ('  }}', ' }}'),
                ('{{  ', '{{ '),
                ('  %}', ' %}'),
                ('{%  ', '{% '),
                ('  >', '>'),
                ('<  ', '<'),
                
                # علامات مختلطة
                ('{% url ', '{{ url_for('),
                (' %}"', ') }}"'),
                ('{% static ', '{{ url_for("static", filename='),
                
                # تعبيرات شرطية مقسمة
                ('if user.email else\n                user.username', 'if user.email else user.username'),
                ('if user.full_name else\n                user.username', 'if user.full_name else user.username'),
                ('if employee.user.email else\n                employee.user.username', 'if employee.user.email else employee.user.username'),
                ('if case.created_at else\n                "-"', 'if case.created_at else "-"'),
                ('if contract.end_date else\n                "-"', 'if contract.end_date else "-"'),
                
                # إصلاحات خاصة
                ('> >', '>'),
                ('< <', '<'),
                ('>&nbsp;', '> '),
                ('&nbsp;<', ' <'),
                
                # إصلاحات dashboard محددة
                ('case.created_at else "-")', 'case.created_at else "-"'),
                ('employee.hire_date else "-")', 'employee.hire_date else "-"'),
                ('contract.start_date else "-")', 'contract.start_date else "-"'),
                
                # إصلاحات فلاتر
                ('|date:"', '|strftime("'),
                ('|floatformat:', '|round('),
                ('|truncatechars:', '|truncate('),
                ('|default:', '|default('),
                
                # إصلاح format مضاعف
                ('format(format(', 'format('),
                ('.format(.format(', '.format('),
            ]
            
            for old, new in direct_fixes:
                content = content.replace(old, new)
            
            # حفظ الملف إذا تم تغييره
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  ⚡ {file_path}: تم إصلاحه")
                total_fixes += 1
            
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} ملف HTML")
    return total_fixes

def validate_html_syntax():
    """التحقق السريع من صحة HTML"""
    
    print("\n🔍 التحقق من صحة HTML...")
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        
        critical_files = [
            'dashboard.html',
            'base.html', 
            'login.html',
            'admin/index.html',
            'cases/index.html',
            'clients/index.html',
            'employees/index.html',
        ]
        
        valid_count = 0
        error_count = 0
        
        for file_name in critical_files:
            if os.path.exists(f'templates/{file_name}'):
                try:
                    template = env.get_template(file_name)
                    print(f"  ✅ {file_name}: صحيح")
                    valid_count += 1
                    
                except TemplateSyntaxError as e:
                    print(f"  ❌ {file_name}: خطأ في بناء الجملة")
                    error_count += 1
                except Exception as e:
                    print(f"  ⚠️ {file_name}: خطأ في التحميل")
                    error_count += 1
        
        print(f"\n📊 النتائج:")
        print(f"  ✅ ملفات صحيحة: {valid_count}")
        print(f"  ❌ ملفات بها أخطاء: {error_count}")
        
        return error_count == 0
        
    except ImportError:
        print("❌ لا يمكن التحقق من بناء الجملة")
        return False

def create_emergency_templates():
    """إنشاء قوالب طوارئ تعمل بشكل مضمون"""
    
    print("\n🆘 إنشاء قوالب طوارئ...")
    
    # قالب أساسي للطوارئ
    emergency_base = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الشؤون القانونية{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa; }
        .navbar-brand { font-weight: bold; }
        .card { box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: none; }
        .stats-card { transition: all 0.3s ease; }
        .stats-card:hover { transform: translateY(-5px); box-shadow: 0 8px 15px rgba(0,0,0,0.2); }
        .stats-number { font-size: 2rem; font-weight: bold; }
        .stats-label { font-size: 0.9rem; opacity: 0.9; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-balance-scale me-2"></i>نظام الشؤون القانونية
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>'''
    
    # لوحة تحكم للطوارئ
    emergency_dashboard = '''{% extends "base_emergency.html" %}
{% block title %}لوحة التحكم{% endblock %}
{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4 text-primary">
            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
        </h1>
    </div>
</div>

<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card stats-card bg-primary text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-gavel fa-3x mb-3"></i>
                <div class="stats-number">{{ total_cases|default(0) }}</div>
                <div class="stats-label">إجمالي القضايا</div>
                <small class="d-block mt-2">
                    النشطة: {{ active_cases|default(0) }} | المكتملة: {{ completed_cases|default(0) }}
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-success text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x mb-3"></i>
                <div class="stats-number">{{ total_clients|default(0) }}</div>
                <div class="stats-label">إجمالي العملاء</div>
                <small class="d-block mt-2">العملاء المسجلين في النظام</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-info text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-user-tie fa-3x mb-3"></i>
                <div class="stats-number">{{ total_employees|default(0) }}</div>
                <div class="stats-label">إجمالي الموظفين</div>
                <small class="d-block mt-2">الموظفين العاملين</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card stats-card bg-warning text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-file-invoice fa-3x mb-3"></i>
                <div class="stats-number">{{ total_invoices|default(0) }}</div>
                <div class="stats-label">إجمالي الفواتير</div>
                <small class="d-block mt-2">
                    مدفوعة: {{ paid_invoices|default(0) }} | معلقة: {{ pending_invoices|default(0) }}
                </small>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>الإحصائيات المالية</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-success">{{ total_revenue|default(0) }}</h4>
                        <small class="text-muted">إجمالي الإيرادات</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ pending_revenue|default(0) }}</h4>
                        <small class="text-muted">الإيرادات المعلقة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>الأنشطة الحديثة</h5>
            </div>
            <div class="card-body">
                <p class="text-center text-muted">
                    <i class="fas fa-clock fa-2x mb-2"></i><br>
                    مرحباً بك في نظام الشؤون القانونية<br>
                    النظام يعمل بشكل مثالي
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
    
    # صفحة تسجيل دخول للطوارئ
    emergency_login = '''{% extends "base_emergency.html" %}
{% block title %}تسجيل الدخول{% endblock %}
{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول</h4>
            </div>
            <div class="card-body p-4">
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-1"></i>اسم المستخدم
                        </label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>كلمة المرور
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-sign-in-alt me-1"></i>دخول
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
    
    # حفظ قوالب الطوارئ
    emergency_templates = {
        'templates/base_emergency.html': emergency_base,
        'templates/dashboard_emergency.html': emergency_dashboard,
        'templates/login_emergency.html': emergency_login,
    }
    
    for file_path, content in emergency_templates.items():
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  🆘 تم إنشاء {file_path}")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء {file_path}: {e}")

def main():
    """الدالة الرئيسية للإصلاح الفوري"""
    
    print("🚨 بدء الإصلاح الفوري لجميع أخطاء HTML...")
    
    # إصلاح فوري
    fixed_files = emergency_html_fix()
    
    # التحقق السريع
    is_valid = validate_html_syntax()
    
    # إنشاء قوالب طوارئ
    create_emergency_templates()
    
    print("\n" + "="*50)
    print("🚨 تقرير الإصلاح الفوري")
    print("="*50)
    print(f"⚡ ملفات مُصلحة: {fixed_files}")
    print(f"📁 صحة بناء الجملة: {'✅ صحيح' if is_valid else '❌ يحتاج مراجعة'}")
    print(f"🆘 قوالب طوارئ: ✅ تم إنشاؤها")
    
    if is_valid:
        print("\n🎉 تم الإصلاح الفوري بنجاح!")
        print("✅ جميع ملفات HTML تعمل بشكل صحيح")
    else:
        print("\n⚠️ قد تحتاج بعض الملفات مراجعة إضافية")
        print("🆘 استخدم قوالب الطوارئ للعمل الفوري")
    
    print("="*50)
    
    return is_valid

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
