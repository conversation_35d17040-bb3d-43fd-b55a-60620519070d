{% extends "base.html" %}

{% block title %}{{ case.case_title }} - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open me-2"></i>
        {{ case.case_title }}
        <small class="text-muted">({{ case.case_number }}</small>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="#" onclick="alert('سيتم إضافة رابط التعديل قريباً')" class="btn btn-outline-primary">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            <a href="#" onclick="alert('سيتم إضافة رابط قائمة القضايا قريباً')" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة إلى القائمة
            </a>
        </div>
    </div>
</div>

<!-- معلومات القضية الأساسية -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات القضية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>رقم القضية:</strong>
                        <p class="mb-0">{{ case.case_number }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>نوع القضية:</strong>
                        <p class="mb-0">{{ case.case_type }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>العميل:</strong>
                        <p class="mb-0">{{ case.client.full_name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>المحامي المسؤول:</strong>
                        <p class="mb-0">{{ case.lawyer.user.full_name }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>حالة القضية:</strong>
                        <p class="mb-0">
                            {% if case.case_status == 'pending' %}
                                <span class="badge status-pending">معلقة</span>
                            {% elif case.case_status == 'active' %}
                                <span class="badge status-active">نشطة</span>
                            {% elif case.case_status == 'completed' %}
                                <span class="badge status-completed">مكتملة</span>
                            {% elif case.case_status == 'won' %}
                                <span class="badge bg-success">مكسوبة</span>
                            {% elif case.case_status == 'lost' %}
                                <span class="badge bg-danger">مخسورة</span>
                            {% else %}
                                <span class="badge status-cancelled">ملغية</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>المحكمة:</strong>
                        <p class="mb-0">{{ case.court_name or 'غير محدد' }}</p>
                    </div>
                </div>
                
                {% if case.case_value %}
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>قيمة القضية:</strong>
                        <p class="mb-0">{{ "{:,.2f}".format(case.case_value ) }} ريال</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>تاريخ رفع القضية:</strong>
                        <p class="mb-0">{{ case.filing_date.filing_date else 'غير محدد'|strftime("%Y-%m-%d") if case }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if case.case_description %}
                <div class="mb-3">
                    <strong>وصف القضية:</strong>
                    <p class="mb-0">{{ case.case_description }}</p>
                </div>
                {% endif %}
                
                {% if case.notes %}
                <div class="mb-3">
                    <strong>ملاحظات:</strong>
                    <p class="mb-0">{{ case.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h4 class="text-primary mb-1">{{ sessions|length }}</h4>
                            <small class="text-muted">الجلسات</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h4 class="text-success mb-1">{{ notes|length }}</h4>
                            <small class="text-muted">الملاحظات</small>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button onclick="alert('سيتم إضافة هذه الوظيفة قريباً')" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-calendar-plus me-1"></i>
                        إضافة جلسة
                    </button>
                    <button onclick="alert('سيتم إضافة هذه الوظيفة قريباً')" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-sticky-note me-1"></i>
                        إضافة ملاحظة
                    </button>
                    <button onclick="alert('سيتم إضافة هذه الوظيفة قريباً')" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-upload me-1"></i>
                        رفع مستند
                    </button>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    معلومات التوقيت
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>تاريخ الإنشاء:</strong><br>
                    {{ case.created_at.strftime("%Y-%m-%d %H:%M") if case.created_at else "-" }}
                </small>
                <hr>
                <small class="text-muted">
                    <strong>آخر تحديث:</strong><br>
                    {{ case.updated_at.strftime("%Y-%m-%d %H:%M") if case.updated_at else "-" }}
                </small>
            </div>
        </div>
    </div>
</div>

<!-- تبويبات المعلومات التفصيلية -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="caseDetailTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions" type="button" role="tab">
                    <i class="fas fa-gavel me-1"></i>
                    الجلسات ({{ sessions|length }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="notes-tab" data-bs-toggle="tab" data-bs-target="#notes" type="button" role="tab">
                    <i class="fas fa-sticky-note me-1"></i>
                    الملاحظات ({{ notes|length }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="timeline-tab" data-bs-toggle="tab" data-bs-target="#timeline" type="button" role="tab">
                    <i class="fas fa-history me-1"></i>
                    الجدول الزمني ({{ timeline|length }}
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="caseDetailTabsContent">
            <!-- تبويب الجلسات -->
            <div class="tab-pane fade show active" id="sessions" role="tabpanel">
                {% if sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>نوع الجلسة</th>
                                    <th>التاريخ</th>
                                    <th>القاعة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in sessions %}
                                <tr>
                                    <td>{{ session.session_type }}</td>
                                    <td>{{ session.session_date.strftime("%Y-%m-%d %H:%M") if session.session_date else "-" }}</td>
                                    <td>{{ session.court_room or '-' }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ session.session_status }}</span>
                                    </td>
                                    <td>
                                        <button onclick="alert('سيتم إضافة هذه الوظيفة قريباً')" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-gavel fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد جلسات مسجلة</h5>
                        <p class="text-muted">لم يتم تسجيل أي جلسات لهذه القضية بعد.</p>
                    </div>
                {% endif %}
            </div>
            
            <!-- تبويب الملاحظات -->
            <div class="tab-pane fade" id="notes" role="tabpanel">
                {% if notes %}
                    {% for note in notes %}
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0">
                                    <span class="badge bg-secondary me-2">{{ note.note_type }}</span>
                                    {{ note.author.full_name }}
                                </h6>
                                <small class="text-muted">{{ note.created_at.strftime("%Y-%m-%d %H:%M") if note.created_at else "-" }}</small>
                            </div>
                            <p class="card-text">{{ note.note_content }}</p>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-sticky-note fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد ملاحظات</h5>
                        <p class="text-muted">لم يتم إضافة أي ملاحظات لهذه القضية بعد.</p>
                    </div>
                {% endif %}
            </div>
            
            <!-- تبويب الجدول الزمني -->
            <div class="tab-pane fade" id="timeline" role="tabpanel">
                {% if timeline %}
                    <div class="timeline">
                        {% for event in timeline %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{{ event.event_title }}</h6>
                                <p class="timeline-description">{{ event.event_description }}</p>
                                <small class="text-muted">
                                    {{ event.event_date.strftime("%Y-%m-%d %H:%M") if event.event_date else "-" }} - 
                                    {{ event.creator.full_name }}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أحداث</h5>
                        <p class="text-muted">لم يتم تسجيل أي أحداث في الجدول الزمني بعد.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 20px);
    background-color: #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-description {
    margin-bottom: 10px;
    color: #6c757d;
}
</style>
{% endblock %}
