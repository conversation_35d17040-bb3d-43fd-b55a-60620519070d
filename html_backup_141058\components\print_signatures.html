{# مكون التوقيعات للتقارير #} {% if show_signatures != 'false' %}
<div class="signatures page-break-inside-avoid"><div class="signature-box"><div class="signature-title">إعداد التقرير</div><div class="signature-line"></div><div class="signature-name">
      {{ prepared_by or "نظام إدارة الموارد البشرية" }}
    </div><div class="signature-date">التاريخ: _______________</div></div><div class="signature-box"><div class="signature-title">مراجعة</div><div class="signature-line"></div><div class="signature-name">مدير الموارد البشرية</div><div class="signature-date">التاريخ: _______________</div></div><div class="signature-box"><div class="signature-title">اعتماد</div><div class="signature-line"></div><div class="signature-name">المدير العام</div><div class="signature-date">التاريخ: _______________</div></div></div>
{% endif %}

<style>
  .signatures {
    margin-top: 60px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    page-break-inside: avoid;
  }

  .signature-box {
    text-align: center;
    min-height: 120px;
  }

  .signature-title {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 40px;
    font-size: 14px;
  }

  .signature-line {
    border-top: 2px solid #333;
    margin: 0 20px 10px 20px;
    height: 1px;
  }

  .signature-name {
    color: #495057;
    font-size: 12px;
    margin-bottom: 10px;
    font-weight: 600;
  }

  .signature-date {
    color: #6c757d;
    font-size: 11px;
    margin-top: 15px;
  }

  @media print {
    .signatures {
      margin-top: 80px;
      page-break-inside: avoid;
    }

    .signature-line {
      border-top: 2px solid #000 !important;
    }

    .signature-title {
      color: #000 !important;
    }

    .signature-name {
      color: #000 !important;
    }

    .signature-date {
      color: #333 !important;
    }
  }

  @media screen {
    .signatures {
      display: none;
    }
  }
</style>
