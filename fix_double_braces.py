#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع للأقواس المضاعفة في القوالب
"""

import os
import re
import glob

def fix_double_braces():
    """إصلاح الأقواس المضاعفة {{ {{ }} }} في جميع القوالب"""
    
    print("🔧 إصلاح الأقواس المضاعفة في القوالب...")
    
    # البحث عن جميع ملفات HTML
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # إصلاح الأنماط المختلفة للأقواس المضاعفة
            patterns = [
                # {{ {{ expression }} }}
                (r'\{\{\s*\{\{\s*([^}]+)\s*\}\}\s*\}\}', r'{{ \1 }}'),
                
                # {{ {{ url_for('view') }} }}
                (r'\{\{\s*\{\{\s*(url_for\([^}]+\))\s*\}\}\s*\}\}', r'{{ \1 }}'),
                
                # أي نمط مضاعف آخر
                (r'\{\{\s*\{\{([^}]*)\}\}\s*\}\}', r'{{ \1 }}'),
                
                # إصلاح مسافات إضافية
                (r'\{\{\s{2,}', r'{{ '),
                (r'\s{2,}\}\}', r' }}'),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            # حفظ الملف إذا تم إجراء تغييرات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
            else:
                print(f"  ✅ {file_path}: لا توجد أخطاء")
                
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} خطأ في الأقواس المضاعفة")
    return total_fixes

def validate_templates():
    """التحقق من صحة بناء الجملة في القوالب"""
    
    print("\n🔍 التحقق من صحة بناء الجملة...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    errors = []
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن أخطاء شائعة
            error_patterns = [
                (r'\{\{\s*\{\{', 'أقواس مضاعفة'),
                (r'\}\}\s*\}\}', 'أقواس إغلاق مضاعفة'),
                (r'\{\%\s*\{\%', 'علامات قالب مضاعفة'),
                (r'===\s*=', 'مقارنة JavaScript خاطئة'),
                (r'!==\s*=', 'مقارنة JavaScript خاطئة'),
            ]
            
            for pattern, error_type in error_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    errors.append(f"{file_path}: {error_type} ({len(matches)} مرة)")
        
        except Exception as e:
            errors.append(f"{file_path}: خطأ في القراءة - {e}")
    
    if errors:
        print("❌ تم العثور على أخطاء:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ جميع القوالب صحيحة!")
        return True

if __name__ == "__main__":
    print("🚀 بدء إصلاح الأقواس المضاعفة...")
    
    # إصلاح الأخطاء
    fixes = fix_double_braces()
    
    # التحقق من النتائج
    is_valid = validate_templates()
    
    print("\n" + "="*50)
    if is_valid:
        print("🎉 تم إصلاح جميع الأخطاء بنجاح!")
        print("✅ جميع القوالب تعمل بشكل صحيح")
    else:
        print("⚠️ لا تزال هناك بعض الأخطاء")
    print("="*50)
