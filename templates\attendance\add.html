{% extends "base.html" %} {% block title %}إضافة سجل حضور{% endblock %} {% block content %}
<div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-header"><h3 class="card-title"><i class="fas fa-plus"></i>
            إضافة سجل حضور جديد
          </h3><div class="card-tools"><a
              href="{{ url_for ('attendance.index' }}"
              class="btn btn-secondary btn-sm"
        ><i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a></div></div><form id="attendanceForm"><div class="card-body"><div class="row"><div class="col-md-6"><div class="form-group"><label for="employee_id"
                >الموظف <span class="text-danger">*</span></label><select
                    class="form-control"
                    id="employee_id"
                    name="employee_id"
                    required
              ><option value="">اختر الموظف</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}">
                      {{ employee.full_name }} - {{ employee.employee_number }}
                    </option>
                   ) {% endfor %}
                  </select></div></div><div class="col-md-6"><div class="form-group"><label for="date"
                >التاريخ <span class="text-danger">*</span></label><input
                    type="date"
                    class="form-control"
                    id="date"
                    name="date"
                    value="{{ today }}"
                    required
                  /></div></div></div><div class="row"><div class="col-md-3"><div class="form-group"><label for="check_in">وقت الحضور</label><input
                    type="time"
                    class="form-control"
                    id="check_in"
                    name="check_in"
                  /></div></div><div class="col-md-3"><div class="form-group"><label for="check_out">وقت الانصراف</label><input
                    type="time"
                    class="form-control"
                    id="check_out"
                    name="check_out"
                  /></div></div><div class="col-md-3"><div class="form-group"><label for="break_start">بداية الاستراحة</label><input
                    type="time"
                    class="form-control"
                    id="break_start"
                    name="break_start"
                  /></div></div><div class="col-md-3"><div class="form-group"><label for="break_end">نهاية الاستراحة</label><input
                    type="time"
                    class="form-control"
                    id="break_end"
                    name="break_end"
                  /></div></div></div><div class="row"><div class="col-md-4"><div class="form-group"><label for="status">الحالة</label><select class="form-control" id="status" name="status"><option value="present">حاضر</option><option value="absent">غائب</option><option value="late">متأخر</option><option value="half_day">نصف يوم</option></select></div></div><div class="col-md-4"><div class="form-group"><label for="location">الموقع</label><input
                    type="text"
                    class="form-control"
                    id="location"
                    name="location"
                    placeholder="مكتب، عمل ميداني، إلخ..."
                  /></div></div><div class="col-md-4"><div class="form-group"><label for="hours_worked">ساعات العمل</label><input
                    type="number"
                    class="form-control"
                    id="hours_worked"
                    name="hours_worked"
                    step="0.25"
                    min="0"
                    max="24"
                    readonly
                  /><small class="form-text text-muted"
                >يتم حسابها تلقائياً</small></div></div></div><div class="row"><div class="col-12"><div class="form-group"><label for="notes">ملاحظات</label><textarea
                    class="form-control"
                    id="notes"
                    name="notes"
                    rows="3"
                    placeholder="أي ملاحظات إضافية..."
              ></textarea></div></div></div></div><div class="card-footer"><button type="submit" class="btn btn-primary"><i class="fas fa-save"></i>
              حفظ سجل الحضور
            </button><a
              href="{{ url_for('attendance.index' }}"
              class="btn btn-secondary"
        ><i class="fas fa-times"></i>
              إلغاء
            </a></div></form></div></div></div></div><script>
  // حساب ساعات العمل تلقائياً
  function calculateHours() {
    const checkIn = document.getElementById("check_in").value;
    const checkOut = document.getElementById("check_out").value;
    const breakStart = document.getElementById("break_start").value;
    const breakEnd = document.getElementById("break_end").value;

    if (checkIn && checkOut) {
      // تحويل الأوقات إلى دقائق
      const checkInMinutes = timeToMinutes(checkIn);
      const checkOutMinutes = timeToMinutes(checkOut);

      let totalMinutes = checkOutMinutes - checkInMinutes;

      // إذا كان وقت الانصراف في اليوم التالي
      if (totalMinutes < 0) {
        totalMinutes += 24 * 60;
      }

      // خصم وقت الاستراحة
      if (breakStart && breakEnd) {
        const breakStartMinutes = timeToMinutes(breakStart);
        const breakEndMinutes = timeToMinutes(breakEnd);
        let breakDuration = breakEndMinutes - breakStartMinutes;

        if (breakDuration < 0) {
          breakDuration += 24 * 60;
        }

        totalMinutes -= breakDuration;
      }

      // تحويل إلى ساعات
      const hours = Math.max(0, totalMinutes / 60);
      document.getElementById("hours_worked").value = hours.toFixed(2);
    }
  }

  function timeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(":").map(Number);
    return hours * 60 + minutes;
  }

  // ربط الأحداث
  document
    .getElementById("check_in")
    .addEventListener("change", calculateHours);
  document
    .getElementById("check_out")
    .addEventListener("change", calculateHours);
  document
    .getElementById("break_start")
    .addEventListener("change", calculateHours);
  document
    .getElementById("break_end")
    .addEventListener("change", calculateHours);

  // إرسال النموذج
  document
    .getElementById("attendanceForm")
    .addEventListener("submit", function (e) {
      e.preventDefault();

      const formData = new FormData(this);

      // إظهار مؤشر التحميل
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
      submitBtn.disabled = true;

      fetch('{{ url_for ("attendance:add" %}',) {
        method: "POST",
        body: formData,
      })
        .then(response) => response.json()
        .then(data) => {
          if (data.success) {
            // إظهار رسالة نجاح
            showAlert("success", data.message);

            // إعادة توجيه بعد ثانيتين
            setTimeout() => {
              window.location.href = '{{ url_for("attendance:index" %}';
            }, 2000);
          } else {
            showAlert("danger", data.message);

            // إعادة تفعيل الزر
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
          }
        })
        .catch(error) => {
          showAlert("danger", "حدث خطأ في الاتصال");

          // إعادة تفعيل الزر
          submitBtn.innerHTML = originalText;
          submitBtn.disabled = false;
        });
    });

  function showAlert(type, message) {
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
      ${message}
      <button type="button" class="close" data-dismiss="alert"><span>&times;</span></button>
    `;

    // إدراج التنبيه في أعلى الصفحة
    const container = document.querySelector(".container-fluid");
    container.insertBefore(alertDiv, container.firstChild);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout() => {
      alertDiv.remove();
    }, 5000);
  }

  // تعيين الوقت الحالي عند تحميل الصفحة
  document.addEventListener("DOMContentLoaded", function () {
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5);

    // إذا لم يتم تعيين وقت الحضور، استخدم الوقت الحالي
    if (!document.getElementById("check_in").value) {
      document.getElementById("check_in").value = currentTime;
    }
  });
</script>
{% endblock %}
