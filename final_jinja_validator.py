#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص نهائي شامل لفلاتر Jinja2
"""

import os
import re
import glob

def validate_all_jinja_filters():
    """فحص شامل لجميع فلاتر Jinja2"""
    
    print("🔍 فحص شامل لجميع فلاتر Jinja2...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    errors = []
    warnings = []
    fixed_count = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_errors = []
            file_fixes = 0
            
            # البحث عن أخطاء شائعة وإصلاحها
            error_patterns = [
                # format(format(...)) - مضاعف
                (r'format\(format\(([^)]+)\)\s*\)', r'format(\1)', 'format مضاعف'),
                
                # "string"|variable - ترتيب خاطئ
                (r'\{\{\s*"([^"]+)"\|([^}]+)\s*\}\}', r'{{ "\1".format(\2) }}', 'ترتيب فلتر خاطئ'),
                
                # variable|filter.attribute - ترتيب خاطئ
                (r'\{\{\s*([^|}]+)\|([^.}]+)\.([^}]+)\s*\}\}', r'{{ \1.\3|\2 }}', 'ترتيب فلتر وخاصية خاطئ'),
                
                # أقواس مضاعفة
                (r'\{\{\s*\{\{', r'{{', 'أقواس مضاعفة'),
                (r'\}\}\s*\}\}', r'}}', 'أقواس إغلاق مضاعفة'),
                
                # علامات قالب مضاعفة
                (r'\{\%\s*\{\%', r'{%', 'علامات قالب مضاعفة'),
                (r'\%\}\s*\%\}', r'%}', 'علامات إغلاق قالب مضاعفة'),
            ]
            
            for pattern, replacement, error_type in error_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
                    file_errors.append(f"{error_type}: {len(matches)} مرة")
            
            # البحث عن أخطاء لا يمكن إصلاحها تلقائياً
            critical_patterns = [
                (r'undefined', 'متغير غير معرف'),
                (r'\.format\(\s*\)', 'format فارغ'),
                (r'\|\s*$', 'فلتر غير مكتمل'),
                (r'\{\{\s*\}\}', 'متغير فارغ'),
            ]
            
            for pattern, error_type in critical_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    file_errors.append(f"{error_type}: {len(matches)} مرة")
            
            # حفظ الملف إذا تم إجراء إصلاحات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_count += file_fixes
                print(f"  🔧 {file_path}: تم إصلاح {file_fixes} خطأ")
            
            # إضافة الأخطاء إلى القائمة
            if file_errors:
                errors.append(f"{file_path}: {', '.join(file_errors)}")
            else:
                print(f"  ✅ {file_path}: لا توجد أخطاء")
        
        except Exception as e:
            errors.append(f"{file_path}: خطأ في القراءة - {e}")
    
    return errors, warnings, fixed_count

def check_specific_dashboard_issues():
    """فحص مشاكل محددة في dashboard.html"""
    
    print("\n🔍 فحص مشاكل محددة في dashboard.html...")
    
    dashboard_file = "templates/dashboard.html"
    if not os.path.exists(dashboard_file):
        return ["dashboard.html غير موجود"]
    
    try:
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # البحث عن مشاكل محددة
        specific_patterns = [
            (r'format\(format\(', 'format مضاعف'),
            (r'\.format\(\s*format\s*\(', 'format متداخل'),
            (r'total_revenue\s*\)\s*\)', 'أقواس إضافية'),
            (r'pending_revenue\s*\)\s*\)', 'أقواس إضافية'),
        ]
        
        for pattern, issue_type in specific_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues.append(f"{issue_type}: {len(matches)} مرة")
        
        return issues
        
    except Exception as e:
        return [f"خطأ في قراءة dashboard.html: {e}"]

def test_jinja_syntax():
    """اختبار بناء جملة Jinja2"""
    
    print("\n🧪 اختبار بناء جملة Jinja2...")
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        # إنشاء بيئة Jinja2
        env = Environment(loader=FileSystemLoader('templates'))
        
        template_files = glob.glob("templates/**/*.html", recursive=True)
        syntax_errors = []
        
        for file_path in template_files:
            try:
                # استخراج المسار النسبي
                relative_path = os.path.relpath(file_path, 'templates').replace('\\', '/')
                
                # محاولة تحميل القالب
                template = env.get_template(relative_path)
                print(f"  ✅ {relative_path}: بناء جملة صحيح")
                
            except TemplateSyntaxError as e:
                syntax_errors.append(f"{relative_path}: {e}")
                print(f"  ❌ {relative_path}: خطأ في بناء الجملة")
            except Exception as e:
                syntax_errors.append(f"{relative_path}: {e}")
                print(f"  ⚠️ {relative_path}: خطأ في التحميل")
        
        return syntax_errors
        
    except ImportError:
        return ["لا يمكن استيراد Jinja2 للاختبار"]

def generate_final_report(errors, warnings, fixed_count, dashboard_issues, syntax_errors):
    """إنشاء التقرير النهائي"""
    
    report = []
    report.append("=" * 60)
    report.append("🔍 تقرير الفحص النهائي لفلاتر Jinja2")
    report.append("=" * 60)
    
    # ملخص عام
    report.append(f"\n📊 ملخص عام:")
    report.append(f"  - إصلاحات تلقائية: {fixed_count}")
    report.append(f"  - أخطاء متبقية: {len(errors)}")
    report.append(f"  - تحذيرات: {len(warnings)}")
    report.append(f"  - مشاكل dashboard: {len(dashboard_issues)}")
    report.append(f"  - أخطاء بناء الجملة: {len(syntax_errors)}")
    
    # الأخطاء المتبقية
    if errors:
        report.append(f"\n❌ أخطاء متبقية ({len(errors)}):")
        for error in errors[:10]:  # أول 10 أخطاء
            report.append(f"  - {error}")
        if len(errors) > 10:
            report.append(f"  ... و {len(errors) - 10} خطأ آخر")
    
    # مشاكل dashboard
    if dashboard_issues:
        report.append(f"\n⚠️ مشاكل dashboard ({len(dashboard_issues)}):")
        for issue in dashboard_issues:
            report.append(f"  - {issue}")
    
    # أخطاء بناء الجملة
    if syntax_errors:
        report.append(f"\n❌ أخطاء بناء الجملة ({len(syntax_errors)}):")
        for error in syntax_errors[:5]:  # أول 5 أخطاء
            report.append(f"  - {error}")
        if len(syntax_errors) > 5:
            report.append(f"  ... و {len(syntax_errors) - 5} خطأ آخر")
    
    # النتيجة النهائية
    total_issues = len(errors) + len(dashboard_issues) + len(syntax_errors)
    if total_issues == 0:
        report.append(f"\n🎉 ممتاز! جميع فلاتر Jinja2 تعمل بشكل صحيح")
        report.append(f"✅ التطبيق جاهز للاستخدام")
    else:
        report.append(f"\n⚠️ يوجد {total_issues} مشكلة تحتاج مراجعة")
        report.append(f"💡 يُنصح بإصلاح هذه المشاكل")
    
    return "\n".join(report)

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء الفحص النهائي الشامل...")
    
    # فحص عام لجميع الفلاتر
    errors, warnings, fixed_count = validate_all_jinja_filters()
    
    # فحص مشاكل dashboard محددة
    dashboard_issues = check_specific_dashboard_issues()
    
    # اختبار بناء الجملة
    syntax_errors = test_jinja_syntax()
    
    # إنشاء التقرير
    report = generate_final_report(errors, warnings, fixed_count, dashboard_issues, syntax_errors)
    
    # طباعة التقرير
    print(report)
    
    # حفظ التقرير
    with open('final_jinja_validation_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 تم حفظ التقرير في: final_jinja_validation_report.txt")
    
    # النتيجة النهائية
    total_issues = len(errors) + len(dashboard_issues) + len(syntax_errors)
    return total_issues == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
