#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع لتحويل request.args إلى request.args في Flask
"""

import os
import re
import glob

def fix_request_get():
    """تحويل جميع استخدامات request.args إلى request.args"""
    
    print("🔧 إصلاح request.args إلى request.args...")
    
    # البحث عن جميع ملفات Python
    python_files = []
    for pattern in ['*.py', 'routes/*.py']:
        python_files.extend(glob.glob(pattern))
    
    total_fixes = 0
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # أنماط الإصلاح
            patterns = [
                # request.args.get() → request.args.get()
                (r'request\.GET\.get\(', 'request.args.get('),
                
                # request.args['key'] → request.args['key']
                (r'request\.GET\[', 'request.args['),
                
                # request.args → request.args
                (r'request\.GET(?![a-zA-Z_])', 'request.args'),
                
                # request.form → request.form (إضافي)
                (r'request\.POST\.get\(', 'request.form.get('),
                (r'request\.POST\[', 'request.form['),
                (r'request\.POST(?![a-zA-Z_])', 'request.form'),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            # حفظ الملف إذا تم إجراء تغييرات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
            else:
                print(f"  ✅ {file_path}: لا توجد أخطاء")
                
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} خطأ request.args/POST")
    return total_fixes

def fix_other_django_flask_issues():
    """إصلاح مشاكل أخرى بين Django و Flask"""
    
    print("\n🔧 إصلاح مشاكل Django/Flask أخرى...")
    
    python_files = []
    for pattern in ['*.py', 'routes/*.py']:
        python_files.extend(glob.glob(pattern))
    
    total_fixes = 0
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # أنماط إضافية
            patterns = [
                # JsonResponse → jsonify
                (r'return JsonResponse\(', 'return jsonify('),
                (r'JsonResponse\(', 'jsonify('),
                
                # render → render_template
                (r'return render\(', 'return render_template('),
                
                # إصلاح استيراد خاطئ
                (r'from django\.shortcuts import', 'from flask import'),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} خطأ إضافي")
                total_fixes += file_fixes
                
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"✅ تم إصلاح {total_fixes} خطأ إضافي")
    return total_fixes

def validate_flask_syntax():
    """التحقق من صحة بناء الجملة Flask"""
    
    print("\n🔍 التحقق من صحة بناء الجملة Flask...")
    
    python_files = []
    for pattern in ['*.py', 'routes/*.py']:
        python_files.extend(glob.glob(pattern))
    
    errors = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن أخطاء شائعة
            error_patterns = [
                (r'request\.GET', 'استخدام request.args بدلاً من request.args'),
                (r'request\.POST', 'استخدام request.form بدلاً من request.form'),
                (r'JsonResponse', 'استخدام JsonResponse بدلاً من jsonify'),
                (r'from django', 'استيراد Django في مشروع Flask'),
            ]
            
            for pattern, error_type in error_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    errors.append(f"{file_path}: {error_type} ({len(matches)} مرة)")
        
        except Exception as e:
            errors.append(f"{file_path}: خطأ في القراءة - {e}")
    
    if errors:
        print("❌ تم العثور على أخطاء:")
        for error in errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ جميع ملفات Python صحيحة!")
        return True

if __name__ == "__main__":
    print("🚀 بدء إصلاح أخطاء Django/Flask...")
    
    # إصلاح request.args/POST
    request_fixes = fix_request_get()
    
    # إصلاح مشاكل أخرى
    other_fixes = fix_other_django_flask_issues()
    
    # التحقق من النتائج
    is_valid = validate_flask_syntax()
    
    print("\n" + "="*50)
    print(f"🎉 تم إصلاح {request_fixes + other_fixes} خطأ إجمالي")
    if is_valid:
        print("✅ جميع ملفات Python تعمل مع Flask بشكل صحيح")
    else:
        print("⚠️ لا تزال هناك بعض الأخطاء")
    print("="*50)
