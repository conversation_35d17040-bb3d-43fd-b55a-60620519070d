#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لجميع أخطاء بناء الجملة في القوالب والنماذج
"""

import os
import re
import glob
from datetime import datetime

def fix_jinja_syntax_errors():
    """إصلاح أخطاء بناء الجملة في جميع القوالب"""
    
    print("🔧 إصلاح أخطاء بناء الجملة في جميع القوالب...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    fixed_files = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # الأنماط الشائعة للأخطاء وإصلاحها
            patterns = [
                # إصلاح المسافات الإضافية في نهاية التعبيرات
                (r'\{\{\s*([^}]+?)\s{2,}\}\}', r'{{ \1 }}'),
                
                # إصلاح المسافات الإضافية في علامات التحكم
                (r'\{\%\s*([^%]+?)\s{2,}\%\}', r'{% \1 %}'),
                
                # إصلاح التعبيرات الشرطية المقسمة
                (r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}'),
                
                # إصلاح الأسطر المقسمة في التعبيرات
                (r'\{\{\s*([^}]+?)\n\s*([^}]+?)\s*\}\}', r'{{ \1 \2 }}'),
                
                # إصلاح علامات التحكم المقسمة
                (r'\{\%\s*([^%]+?)\n\s*([^%]+?)\s*\%\}', r'{% \1 \2 %}'),
                
                # إصلاح الأقواس المضاعفة
                (r'\{\{\s*\{\{', r'{{'),
                (r'\}\}\s*\}\}', r'}}'),
                (r'\{\%\s*\{\%', r'{%'),
                (r'\%\}\s*\%\}', r'%}'),
                
                # إصلاح المسافات حول العمليات
                (r'\{\{\s*([^}]+?)\s*\|\s*([^}]+?)\s*\}\}', r'{{ \1|\2 }}'),
                
                # إصلاح التعبيرات المعقدة
                (r'\{\{\s*([^}]+?)\s*\.\s*([^}]+?)\s*\(\s*([^}]*?)\s*\)\s*\}\}', r'{{ \1.\2(\3) }}'),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                if matches:
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
                    file_fixes += len(matches)
            
            # إصلاحات محددة للمشاكل الشائعة
            specific_fixes = [
                # إصلاح if-else المقسم
                ('if case.created_at else\n                "-"', 'if case.created_at else "-"'),
                ('if case.lawyer %} •', 'if case.lawyer %} •'),
                ('case.lawyer.user.full_name   }}{% endif  %}', 'case.lawyer.user.full_name }}{% endif %}'),
                
                # إصلاح المسافات الإضافية
                ('case.client.full_name  }}', 'case.client.full_name }}'),
                ('case.lawyer  %}', 'case.lawyer %}'),
                ('else "-"  }}', 'else "-" }}'),
                
                # إصلاح علامات Django في Jinja2
                ('{% url ', '{{ url_for('),
                (' %}"', ') }}"'),
                
                # إصلاح فلاتر Django
                ('|date:', '|strftime('),
                ('|floatformat:', '|round('),
                ('|default:', '|default('),
                ('|truncatechars:', '|truncate('),
            ]
            
            for old, new in specific_fixes:
                if old in content:
                    content = content.replace(old, new)
                    file_fixes += 1
            
            # حفظ الملف إذا تم إجراء إصلاحات
            if content != original_content:
                # إنشاء نسخة احتياطية
                backup_dir = "template_syntax_backups"
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = os.path.join(backup_dir, f"{os.path.basename(file_path)}_{timestamp}.backup")
                
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # حفظ الملف المُصلح
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
                fixed_files += 1
            else:
                print(f"  ✅ {file_path}: لا توجد أخطاء")
        
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} خطأ في {fixed_files} ملف")
    return total_fixes, fixed_files

def validate_all_templates():
    """التحقق من صحة بناء الجملة في جميع القوالب"""
    
    print("\n🔍 التحقق من صحة بناء الجملة...")
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        template_files = glob.glob("templates/**/*.html", recursive=True)
        
        valid_files = 0
        error_files = []
        
        for file_path in template_files:
            try:
                relative_path = os.path.relpath(file_path, 'templates').replace('\\', '/')
                template = env.get_template(relative_path)
                valid_files += 1
                print(f"  ✅ {relative_path}: صحيح")
                
            except TemplateSyntaxError as e:
                error_files.append(f"{relative_path}: {e}")
                print(f"  ❌ {relative_path}: خطأ في بناء الجملة")
            except Exception as e:
                error_files.append(f"{relative_path}: {e}")
                print(f"  ⚠️ {relative_path}: خطأ في التحميل")
        
        print(f"\n📊 النتائج:")
        print(f"  - ملفات صحيحة: {valid_files}")
        print(f"  - ملفات بها أخطاء: {len(error_files)}")
        
        if error_files:
            print(f"\n❌ الأخطاء المتبقية:")
            for error in error_files[:10]:  # أول 10 أخطاء
                print(f"  - {error}")
            if len(error_files) > 10:
                print(f"  ... و {len(error_files) - 10} خطأ آخر")
        
        return len(error_files) == 0, error_files
        
    except ImportError:
        print("❌ لا يمكن استيراد Jinja2 للتحقق")
        return False, ["لا يمكن التحقق من بناء الجملة"]

def fix_specific_syntax_issues():
    """إصلاح مشاكل بناء الجملة المحددة"""
    
    print("\n🔧 إصلاح مشاكل بناء الجملة المحددة...")
    
    # مشاكل محددة معروفة
    specific_issues = {
        'templates/admin/edit_profile.html': [
            ('{{ user.email if user.email else', '{{ user.email if user.email else'),
            ('user.username }}', 'user.username }}'),
        ],
        'templates/admin/profile.html': [
            ('{{ user.full_name if user.full_name else', '{{ user.full_name if user.full_name else'),
            ('user.username }}', 'user.username }}'),
        ],
        'templates/dashboard.html': [
            ('case.client.full_name  }}', 'case.client.full_name }}'),
            ('case.lawyer  %}', 'case.lawyer %}'),
            ('case.lawyer.user.full_name   }}', 'case.lawyer.user.full_name }}'),
            ('endif  %}', 'endif %}'),
            ('else "-"  }}', 'else "-" }}'),
        ]
    }
    
    fixed_count = 0
    
    for file_path, fixes in specific_issues.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                file_fixes = 0
                
                for old, new in fixes:
                    if old in content:
                        content = content.replace(old, new)
                        file_fixes += 1
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"  ✅ {file_path}: تم إصلاح {file_fixes} مشكلة محددة")
                    fixed_count += file_fixes
                
            except Exception as e:
                print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"✅ تم إصلاح {fixed_count} مشكلة محددة")
    return fixed_count

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء الإصلاح الشامل لأخطاء بناء الجملة...")
    
    # إصلاح الأخطاء العامة
    total_fixes, fixed_files = fix_jinja_syntax_errors()
    
    # إصلاح المشاكل المحددة
    specific_fixes = fix_specific_syntax_issues()
    
    # التحقق من النتائج
    is_valid, errors = validate_all_templates()
    
    print("\n" + "="*60)
    print("📊 تقرير الإصلاح الشامل")
    print("="*60)
    print(f"🔧 إصلاحات عامة: {total_fixes}")
    print(f"🎯 إصلاحات محددة: {specific_fixes}")
    print(f"📁 ملفات مُصلحة: {fixed_files}")
    print(f"✅ ملفات صحيحة: {len(glob.glob('templates/**/*.html', recursive=True)) - len(errors) if errors else 'جميع الملفات'}")
    print(f"❌ أخطاء متبقية: {len(errors) if errors else 0}")
    
    if is_valid:
        print("\n🎉 تم إصلاح جميع أخطاء بناء الجملة بنجاح!")
        print("✅ جميع القوالب تعمل بشكل صحيح")
    else:
        print(f"\n⚠️ لا تزال هناك {len(errors)} أخطاء تحتاج مراجعة")
        print("💡 قد تحتاج إصلاح يدوي")
    
    print("="*60)
    
    return is_valid

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
