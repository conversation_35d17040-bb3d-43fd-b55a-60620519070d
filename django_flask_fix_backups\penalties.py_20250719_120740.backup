from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from decimal import Decimal
import uuid
import os
from werkzeug.utils import secure_filename
from permissions_manager import permission_required

bp = Blueprint('penalties', __name__, url_prefix='/penalties')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def get_db():
    """الحصول على كائن قاعدة البيانات"""
    try:
        from django.shortcuts import current_app
        return current_app.extensions['sqlalchemy'].db
    except (RuntimeError, KeyError):
        return None

def allowed_file(filename):
    """التحقق من أن نوع الملف مسموح"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def ensure_upload_folder():
    """التأكد من وجود مجلد رفع الملفات"""
    upload_folder = os.path.join(current_app.root_path, 'static', 'uploads', 'penalties')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    return upload_folder

@bp.route('/')
@login_required
@permission_required('view_penalties')
def index():
    """عرض الجزاءات"""
    try:
        models = get_models()
        Penalty = models.get('Penalty')
        Employee = models.get('Employee')
        Department = models.get('Department')
        
        if not all([Penalty, Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # فلترة البيانات
        search = request.GET.get('search', '').strip()
        employee_id = request.GET.get('employee_id', type=int)
        department_id = request.GET.get('department_id', type=int)
        penalty_type = request.GET.get('penalty_type', '').strip()
        category = request.GET.get('category', '').strip()
        severity = request.GET.get('severity', '').strip()
        status = request.GET.get('status', '').strip()
        payment_status = request.GET.get('payment_status', '').strip()
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        
        # بناء الاستعلام
        query = Penalty.query.join(Employee)
        
        if search:
            from sqlalchemy import or_
            query = query.filter(
                or_(
                    Employee.full_name.contains(search),
                    Penalty.title.contains(search),
                    Penalty.description.contains(search)
                )
            )
        
        if employee_id:
            query = query.filter(Penalty.employee_id == employee_id)
        
        if department_id:
            query = query.filter(Employee.department_id == department_id)
        
        if penalty_type:
            query = query.filter(Penalty.penalty_type == penalty_type)
        
        if category:
            query = query.filter(Penalty.category == category)
        
        if severity:
            query = query.filter(Penalty.severity == severity)
        
        if status:
            query = query.filter(Penalty.status == status)
        
        if payment_status:
            query = query.filter(Penalty.payment_status == payment_status)
        
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(Penalty.penalty_date >= date_from_obj)
            except ValueError:
                pass
        
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                query = query.filter(Penalty.penalty_date <= date_to_obj)
            except ValueError:
                pass
        
        # ترتيب النتائج
        penalties = query.order_by(Penalty.created_at.desc()).all()
        
        # جلب قوائم الفلترة
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
        departments = Department.query.order_by(Department.name).all()
        
        # أنواع الجزاءات
        penalty_types = [
            ('financial', 'جزاء مالي'),
            ('suspension', 'إيقاف عن العمل'),
            ('termination', 'فصل من العمل'),
            ('demotion', 'تخفيض درجة'),
            ('warning', 'إنذار'),
            ('other', 'أخرى')
        ]
        
        # فئات الجزاءات
        categories = [
            ('attendance', 'الحضور والغياب'),
            ('performance', 'الأداء الوظيفي'),
            ('conduct', 'السلوك المهني'),
            ('safety', 'السلامة والأمان'),
            ('policy', 'مخالفة السياسات'),
            ('financial', 'مخالفات مالية'),
            ('other', 'أخرى')
        ]
        
        # مستويات الخطورة
        severities = [
            ('low', 'منخفض'),
            ('medium', 'متوسط'),
            ('high', 'عالي'),
            ('critical', 'حرج')
        ]
        
        # حالات الجزاء
        statuses = [
            ('active', 'نشط'),
            ('completed', 'مكتمل'),
            ('cancelled', 'ملغي')
        ]
        
        # حالات الدفع
        payment_statuses = [
            ('pending', 'في الانتظار'),
            ('paid', 'مدفوع'),
            ('waived', 'معفى')
        ]
        
        # إحصائيات سريعة
        total_amount = sum([p.amount or 0 for p in penalties if p.penalty_type == 'financial'])
        paid_amount = sum([p.amount or 0 for p in penalties if p.penalty_type == 'financial' and p.payment_status == 'paid'])
        
        stats = {
            'total_penalties': len(penalties),
            'active_penalties': len([p for p in penalties if p.status == 'active']),
            'completed_penalties': len([p for p in penalties if p.status == 'completed']),
            'financial_penalties': len([p for p in penalties if p.penalty_type == 'financial']),
            'total_amount': total_amount,
            'paid_amount': paid_amount,
            'pending_amount': total_amount - paid_amount,
            'this_month': len([p for p in penalties if p.created_at.month == date.today().month])
        }
        
        return render('penalties/index.html',
                             penalties=penalties,
                             employees=employees,
                             departments=departments,
                             penalty_types=penalty_types,
                             categories=categories,
                             severities=severities,
                             statuses=statuses,
                             payment_statuses=payment_statuses,
                             search=search,
                             employee_id=employee_id,
                             department_id=department_id,
                             penalty_type=penalty_type,
                             category=category,
                             severity=severity,
                             status=status,
                             payment_status=payment_status,
                             date_from=date_from,
                             date_to=date_to,
                             stats=stats)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required('view_penalties')
def add():
    """إضافة جزاء جديد"""
    if request.method == 'GET':
        try:
            models = get_models()
            Employee = models.get('Employee')
            
            if not Employee:
                flash('خطأ في تحميل النماذج', 'error')
                return redirect(url_for('penalties.index'))

            employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
            
            # أنواع الجزاءات
            penalty_types = [
                ('financial', 'جزاء مالي'),
                ('suspension', 'إيقاف عن العمل'),
                ('termination', 'فصل من العمل'),
                ('demotion', 'تخفيض درجة'),
                ('warning', 'إنذار'),
                ('other', 'أخرى')
            ]
            
            # فئات الجزاءات
            categories = [
                ('attendance', 'الحضور والغياب'),
                ('performance', 'الأداء الوظيفي'),
                ('conduct', 'السلوك المهني'),
                ('safety', 'السلامة والأمان'),
                ('policy', 'مخالفة السياسات'),
                ('financial', 'مخالفات مالية'),
                ('other', 'أخرى')
            ]
            
            # مستويات الخطورة
            severities = [
                ('low', 'منخفض'),
                ('medium', 'متوسط'),
                ('high', 'عالي'),
                ('critical', 'حرج')
            ]
            
            return render('penalties/add.html',
                                 employees=employees,
                                 penalty_types=penalty_types,
                                 categories=categories,
                                 severities=severities,
                                 today=date.today())
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('penalties.index'))
    
    # POST request
    try:
        models = get_models()
        Penalty = models.get('Penalty')
        Employee = models.get('Employee')
        db = get_db()
        
        if not all([Penalty, Employee, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        # جلب البيانات من النموذج
        employee_id = request.POST.get('employee_id', type=int)
        penalty_type = request.POST.get('penalty_type', '').strip()
        category = request.POST.get('category', '').strip()
        title = request.POST.get('title', '').strip()
        description = request.POST.get('description', '').strip()
        incident_date_str = request.POST.get('incident_date')
        penalty_date_str = request.POST.get('penalty_date')
        severity = request.POST.get('severity', 'medium').strip()
        amount = request.POST.get('amount', type=float)
        days_count = request.POST.get('days_count', type=int)
        notes = request.POST.get('notes', '').strip()

        # التحقق من صحة البيانات
        if not all([employee_id, penalty_type, category, title, description, incident_date_str, penalty_date_str]):
            return JsonResponse({'success': False, 'message': 'جميع الحقول الأساسية مطلوبة'})

        # التحقق من وجود الموظف
        employee = Employee.query.get(employee_id)
        if not employee:
            return JsonResponse({'success': False, 'message': 'الموظف غير موجود'})

        # تحويل التواريخ
        try:
            incident_date_obj = datetime.strptime(incident_date_str, '%Y-%m-%d').date()
            penalty_date_obj = datetime.strptime(penalty_date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'success': False, 'message': 'تنسيق التاريخ غير صحيح'})

        # التحقق من صحة البيانات حسب نوع الجزاء
        if penalty_type == 'financial' and not amount:
            return JsonResponse({'success': False, 'message': 'مبلغ الجزاء مطلوب للجزاءات المالية'})
        
        if penalty_type == 'suspension' and not days_count:
            return JsonResponse({'success': False, 'message': 'عدد أيام الإيقاف مطلوب'})

        # معالجة رفع الملف
        attachment_path = None
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                # إنشاء مجلد الرفع
                upload_folder = ensure_upload_folder()
                
                # إنشاء اسم ملف فريد
                original_filename = secure_filename(file.filename)
                file_extension = original_filename.rsplit('.', 1)[1].lower()
                unique_filename = f"penalty_{employee_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
                file_path = os.path.join(upload_folder, unique_filename)
                
                # حفظ الملف
                file.save(file_path)
                attachment_path = f"uploads/penalties/{unique_filename}"

        # تحديد حالة الدفع الافتراضية
        payment_status = 'pending' if penalty_type == 'financial' else None

        # إنشاء الجزاء
        penalty = Penalty(
            employee_id=employee_id,
            penalty_type=penalty_type,
            category=category,
            title=title,
            description=description,
            incident_date=incident_date_obj,
            penalty_date=penalty_date_obj,
            amount=amount,
            days_count=days_count,
            severity=severity,
            status='active',
            payment_status=payment_status,
            notes=notes,
            attachment_path=attachment_path,
            created_by=current_user.id
        )

        db.add(penalty)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم إضافة الجزاء بنجاح'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:penalty_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required('manage_penalties')
def edit(penalty_id):
    """تعديل جزاء"""
    try:
        models = get_models()
        Penalty = models.get('Penalty')
        Employee = models.get('Employee')
        db = get_db()
        
        if not all([Penalty, Employee, db]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('penalties.index'))

        penalty = Penalty.query.get_or_404(penalty_id)
        
        if request.method == 'GET':
            employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
            
            # أنواع الجزاءات
            penalty_types = [
                ('financial', 'جزاء مالي'),
                ('suspension', 'إيقاف عن العمل'),
                ('termination', 'فصل من العمل'),
                ('demotion', 'تخفيض درجة'),
                ('warning', 'إنذار'),
                ('other', 'أخرى')
            ]
            
            # فئات الجزاءات
            categories = [
                ('attendance', 'الحضور والغياب'),
                ('performance', 'الأداء الوظيفي'),
                ('conduct', 'السلوك المهني'),
                ('safety', 'السلامة والأمان'),
                ('policy', 'مخالفة السياسات'),
                ('financial', 'مخالفات مالية'),
                ('other', 'أخرى')
            ]
            
            # مستويات الخطورة
            severities = [
                ('low', 'منخفض'),
                ('medium', 'متوسط'),
                ('high', 'عالي'),
                ('critical', 'حرج')
            ]
            
            # حالات الجزاء
            statuses = [
                ('active', 'نشط'),
                ('completed', 'مكتمل'),
                ('cancelled', 'ملغي')
            ]
            
            # حالات الدفع
            payment_statuses = [
                ('pending', 'في الانتظار'),
                ('paid', 'مدفوع'),
                ('waived', 'معفى')
            ]
            
            return render('penalties/edit.html',
                                 penalty=penalty,
                                 employees=employees,
                                 penalty_types=penalty_types,
                                 categories=categories,
                                 severities=severities,
                                 statuses=statuses,
                                 payment_statuses=payment_statuses)
        
        # POST request - تحديث الجزاء
        # جلب البيانات من النموذج
        employee_id = request.POST.get('employee_id', type=int)
        penalty_type = request.POST.get('penalty_type', '').strip()
        category = request.POST.get('category', '').strip()
        title = request.POST.get('title', '').strip()
        description = request.POST.get('description', '').strip()
        incident_date_str = request.POST.get('incident_date')
        penalty_date_str = request.POST.get('penalty_date')
        severity = request.POST.get('severity', 'medium').strip()
        status = request.POST.get('status', 'active').strip()
        payment_status = request.POST.get('payment_status', '').strip()
        amount = request.POST.get('amount', type=float)
        days_count = request.POST.get('days_count', type=int)
        notes = request.POST.get('notes', '').strip()

        # التحقق من صحة البيانات
        if not all([employee_id, penalty_type, category, title, description, incident_date_str, penalty_date_str]):
            return JsonResponse({'success': False, 'message': 'جميع الحقول الأساسية مطلوبة'})

        # التحقق من وجود الموظف
        employee = Employee.query.get(employee_id)
        if not employee:
            return JsonResponse({'success': False, 'message': 'الموظف غير موجود'})

        # تحويل التواريخ
        try:
            incident_date_obj = datetime.strptime(incident_date_str, '%Y-%m-%d').date()
            penalty_date_obj = datetime.strptime(penalty_date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'success': False, 'message': 'تنسيق التاريخ غير صحيح'})

        # معالجة رفع الملف الجديد
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                # حذف الملف القديم
                if penalty.attachment_path:
                    try:
                        old_file_path = os.path.join(current_app.root_path, 'static', penalty.attachment_path)
                        if os.path.exists(old_file_path):
                            os.remove(old_file_path)
                    except:
                        pass
                
                # إنشاء مجلد الرفع
                upload_folder = ensure_upload_folder()
                
                # إنشاء اسم ملف فريد
                original_filename = secure_filename(file.filename)
                file_extension = original_filename.rsplit('.', 1)[1].lower()
                unique_filename = f"penalty_{employee_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
                file_path = os.path.join(upload_folder, unique_filename)
                
                # حفظ الملف
                file.save(file_path)
                penalty.attachment_path = f"uploads/penalties/{unique_filename}"

        # تحديث البيانات
        penalty.employee_id = employee_id
        penalty.penalty_type = penalty_type
        penalty.category = category
        penalty.title = title
        penalty.description = description
        penalty.incident_date = incident_date_obj
        penalty.penalty_date = penalty_date_obj
        penalty.amount = amount
        penalty.days_count = days_count
        penalty.severity = severity
        penalty.status = status
        penalty.payment_status = payment_status
        penalty.notes = notes

        db.save()

        return JsonResponse({'success': True, 'message': 'تم تحديث الجزاء بنجاح'})

    except Exception as e:
        if request.method == 'POST':
            return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})
        else:
            flash(f'حدث خطأ: {str(e)}', 'error')
            return redirect(url_for('penalties.index'))

@bp.route('/<int:penalty_id>/delete', methods=['POST'])
@login_required
@permission_required('manage_penalties')
def delete(penalty_id):
    """حذف جزاء"""
    try:
        models = get_models()
        Penalty = models.get('Penalty')
        db = get_db()

        if not all([Penalty, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        penalty = Penalty.query.get_or_404(penalty_id)

        # حذف الملف المرفق إن وجد
        if penalty.attachment_path:
            try:
                file_path = os.path.join(current_app.root_path, 'static', penalty.attachment_path)
                if os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass  # تجاهل أخطاء حذف الملف

        db.delete(penalty)
        db.save()

        return JsonResponse({'success': True, 'message': 'تم حذف الجزاء بنجاح'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/<int:penalty_id>/mark-paid', methods=['POST'])
@login_required
@permission_required('manage_penalties')
def mark_paid(penalty_id):
    """تسديد جزاء مالي"""
    try:
        models = get_models()
        Penalty = models.get('Penalty')
        db = get_db()

        if not all([Penalty, db]):
            return JsonResponse({'success': False, 'message': 'خطأ في تحميل النماذج'})

        penalty = Penalty.query.get_or_404(penalty_id)

        if penalty.penalty_type != 'financial':
            return JsonResponse({'success': False, 'message': 'هذا الجزاء ليس مالياً'})

        if penalty.payment_status == 'paid':
            return JsonResponse({'success': False, 'message': 'تم تسديد هذا الجزاء مسبقاً'})

        penalty.payment_status = 'paid'
        db.save()

        return JsonResponse({'success': True, 'message': 'تم تسديد الجزاء بنجاح'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@bp.route('/report')
@login_required
@permission_required('view_penalties')
def report():
    """تقرير الجزاءات"""
    try:
        models = get_models()
        Penalty = models.get('Penalty')
        Employee = models.get('Employee')
        Department = models.get('Department')

        if not all([Penalty, Employee, Department]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('dashboard'))

        # فلترة البيانات
        employee_id = request.GET.get('employee_id', type=int)
        department_id = request.GET.get('department_id', type=int)
        penalty_type = request.GET.get('penalty_type', '').strip()
        category = request.GET.get('category', '').strip()
        severity = request.GET.get('severity', '').strip()
        status = request.GET.get('status', '').strip()
        payment_status = request.GET.get('payment_status', '').strip()
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        # تواريخ افتراضية (السنة الحالية)
        if not date_from:
            date_from = date.today().replace(month=1, day=1).strftime('%Y-%m-%d')
        if not date_to:
            date_to = date.today().strftime('%Y-%m-%d')

        # بناء الاستعلام
        query = Penalty.query.join(Employee)

        if employee_id:
            query = query.filter(Penalty.employee_id == employee_id)

        if department_id:
            query = query.filter(Employee.department_id == department_id)

        if penalty_type:
            query = query.filter(Penalty.penalty_type == penalty_type)

        if category:
            query = query.filter(Penalty.category == category)

        if severity:
            query = query.filter(Penalty.severity == severity)

        if status:
            query = query.filter(Penalty.status == status)

        if payment_status:
            query = query.filter(Penalty.payment_status == payment_status)

        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Penalty.penalty_date >= date_from_obj, Penalty.penalty_date <= date_to_obj)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح', 'error')
            return redirect(url_for('penalties.index'))

        penalties = query.order_by(Penalty.penalty_date.desc()).all()

        # جلب قوائم الفلترة
        employees = Employee.query.filter_by(is_active=True).order_by(Employee.full_name).all()
        departments = Department.query.order_by(Department.name).all()

        # أنواع الجزاءات
        penalty_types = [
            ('financial', 'جزاء مالي'),
            ('suspension', 'إيقاف عن العمل'),
            ('termination', 'فصل من العمل'),
            ('demotion', 'تخفيض درجة'),
            ('warning', 'إنذار'),
            ('other', 'أخرى')
        ]

        # فئات الجزاءات
        categories = [
            ('attendance', 'الحضور والغياب'),
            ('performance', 'الأداء الوظيفي'),
            ('conduct', 'السلوك المهني'),
            ('safety', 'السلامة والأمان'),
            ('policy', 'مخالفة السياسات'),
            ('financial', 'مخالفات مالية'),
            ('other', 'أخرى')
        ]

        # مستويات الخطورة
        severities = [
            ('low', 'منخفض'),
            ('medium', 'متوسط'),
            ('high', 'عالي'),
            ('critical', 'حرج')
        ]

        # حساب الإحصائيات
        total_penalties = len(penalties)
        financial_penalties = [p for p in penalties if p.penalty_type == 'financial']
        total_amount = sum([p.amount or 0 for p in financial_penalties])
        paid_amount = sum([p.amount or 0 for p in financial_penalties if p.payment_status == 'paid'])

        # إحصائيات حسب النوع
        type_stats = {}
        for penalty_type_code, penalty_type_name in penalty_types:
            type_penalties = [p for p in penalties if p.penalty_type == penalty_type_code]
            type_stats[penalty_type_code] = {
                'name': penalty_type_name,
                'count': len(type_penalties),
                'amount': sum([p.amount or 0 for p in type_penalties if p.penalty_type == 'financial'])
            }

        # إحصائيات حسب الفئة
        category_stats = {}
        for category_code, category_name in categories:
            category_penalties = [p for p in penalties if p.category == category_code]
            category_stats[category_code] = {
                'name': category_name,
                'count': len(category_penalties)
            }

        # إحصائيات حسب الخطورة
        severity_stats = {}
        for severity_code, severity_name in severities:
            severity_penalties = [p for p in penalties if p.severity == severity_code]
            severity_stats[severity_code] = {
                'name': severity_name,
                'count': len(severity_penalties)
            }

        stats = {
            'total_penalties': total_penalties,
            'active_penalties': len([p for p in penalties if p.status == 'active']),
            'completed_penalties': len([p for p in penalties if p.status == 'completed']),
            'cancelled_penalties': len([p for p in penalties if p.status == 'cancelled']),
            'financial_penalties_count': len(financial_penalties),
            'total_amount': total_amount,
            'paid_amount': paid_amount,
            'pending_amount': total_amount - paid_amount,
            'type_stats': type_stats,
            'category_stats': category_stats,
            'severity_stats': severity_stats
        }

        return render('penalties/report.html',
                             penalties=penalties,
                             employees=employees,
                             departments=departments,
                             penalty_types=penalty_types,
                             categories=categories,
                             severities=severities,
                             employee_id=employee_id,
                             department_id=department_id,
                             penalty_type=penalty_type,
                             category=category,
                             severity=severity,
                             status=status,
                             payment_status=payment_status,
                             date_from=date_from,
                             date_to=date_to,
                             stats=stats,
                             report_title="تقرير الجزاءات",
                             report_period=f"من {date_from} إلى {date_to}",
                             report_date=date.today().strftime('%Y-%m-%d'))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('penalties.index'))
