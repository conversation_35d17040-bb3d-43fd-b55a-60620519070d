#!/usr/bin/env python3
"""
أداة إصلاح Flask ونظام الشؤون القانونية
"""

import os
import sys
import subprocess
import platform
import importlib.util

def print_header():
    """طباعة رأس البرنامج"""
    print("="*60)
    print("🔧 أداة إصلاح Flask - نظام الشؤون القانونية")
    print("="*60)

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 التحقق من إصدار Python...")
    version = sys.version_info
    print(f"   الإصدار الحالي: Python {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    print("✅ إصدار Python مناسب")
    return True

def check_pip():
    """التحقق من pip"""
    print("\n📦 التحقق من pip...")
    try:
        import pip
        print("✅ pip متوفر")
        return True
    except ImportError:
        print("❌ pip غير متوفر")
        return False

def install_package(package_name):
    """تثبيت حزمة واحدة"""
    try:
        print(f"📦 تثبيت {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            package_name, "--upgrade", "--no-cache-dir"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print(f"✅ تم تثبيت {package_name}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ فشل في تثبيت {package_name}")
        return False

def check_package(package_name, import_name=None):
    """التحقق من وجود حزمة"""
    if import_name is None:
        import_name = package_name.lower().replace('-', '_')
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            return True
    except ImportError:
        pass
    return False

def install_core_packages():
    """تثبيت الحزم الأساسية"""
    print("\n🔧 تثبيت الحزم الأساسية...")
    
    core_packages = [
        ("Flask", "flask"),
        ("Flask-SQLAlchemy", "flask_sqlalchemy"),
        ("Flask-Login", "flask_login"),
        ("Werkzeug", "werkzeug"),
        ("python-dotenv", "dotenv")
    ]
    
    success_count = 0
    
    for package_name, import_name in core_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name} موجود بالفعل")
            success_count += 1
        else:
            if install_package(package_name):
                success_count += 1
    
    print(f"\n📊 تم تثبيت {success_count}/{len(core_packages)} من الحزم الأساسية")
    return success_count == len(core_packages)

def install_optional_packages():
    """تثبيت الحزم الاختيارية"""
    print("\n🔧 تثبيت الحزم الاختيارية...")
    
    optional_packages = [
        "Flask-WTF",
        "WTForms", 
        "bcrypt",
        "python-dateutil",
        "email-validator"
    ]
    
    success_count = 0
    
    for package in optional_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 تم تثبيت {success_count}/{len(optional_packages)} من الحزم الاختيارية")

def test_flask_import():
    """اختبار استيراد Flask"""
    print("\n🧪 اختبار استيراد Flask...")
    
    try:
        from flask import render
        print(f"✅ Flask {flask.__version__} يعمل بشكل صحيح")
        
        from flask import render_sqlalchemy
        print(f"✅ Flask-SQLAlchemy يعمل بشكل صحيح")
        
        from flask import render_login
        print(f"✅ Flask-Login يعمل بشكل صحيح")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_app_import():
    """اختبار استيراد التطبيق"""
    print("\n🧪 اختبار استيراد التطبيق...")
    
    try:
        # تغيير المجلد الحالي
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # محاولة استيراد التطبيق
        import app
        print("✅ تم استيراد التطبيق بنجاح")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        return False
    except Exception as e:
        print(f"⚠️  تحذير: {e}")
        return True  # قد يكون التطبيق يعمل رغم التحذير

def create_virtual_environment():
    """إنشاء بيئة افتراضية"""
    print("\n🏗️  إنشاء بيئة افتراضية...")
    
    venv_path = "venv"
    
    if os.path.exists(venv_path):
        print("✅ البيئة الافتراضية موجودة بالفعل")
        return True
    
    try:
        subprocess.check_call([sys.executable, "-m", "venv", venv_path])
        print("✅ تم إنشاء البيئة الافتراضية")
        
        # تعليمات التفعيل
        if platform.system() == "Windows":
            activate_cmd = f"{venv_path}\\Scripts\\activate.bat"
        else:
            activate_cmd = f"source {venv_path}/bin/activate"
        
        print(f"💡 لتفعيل البيئة الافتراضية:")
        print(f"   {activate_cmd}")
        
        return True
        
    except subprocess.CalledProcessError:
        print("❌ فشل في إنشاء البيئة الافتراضية")
        return False

def fix_common_issues():
    """إصلاح المشاكل الشائعة"""
    print("\n🔧 إصلاح المشاكل الشائعة...")
    
    # تحديث pip
    try:
        print("📦 تحديث pip...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], stdout=subprocess.DEVNULL)
        print("✅ تم تحديث pip")
    except:
        print("⚠️  لم يتم تحديث pip")
    
    # مسح cache
    try:
        print("🧹 مسح cache...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "cache", "purge"
        ], stdout=subprocess.DEVNULL)
        print("✅ تم مسح cache")
    except:
        print("⚠️  لم يتم مسح cache")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من Python
    if not check_python_version():
        return
    
    # التحقق من pip
    if not check_pip():
        return
    
    # إصلاح المشاكل الشائعة
    fix_common_issues()
    
    # تثبيت الحزم الأساسية
    if not install_core_packages():
        print("\n❌ فشل في تثبيت الحزم الأساسية")
        return
    
    # تثبيت الحزم الاختيارية
    install_optional_packages()
    
    # اختبار Flask
    if not test_flask_import():
        print("\n❌ Flask لا يعمل بشكل صحيح")
        return
    
    # اختبار التطبيق
    test_app_import()
    
    print("\n" + "="*60)
    print("🎉 تم إصلاح Flask بنجاح!")
    print("="*60)
    print("🚀 يمكنك الآن تشغيل التطبيق:")
    print("   python app.py")
    print("="*60)

if __name__ == "__main__":
    main()
