{% extends "base.html" %}

{% block title %}تقرير الإنذارات{% endblock %}

{% block content %}
<div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-header"><h3 class="card-title"><i class="fas fa-chart-bar"></i>
            {{ report_title }}
          </h3><div class="card-tools"><button type="button" class="btn btn-primary btn-sm" onclick="printReport()"><i class="fas fa-print"></i>
              طباعة التقرير
            </button><a href="{{ url_for('warnings.index' }}" class="btn btn-secondary btn-sm"><i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a></div></div><div class="card-body"><!-- فلاتر التقرير --><div class="card card-outline card-primary collapsed-card"><div class="card-header"><h3 class="card-title"><i class="fas fa-filter"></i>
                فلاتر التقرير
              </h3><div class="card-tools"><button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fas fa-plus"></i></button></div></div><div class="card-body"><form method="GET" action="{{ url_for('warnings_report' }}"><div class="row"><div class="col-md-3"><div class="form-group"><label for="employee_id">الموظف</label><select class="form-control" id="employee_id" name="employee_id"><option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" 
                                {% if employee_id == employee.id %}selected{% endif %}>
                          {{ employee.full_name }}
                        </option>
                        {% endfor %}
                      </select></div></div><div class="col-md-3"><div class="form-group"><label for="department_id">القسم</label><select class="form-control" id="department_id" name="department_id"><option value="">جميع الأقسام</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" 
                                {% if department_id == department.id %}selected{% endif %}>
                          {{ department.name }}
                        </option>
                        {% endfor %}
                      </select></div></div><div class="col-md-2"><div class="form-group"><label for="warning_type">نوع الإنذار</label><select class="form-control" id="warning_type" name="warning_type"><option value="">جميع الأنواع</option>
                        {% for type_code, type_name in warning_types %}
                        <option value="{{ type_code }}" {% if warning_type == type_code %}selected{% endif %}>
                          {{ type_name }}
                        </option>
                        {% endfor %}
                      </select></div></div><div class="col-md-2"><div class="form-group"><label for="date_from">من تاريخ</label><input type="date" class="form-control" id="date_from" name="date_from" 
                             value="{{ date_from }}"></div></div><div class="col-md-2"><div class="form-group"><label for="date_to">إلى تاريخ</label><input type="date" class="form-control" id="date_to" name="date_to" 
                             value="{{ date_to }}"></div></div></div><div class="row"><div class="col-12"><button type="submit" class="btn btn-primary"><i class="fas fa-search"></i>
                      تحديث التقرير
                    </button><a href="{{ url_for('warnings_report' }}" class="btn btn-secondary"><i class="fas fa-undo"></i>
                      إعادة تعيين
                    </a></div></div></form></div></div><!-- إحصائيات التقرير --><div class="row mb-4"><div class="col-md-3"><div class="info-box bg-info"><span class="info-box-icon"><i class="fas fa-list"></i></span><div class="info-box-content"><span class="info-box-text">إجمالي الإنذارات</span><span class="info-box-number">{{ stats.total_warnings }}</span></div></div></div><div class="col-md-3"><div class="info-box bg-warning"><span class="info-box-icon"><i class="fas fa-exclamation"></i></span><div class="info-box-content"><span class="info-box-text">نشطة</span><span class="info-box-number">{{ stats.active_warnings }}</span></div></div></div><div class="col-md-3"><div class="info-box bg-success"><span class="info-box-icon"><i class="fas fa-check"></i></span><div class="info-box-content"><span class="info-box-text">تم حلها</span><span class="info-box-number">{{ stats.resolved_warnings }}</span></div></div></div><div class="col-md-3"><div class="info-box bg-secondary"><span class="info-box-icon"><i class="fas fa-clock"></i></span><div class="info-box-content"><span class="info-box-text">منتهية الصلاحية</span><span class="info-box-number">{{ stats.expired_warnings }}</span></div></div></div></div><!-- إحصائيات حسب النوع والفئة --><div class="row mb-4"><div class="col-md-6"><div class="card card-outline card-success"><div class="card-header"><h3 class="card-title">إحصائيات حسب نوع الإنذار</h3></div><div class="card-body">
                  {% for type_code, type_data in stats.type_stats %}
                  <div class="progress-group">
                    {{ type_data.name }}
                    <span class="float-right"><b>{{ type_data.count }}</b>/{{ stats.total_warnings }}</span><div class="progress progress-sm"><div class="progress-bar bg-primary" 
                           data-dynamic-style="true"></div></div></div>
                  {% endfor %}
                </div></div></div><div class="col-md-6"><div class="card card-outline card-warning"><div class="card-header"><h3 class="card-title">إحصائيات حسب الفئة</h3></div><div class="card-body">
                  {% for cat_code, cat_data in stats.category_stats %}
                  <div class="progress-group">
                    {{ cat_data.name }}
                    <span class="float-right"><b>{{ cat_data.count }}</b>/{{ stats.total_warnings }}</span><div class="progress progress-sm"><div class="progress-bar bg-warning" 
                           data-dynamic-style="true"></div></div></div>
                  {% endfor %}
                </div></div></div></div><!-- جدول التقرير --><div id="reportContent"><div class="report-header text-center mb-4" style="display: none;"><h2>{{ report_title }}</h2><p><strong>الفترة:</strong> {{ report_period }}</p><p><strong>تاريخ التقرير:</strong> {{ report_date }}</p><hr></div><div class="table-responsive"><table class="table table-bordered table-striped"><thead><tr><th>الموظف</th><th>القسم</th><th>نوع الإنذار</th><th>العنوان</th><th>الفئة</th><th>تاريخ الحادثة</th><th>الخطورة</th><th>الحالة</th><th class="no-print">الوصف</th></tr></thead><tbody>
                  {% for warning in warnings %}
                  <tr><td><strong>{{ warning.employee.full_name }}</strong><br><small class="text-muted">{{ warning.employee.employee_number }}</small></td><td>
                      {% if warning.employee.department %}
                        {{ warning.employee.department.name }}
                      {% else %}
                        <span class="text-muted">-</span>
                      {% endif %}
                    </td><td>
                      {% for type_code, type_name in warning_types %}
                        {% if warning.warning_type == type_code %}
                          {{ type_name }}
                          
                        {% endif %}
                      {% endfor %}
                    </td><td><strong>{{ warning.title }}</strong></td><td>
                      {% for cat_code, cat_name in categories %}
                        {% if warning.category == cat_code %}
                          {{ cat_name }}
                          
                        {% endif %}
                      {% endfor %}
                    </td><td>{{ warning.incident_date.strftime("%Y-%m-%d") if warning.incident_date else "-" }}</td><td>
                      {% if warning.severity == 'low' %}
                        منخفض
                      {% elif warning.severity == 'medium' %}
                        متوسط
                      {% elif warning.severity == 'high' %}
                        عالي
                      {% elif warning.severity == 'critical' %}
                        حرج
                      {% endif %}
                    </td><td>
                      {% if warning.status == 'active' %}
                        نشط
                      {% elif warning.status == 'resolved' %}
                        تم الحل
                      {% elif warning.status == 'expired' %}
                        منتهي
                      {% endif %}
                    </td><td class="no-print"><small>{{ warning.description|truncate(100 }}{% if warning.description|length > 100 %}...{% endif %}</small></td></tr>
                  {% else %}
                  <tr><td colspan="9" class="text-center text-muted"><i class="fas fa-inbox fa-2x mb-2"></i><br>
                      لا توجد إنذارات في الفترة المحددة
                    </td></tr>
                  {% endfor %}
                </tbody></table></div><!-- ملخص الإحصائيات للطباعة --><div class="print-only mt-4"><h4>ملخص الإحصائيات:</h4><div class="row"><div class="col-md-6"><ul class="list-unstyled"><li><strong>إجمالي الإنذارات:</strong> {{ stats.total_warnings }}</li><li><strong>الإنذارات النشطة:</strong> {{ stats.active_warnings }}</li><li><strong>الإنذارات المحلولة:</strong> {{ stats.resolved_warnings }}</li><li><strong>الإنذارات المنتهية:</strong> {{ stats.expired_warnings }}</li></ul></div><div class="col-md-6"><h5>إحصائيات حسب النوع:</h5><ul class="list-unstyled">
                    {% for type_code, type_data in stats.type_stats %}
                    <li><strong>{{ type_data.name }}:</strong> {{ type_data.count }}</li>
                    {% endfor %}
                  </ul></div></div><h5>إحصائيات حسب الفئة:</h5><ul class="list-unstyled">
                {% for cat_code, cat_data in stats.category_stats %}
                <li><strong>{{ cat_data.name }}:</strong> {{ cat_data.count }}</li>
                {% endfor %}
              </ul><h5>إحصائيات حسب الخطورة:</h5><ul class="list-unstyled">
                {% for sev_code, sev_data in stats.severity_stats %}
                <li><strong>{{ sev_data.name) }}:</strong> {{ sev_data.count) }}</li>
                {% endfor %}
              </ul></div></div></div></div></div></div></div><style>
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .report-header {
    display: block !important;
  }
  
  .card-header,
  .card-tools,
  .btn,
  .sidebar,
  .main-header,
  .main-footer {
    display: none !important;
  }
  
  .content-wrapper {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .card {
    border: none !important;
    box-shadow: none !important;
  }
  
  .table {
    font-size: 12px;
  }
  
  .progress {
    display: none !important;
  }
}

.print-only {
  display: none;
}
</style><script>
function printReport() {
  // إظهار عناصر الطباعة
  document.querySelector('.report-header').style.display = 'block';
  
  // طباعة الصفحة
  window.print();
  
  // إخفاء عناصر الطباعة بعد الطباعة
  setTimeout() => {
    document.querySelector('.report-header').style.display = 'none';
  }, 1000);
}

// تحسين عرض التقرير
document.addEventListener('DOMContentLoaded', function() {
  // إضافة أرقام الصفوف
  const rows = document.querySelectorAll('tbody tr');
  rows.forEach(row, index) => {
    if (!row.querySelector('td[colspan]') {
      const firstCell = row.querySelector('td');
      if (firstCell) {
        firstCell.innerHTML = `<small class="text-muted">${index + 1}.</small> ${firstCell.innerHTML}`;
      }
    }
  });
});
</script>
{% endblock %}
