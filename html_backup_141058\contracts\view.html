{% extends "base.html" %}

{% block title %}عرض العقد {{ contract.contract_number }} - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-contract me-2"></i>
        عقد رقم: {{ contract.contract_number }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('contracts.edit', id=contract.id }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            {% if contract.pdf_file_path %}
            <a href="{{ url_for('contracts.download', id=contract.id }}" class="btn btn-success">
                <i class="fas fa-download me-1"></i>
                تحميل PDF
            </a>
            {% endif %}
            <button onclick="printContract()" class="btn btn-info">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
        <a href="{{ url_for('contracts.index' }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- العمود الأيسر -->
    <div class="col-md-8">
        <!-- معلومات العقد الأساسية -->
        <div class="card mb-4" id="contract-details">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات العقد
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">عنوان العقد</h6>
                        <p class="mb-3">{{ contract.title }}</p>
                        {% if contract.title_en %}
                        <h6 class="text-primary">العنوان بالإنجليزية</h6>
                        <p class="mb-3">{{ contract.title_en }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">نوع العقد</h6>
                        <p class="mb-3">
                            <span class="badge bg-info fs-6">{{ contract.contract_type }}</span>
                            {% if contract.contract_type_en %}
                            <br><small class="text-muted">{{ contract.contract_type_en }}</small>
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                {% if contract.description %}
                <div class="mb-3">
                    <h6 class="text-primary">وصف العقد</h6>
                    <p class="mb-0">{{ contract.description }}</p>
                    {% if contract.description_en %}
                    <hr>
                    <p class="text-muted mb-0"><em>{{ contract.description_en }}</em></p>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- بيانات العميل -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    بيانات العميل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">اسم العميل</h6>
                        <p class="mb-3">{{ contract.client_name }}</p>
                        
                        {% if contract.client_email %}
                        <h6 class="text-primary">البريد الإلكتروني</h6>
                        <p class="mb-3">
                            <a href="mailto:{{ contract.client_email }}">{{ contract.client_email }}</a>
                        </p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if contract.client_phone %}
                        <h6 class="text-primary">رقم الهاتف</h6>
                        <p class="mb-3">
                            <a href="tel:{{ contract.client_phone }}">{{ contract.client_phone }}</a>
                        </p>
                        {% endif %}
                        
                        {% if contract.client_address %}
                        <h6 class="text-primary">العنوان</h6>
                        <p class="mb-3">{{ contract.client_address }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- بنود العقد -->
        {% if contract.terms_and_conditions %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-ul me-2"></i>
                    بنود العقد والشروط
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">البنود والشروط</h6>
                    <div class="border rounded p-3 bg-light">
                        {{ contract.terms_and_conditions|nl2br }}
                    </div>
                </div>
                
                {% if contract.terms_and_conditions_en %}
                <div class="mb-3">
                    <h6 class="text-primary">Terms and Conditions (English)</h6>
                    <div class="border rounded p-3 bg-light">
                        {{ contract.terms_and_conditions_en|nl2br }}
                    </div>
                </div>
                {% endif %}
                
                {% if contract.payment_terms %}
                <div class="mb-3">
                    <h6 class="text-primary">شروط الدفع</h6>
                    <div class="border rounded p-3 bg-light">
                        {{ contract.payment_terms|nl2br }}
                    </div>
                </div>
                {% endif %}
                
                {% if contract.payment_terms_en %}
                <div class="mb-3">
                    <h6 class="text-primary">Payment Terms (English)</h6>
                    <div class="border rounded p-3 bg-light">
                        {{ contract.payment_terms_en|nl2br }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- ملاحظات -->
        {% if contract.notes or contract.notes_en %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
            </div>
            <div class="card-body">
                {% if contract.notes %}
                <div class="mb-3">
                    <h6 class="text-primary">ملاحظات</h6>
                    <p>{{ contract.notes|nl2br }}</p>
                </div>
                {% endif %}
                
                {% if contract.notes_en %}
                <div class="mb-3">
                    <h6 class="text-primary">Notes (English)</h6>
                    <p class="text-muted">{{ contract.notes_en|nl2br }}</p>
                </div>
                {% endif %}
                
                {% if contract.tags %}
                <div class="mb-3">
                    <h6 class="text-primary">علامات البحث</h6>
                    <p>
                        {% for tag in contract.tags.split(',') %}
                        <span class="badge bg-secondary me-1">{{ tag.strip( }}</span>
                        {% endfor %}
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- العمود الأيمن -->
    <div class="col-md-4">
        <!-- معلومات سريعة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    معلومات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">الحالة</h6>
                    {% if contract.status == 'draft' %}
                        <span class="badge bg-secondary fs-6">{{ contract.status_display }}</span>
                    {% elif contract.status == 'active' %}
                        <span class="badge bg-success fs-6">{{ contract.status_display }}</span>
                    {% elif contract.status == 'completed' %}
                        <span class="badge bg-primary fs-6">{{ contract.status_display }}</span>
                    {% elif contract.status == 'cancelled' %}
                        <span class="badge bg-danger fs-6">{{ contract.status_display }}</span>
                    {% elif contract.status == 'expired' %}
                        <span class="badge bg-warning fs-6">{{ contract.status_display }}</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <h6 class="text-primary">الأولوية</h6>
                    {% if contract.priority == 'low' %}
                        <span class="badge bg-info fs-6">{{ contract.priority_display }}</span>
                    {% elif contract.priority == 'medium' %}
                        <span class="badge bg-warning fs-6">{{ contract.priority_display }}</span>
                    {% elif contract.priority == 'high' %}
                        <span class="badge bg-danger fs-6">{{ contract.priority_display }}</span>
                    {% endif %}
                </div>
                
                {% if contract.assigned_lawyer %}
                <div class="mb-3">
                    <h6 class="text-primary">المحامي المسؤول</h6>
                    <p class="mb-0">{{ contract.assigned_lawyer.user.full_name }}</p>
                    <small class="text-muted">{{ contract.assigned_lawyer.specialization }}</small>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- التواريخ والمدة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    التواريخ والمدة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">تاريخ البداية</h6>
                    <p class="mb-0">{{ contract.start_date.start_date else '-'|strftime("%Y-%m-%d") if contract }}</p>
                </div>
                
                {% if contract.end_date %}
                <div class="mb-3">
                    <h6 class="text-primary">تاريخ النهاية</h6>
                    <p class="mb-0">{{ contract.end_date.strftime("%Y-%m-%d") if contract.end_date else "-" }}</p>
                    {% if contract.is_expired %}
                        <small class="text-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            منتهي الصلاحية
                        </small>
                    {% elif contract.days_remaining and contract.days_remaining <= 30 %}
                        <small class="text-warning">
                            <i class="fas fa-clock"></i>
                            {{ contract.days_remaining }} يوم متبقي
                        </small>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if contract.duration_months %}
                <div class="mb-3">
                    <h6 class="text-primary">المدة</h6>
                    <p class="mb-0">{{ contract.duration_months }} شهر</p>
                </div>
                {% endif %}
                
                {% if contract.signed_at %}
                <div class="mb-3">
                    <h6 class="text-primary">تاريخ التوقيع</h6>
                    <p class="mb-0">{{ contract.signed_at.strftime("%Y-%m-%d %H:%M") if contract.signed_at else "-" }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- المبالغ المالية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill me-2"></i>
                    المبالغ المالية
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">إجمالي المبلغ</h6>
                    <h4 class="text-success mb-0">
                        {{ "{:,.2f}".format(contract.total_amount ) }}
                        <small class="text-muted">{{ contract.currency }}</small>
                    </h4>
                </div>
            </div>
        </div>
        
        <!-- معلومات الملف -->
        {% if contract.pdf_file_path %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-pdf me-2"></i>
                    ملف العقد
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">اسم الملف</h6>
                    <p class="mb-0">{{ contract.original_filename }}</p>
                </div>
                
                {% if contract.file_size %}
                <div class="mb-3">
                    <h6 class="text-primary">حجم الملف</h6>
                    <p class="mb-0">{{ "%.2f".format(contract.file_size / 1024 / 1024 }} MB</p>
                </div>
                {% endif %}
                
                <div class="d-grid">
                    <a href="{{ url_for('contracts.download', id=contract.id) }}" class="btn btn-success">
                        <i class="fas fa-download me-1"></i>
                        تحميل الملف
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- معلومات النظام -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">تاريخ الإنشاء</h6>
                    <p class="mb-0">{{ contract.created_at.strftime("%Y-%m-%d %H:%M") if contract.created_at else "-" }}</p>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-primary">آخر تحديث</h6>
                    <p class="mb-0">{{ contract.updated_at.strftime("%Y-%m-%d %H:%M") if contract.updated_at else "-" }}</p>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-primary">أنشأ بواسطة</h6>
                    <p class="mb-0">{{ contract.created_by.full_name }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printContract() {
    // إخفاء العناصر غير المرغوب فيها في الطباعة
    const printContent = document.getElementById('contract-details').cloneNode(true);
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <html>
        <head>
            <title>عقد رقم: {{ contract.contract_number }}</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .card { border: 1px solid #ddd; margin-bottom: 20px; }
                .card-header { background-color: #f8f9fa; padding: 10px; border-bottom: 1px solid #ddd; }
                .card-body { padding: 15px; }
                h5 { margin: 0; color: #333; }
                h6 { color: #007bff; margin-bottom: 5px; }
                p { margin-bottom: 10px; }
                .badge { background-color: #007bff; color: white; padding: 5px 10px; border-radius: 5px; }
                @media print {
                    body { margin: 0; }
                    .card { break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <h1 style="text-align: center; color: #333;">عقد رقم: {{ contract.contract_number }}</h1>
            <h2 style="text-align: center; color: #666;">{{ contract.title }}</h2>
            <hr>
            ${printContent.innerHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}
</script>

<style>
@media print {
    .btn-toolbar, .card-header .btn, .no-print {
        display: none !important;
    }
}
</style>
{% endblock %}
