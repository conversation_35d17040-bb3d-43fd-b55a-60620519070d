#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل مفصل للعلاقات في النماذج
"""

import re
from collections import defaultdict, Counter

def analyze_model_relationships():
    """تحليل مفصل للعلاقات في النماذج"""
    
    print("🔍 تحليل العلاقات في النماذج...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تقسيم إلى كلاسات
        class_pattern = r'class\s+(\w+)\(.*?db\.Model.*?\):(.*?)(?=class\s+\w+|def\s+create_models|$)'
        classes = re.findall(class_pattern, content, re.DOTALL)
        
        relationships = {
            'foreign_keys': {},
            'relationships': {},
            'back_references': {},
            'many_to_many': {},
            'circular_refs': [],
            'orphaned_refs': []
        }
        
        model_tables = {}
        
        for class_name, class_content in classes:
            # استخراج اسم الجدول
            table_match = re.search(r"__tablename__\s*=\s*['\"]([^'\"]+)['\"]", class_content)
            table_name = table_match.group(1) if table_match else class_name.lower()
            model_tables[class_name] = table_name
            
            # استخراج المفاتيح الخارجية
            fk_pattern = r'(\w+)\s*=\s*db\.Column\([^,]*db\.ForeignKey\([\'"]([^\'\"]+)[\'"]\)'
            foreign_keys = re.findall(fk_pattern, class_content)
            if foreign_keys:
                relationships['foreign_keys'][class_name] = foreign_keys
            
            # استخراج العلاقات
            rel_pattern = r'(\w+)\s*=\s*db\.relationship\([\'"]([^\'\"]+)[\'"](?:.*?backref=[\'"]([^\'\"]+)[\'"])?'
            relations = re.findall(rel_pattern, class_content)
            if relations:
                relationships['relationships'][class_name] = relations
            
            # استخراج many-to-many
            m2m_pattern = r'(\w+)\s*=\s*db\.Table\('
            m2m_tables = re.findall(m2m_pattern, class_content)
            if m2m_tables:
                relationships['many_to_many'][class_name] = m2m_tables
        
        return relationships, model_tables
        
    except Exception as e:
        print(f"❌ خطأ في تحليل العلاقات: {e}")
        return {}, {}

def check_relationship_consistency(relationships, model_tables):
    """فحص اتساق العلاقات"""
    
    print("🔍 فحص اتساق العلاقات...")
    
    issues = []
    warnings = []
    
    # فحص المفاتيح الخارجية
    for model, foreign_keys in relationships['foreign_keys'].items():
        for field_name, reference in foreign_keys:
            # تحليل المرجع
            if '.' in reference:
                ref_table, ref_field = reference.split('.', 1)
                
                # التحقق من وجود الجدول المرجعي
                ref_model = None
                for m, t in model_tables.items():
                    if t == ref_table:
                        ref_model = m
                        break
                
                if not ref_model:
                    issues.append(f"❌ {model}.{field_name}: يشير إلى جدول غير موجود '{ref_table}'")
                else:
                    # التحقق من وجود الحقل المرجعي
                    if ref_field != 'id':  # معظم المراجع تشير إلى id
                        warnings.append(f"⚠️ {model}.{field_name}: يشير إلى حقل غير معياري '{ref_field}'")
    
    # فحص العلاقات المتبادلة
    for model, relations in relationships['relationships'].items():
        for rel_name, target_model, backref in relations:
            # التحقق من وجود النموذج المستهدف
            if target_model not in model_tables:
                issues.append(f"❌ {model}.{rel_name}: يشير إلى نموذج غير موجود '{target_model}'")
    
    return issues, warnings

def analyze_relationship_patterns(relationships):
    """تحليل أنماط العلاقات"""
    
    print("📊 تحليل أنماط العلاقات...")
    
    patterns = {
        'one_to_many': [],
        'many_to_one': [],
        'one_to_one': [],
        'many_to_many': [],
        'self_referencing': []
    }
    
    # تحليل المفاتيح الخارجية
    fk_targets = Counter()
    for model, foreign_keys in relationships['foreign_keys'].items():
        for field_name, reference in foreign_keys:
            fk_targets[reference] += 1
            
            # فحص المراجع الذاتية
            if reference.startswith(model_tables.get(model, model.lower())):
                patterns['self_referencing'].append(f"{model}.{field_name}")
    
    # العلاقات الأكثر استخداماً
    most_referenced = fk_targets.most_common(5)
    
    return patterns, most_referenced

def generate_relationship_report():
    """إنشاء تقرير العلاقات"""
    
    print("📋 إنشاء تقرير العلاقات...")
    
    relationships, model_tables = analyze_model_relationships()
    issues, warnings = check_relationship_consistency(relationships, model_tables)
    patterns, most_referenced = analyze_relationship_patterns(relationships)
    
    report = []
    report.append("=" * 60)
    report.append("🔗 تقرير تحليل العلاقات في النماذج")
    report.append("=" * 60)
    
    # ملخص عام
    total_fks = sum(len(fks) for fks in relationships['foreign_keys'].values())
    total_rels = sum(len(rels) for rels in relationships['relationships'].values())
    
    report.append(f"\n📈 ملخص عام:")
    report.append(f"  - عدد النماذج: {len(model_tables)}")
    report.append(f"  - إجمالي المفاتيح الخارجية: {total_fks}")
    report.append(f"  - إجمالي العلاقات: {total_rels}")
    report.append(f"  - مشاكل العلاقات: {len(issues)}")
    report.append(f"  - تحذيرات: {len(warnings)}")
    
    # النماذج والجداول
    report.append(f"\n📋 النماذج والجداول:")
    for model, table in sorted(model_tables.items()):
        report.append(f"  - {model} → {table}")
    
    # المفاتيح الخارجية
    if relationships['foreign_keys']:
        report.append(f"\n🔑 المفاتيح الخارجية:")
        for model, fks in relationships['foreign_keys'].items():
            for field_name, reference in fks:
                report.append(f"  - {model}.{field_name} → {reference}")
    
    # العلاقات
    if relationships['relationships']:
        report.append(f"\n🔗 العلاقات:")
        for model, rels in relationships['relationships'].items():
            for rel_name, target_model, backref in rels:
                backref_info = f" (backref: {backref})" if backref else ""
                report.append(f"  - {model}.{rel_name} → {target_model}{backref_info}")
    
    # الجداول الأكثر مرجعية
    if most_referenced:
        report.append(f"\n📊 الجداول الأكثر مرجعية:")
        for reference, count in most_referenced:
            report.append(f"  - {reference}: {count} مرجع")
    
    # المشاكل
    if issues:
        report.append(f"\n❌ مشاكل العلاقات ({len(issues)}):")
        for issue in issues:
            report.append(f"  {issue}")
    
    # التحذيرات
    if warnings:
        report.append(f"\n⚠️ تحذيرات ({len(warnings)}):")
        for warning in warnings:
            report.append(f"  {warning}")
    
    # النتيجة النهائية
    if not issues:
        report.append(f"\n🎉 ممتاز! جميع العلاقات صحيحة ومتسقة")
    else:
        report.append(f"\n⚠️ يوجد {len(issues)} مشكلة في العلاقات تحتاج إصلاح")
    
    return "\n".join(report)

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء تحليل العلاقات...")
    
    # إنشاء التقرير
    report = generate_relationship_report()
    
    # طباعة التقرير
    print(report)
    
    # حفظ التقرير
    with open('relationship_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 تم حفظ التقرير في: relationship_analysis_report.txt")

if __name__ == "__main__":
    main()
