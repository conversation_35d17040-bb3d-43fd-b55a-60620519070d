{% extends "base.html" %} {% block title %}إعدادات التقارير - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
><h1 class="h2"><i class="fas fa-file-alt me-2"></i>
    إعدادات التقارير
  </h1><div class="btn-toolbar mb-2 mb-md-0"><div class="btn-group me-2"><a
        href="{{ url_for('settings.index' }}"
        class="btn btn-outline-secondary"
  ><i class="fas fa-arrow-right me-1"></i>
        العودة للإعدادات
      </a><button onclick="previewReport()" class="btn btn-info"><i class="fas fa-eye me-1"></i>
        معاينة التقرير
      </button></div></div></div><form id="reportsForm"><div class="row"><div class="col-md-8"><!-- إعدادات عرض التقارير --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-eye me-2"></i>
            إعدادات عرض التقارير
          </h5></div><div class="card-body"><div class="row"><div class="col-md-6"><div class="form-check form-switch mb-3"><input class="form-check-input" type="checkbox" id="show_logo"
                name="show_logo" {{ 'checked' if show_logo == 'true' else '' }}><label class="form-check-label" for="show_logo"><strong>إظهار شعار الشركة</strong></label><div class="form-text">عرض شعار الشركة في رأس التقرير</div></div></div><div class="col-md-6"><div class="form-check form-switch mb-3"><input class="form-check-input" type="checkbox"
                id="show_company_info" name="show_company_info" {{ 'checked' if show_company_info == 'true' else '' }}><label class="form-check-label" for="show_company_info"><strong>إظهار معلومات الشركة</strong></label><div class="form-text">عرض اسم ووصف الشركة</div></div></div></div><div class="row"><div class="col-md-6"><div class="form-check form-switch mb-3"><input class="form-check-input" type="checkbox"
                id="show_print_time" name="show_print_time" {{ 'checked' if show_print_time == 'true' else '' }}><label class="form-check-label" for="show_print_time"><strong>إظهار وقت الطباعة</strong></label><div class="form-text">عرض تاريخ ووقت طباعة التقرير</div></div></div><div class="col-md-6"><div class="form-check form-switch mb-3"><input class="form-check-input" type="checkbox"
                id="show_signatures" name="show_signatures" {{ 'checked' if show_signatures == 'true' else '' }}><label class="form-check-label" for="show_signatures"><strong>إظهار التوقيعات</strong></label><div class="form-text">
                  عرض مربعات التوقيعات في نهاية التقرير
                </div></div></div></div></div></div><!-- إعدادات النصوص --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-font me-2"></i>
            إعدادات النصوص
          </h5></div><div class="card-body"><div class="mb-3"><label for="footer_text" class="form-label">نص ذيل التقرير</label><textarea
              class="form-control"
              id="footer_text"
              name="footer_text"
              rows="3"
              placeholder="النص الذي يظهر في أسفل التقرير"
        >
{{ footer_text }}</textarea><div class="form-text">
              هذا النص سيظهر في أسفل جميع التقارير المطبوعة
            </div></div></div></div><!-- معاينة الإعدادات --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-eye me-2"></i>
            معاينة التقرير
          </h5></div><div class="card-body"><div
            id="reportPreview"
            class="border rounded p-3"
            style="background-color: #f8f9fa; min-height: 300px"
      ><!-- معاينة رأس التقرير --><div id="previewHeader" class="text-center mb-3"><div
                id="previewLogo"
                class="{{ 'd-block' if show_logo == 'true' else 'd-none' }}"
          ><img
                  src="{{ url_for ('static', filename=logo_path or 'images/logo.svg' }}"
                  alt="شعار الشركة"
                  style="max-height: 60px; max-width: 150px"
                /></div><div
                id="previewCompanyInfo"
                class="{{ 'd-block' if show_company_info == 'true' else 'd-none' }}"
          ><h6 class="mt-2 mb-1">
                 ) {{ company_name or 'مكتب الشؤون القانونية' }}
                </h6><small class="text-muted"
              >{{ company_subtitle or 'نظام إدارة الموارد البشرية والشؤون القانونية' }}</small></div><hr /><h6>عنوان التقرير</h6><div
                id="previewPrintTime"
                class="{{ 'd-block' if show_print_time == 'true' else 'd-none' }}"
          ><small class="text-muted"
              >تاريخ الطباعة: <span id="currentDateTime"></span></small></div></div><!-- محتوى التقرير (عينة) --><div class="mb-3"><table class="table table-sm table-bordered"><thead class="table-dark"><tr><th>البيان</th><th>القيمة</th></tr></thead><tbody><tr><td>عدد الموظفين</td><td>25</td></tr><tr><td>إجمالي الرواتب</td><td>125,000 ريال</td></tr></tbody></table></div><!-- معاينة التوقيعات --><div
              id="previewSignatures"
              class="{{ 'd-block' if show_signatures == 'true' else 'd-none' }}"
        ><div class="row mt-4"><div class="col-4 text-center"><div
                    style="
                      border-top: 1px solid #333;
                      margin: 20px 10px 5px 10px;
                    "
              ></div><small><strong>إعداد التقرير</strong></small></div><div class="col-4 text-center"><div
                    style="
                      border-top: 1px solid #333;
                      margin: 20px 10px 5px 10px;
                    "
              ></div><small><strong>مراجعة</strong></small></div><div class="col-4 text-center"><div
                    style="
                      border-top: 1px solid #333;
                      margin: 20px 10px 5px 10px;
                    "
              ></div><small><strong>اعتماد</strong></small></div></div></div><!-- معاينة الذيل --><div
              class="text-center mt-4 pt-3"
              style="border-top: 1px solid #dee2e6"
        ><small class="text-muted" id="previewFooter"
            >{{ footer_text or 'تم إنشاء هذا التقرير تلقائياً بواسطة النظام' }}</small></div></div></div></div></div><div class="col-md-4"><!-- أزرار الحفظ --><div class="card mb-4"><div class="card-body"><div class="d-grid gap-2"><button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i>
              حفظ الإعدادات
            </button><a
              href="{{ url_for('settings.index' }}"
              class="btn btn-outline-secondary"
        ><i class="fas fa-times me-1"></i>
              إلغاء
            </a><button
              type="button"
              onclick="resetSettings()"
              class="btn btn-outline-danger"
        ><i class="fas fa-undo me-1"></i>
              إعادة تعيين
            </button></div></div></div><!-- نصائح --><div class="card"><div class="card-header"><h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>
            نصائح مفيدة
          </h6></div><div class="card-body"><small class="text-muted"><ul class="mb-0"><li>يمكن إخفاء أي عنصر من التقرير حسب الحاجة</li><li>التغييرات تطبق فوراً على جميع التقارير الجديدة</li><li>استخدم المعاينة لرؤية شكل التقرير النهائي</li><li>نص الذيل يظهر في جميع التقارير المطبوعة</li><li>التوقيعات مفيدة للتقارير الرسمية</li></ul></small></div></div></div></div></form><script>
  // تحديث الوقت الحالي
  function updateCurrentTime() {
    const now = new Date();
    const dateTimeString =
      now.toLocaleDateString("ar-SA") +
      " " +
      now.toLocaleTimeString("ar-SA", { hour12: false });
    const element = document.getElementById("currentDateTime");
    if (element) {
      element.textContent = dateTimeString;
    }
  }

  // تحديث المعاينة عند تغيير الإعدادات
  function updatePreview() {
    const showLogo = document.getElementById("show_logo").checked;
    const showCompanyInfo =
      document.getElementById("show_company_info").checked;
    const showPrintTime = document.getElementById("show_print_time").checked;
    const showSignatures = document.getElementById("show_signatures").checked;
    const footerText = document.getElementById("footer_text").value;

    // استخدام Bootstrap classes بدلاً من inline styles
    const logoElement = document.getElementById("previewLogo");
    logoElement.className = showLogo ? "d-block" : "d-none";

    const companyInfoElement = document.getElementById("previewCompanyInfo");
    companyInfoElement.className = showCompanyInfo ? "d-block" : "d-none";

    const printTimeElement = document.getElementById("previewPrintTime");
    printTimeElement.className = showPrintTime ? "d-block" : "d-none";

    const signaturesElement = document.getElementById("previewSignatures");
    signaturesElement.className = showSignatures ? "d-block" : "d-none";

    document.getElementById("previewFooter").textContent =
      footerText || "تم إنشاء هذا التقرير تلقائياً بواسطة النظام";
  }

  // ربط الأحداث
  document.addEventListener("DOMContentLoaded", function () {
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);

    // ربط أحداث التغيير
    document
      .getElementById("show_logo")
      .addEventListener("change", updatePreview);
    document
      .getElementById("show_company_info")
      .addEventListener("change", updatePreview);
    document
      .getElementById("show_print_time")
      .addEventListener("change", updatePreview);
    document
      .getElementById("show_signatures")
      .addEventListener("change", updatePreview);
    document
      .getElementById("footer_text")
      .addEventListener("input", updatePreview);
  });

  // حفظ إعدادات التقارير
  document
    .getElementById("reportsForm")
    .addEventListener("submit", function (e) {
      e.preventDefault();

      const formData = new FormData(this);
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;

      // تعطيل الزر وإظهار مؤشر التحميل
      submitBtn.disabled = true;
      submitBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...';

      fetch("/settings/reports/save", {
        method: "POST",
        body: formData,
      })
        .then(response) => response.json()
        .then(data) => {
          if (data.success) {
            showAlert("success", data.message);
          } else {
            showAlert("error", data.message);
          }
        })
        .catch(error) => {
          console.error("Error:", error);
          showAlert("error", "حدث خطأ في الاتصال بالخادم");
        })
        .finally() => {
          // إعادة تفعيل الزر
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalText;
        });
    });

  function previewReport() {
    // فتح نافذة جديدة لمعاينة التقرير
    window.open("/employees/", "_blank");
  }

  function resetSettings() {
    if (
      confirm(
        "هل أنت متأكد من إعادة تعيين جميع إعدادات التقارير للقيم الافتراضية؟"
      )
    ) {
      fetch("/settings/reset", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: "category=reports",
      })
        .then(response) => response.json()
        .then(data) => {
          if (data.success) {
            showAlert("success", data.message);
            setTimeout() => location.reload(), 1500);
          } else {
            showAlert("error", data.message);
          }
        })
        .catch(error) => {
          console.error("Error:", error);
          showAlert("error", "حدث خطأ في الاتصال بالخادم");
        });
    }
  }

  function showAlert(type, message) {
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${
      type === "error" ? "danger" : type
    } alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container =
      document.querySelector(".container-fluid") || document.body;
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }
</script>
{% endblock %}
