@echo off
chcp 65001 > nul
echo ========================================
echo    تشغيل نظام الشؤون القانونية
echo ========================================
echo.

echo 🔍 فحص التبعيات المطلوبة...
python -c "import flask, flask_sqlalchemy, flask_login" 2>nul
if errorlevel 1 (
    echo ❌ بعض التبعيات مفقودة، جاري تثبيتها...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت التبعيات
        echo يرجى تشغيل: pip install -r requirements.txt
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ جميع التبعيات متوفرة
)

echo.
echo 🚀 بدء تشغيل التطبيق...
echo.
echo ========================================
echo   معلومات الوصول للنظام:
echo ========================================
echo 🌐 الرابط المحلي: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo ========================================
echo.
echo 📝 ملاحظة: اتركي هذه النافذة مفتوحة أثناء استخدام النظام
echo 🔄 لإيقاف النظام: اضغط Ctrl+C
echo.

python app.py
