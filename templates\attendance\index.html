{% extends "base.html" %}

{% block title %}الحضور والغياب{% endblock %}

{% block content %}
<div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-header"><h3 class="card-title"><i class="fas fa-clock"></i>
            إدارة الحضور والغياب
          </h3><div class="card-tools"><a href="{{ url_for ('attendance_add' }}" class="btn btn-primary btn-sm"><i class="fas fa-plus"></i>
              إضافة سجل حضور
            </a><a href="{{ url_for('attendance_report' }}" class="btn btn-info btn-sm"><i class="fas fa-chart-bar"></i>
              تقرير الحضور
            </a></div></div><!-- إحصائيات سريعة --><div class="card-body"><div class="row mb-3"><div class="col-md-3"><div class="info-box bg-success"><span class="info-box-icon"><i class="fas fa-check"></i></span><div class="info-box-content"><span class="info-box-text">حاضر اليوم</span><span class="info-box-number">{{ stats.today_present }}</span></div></div></div><div class="col-md-3"><div class="info-box bg-danger"><span class="info-box-icon"><i class="fas fa-times"></i></span><div class="info-box-content"><span class="info-box-text">غائب اليوم</span><span class="info-box-number">{{ stats.today_absent }}</span></div></div></div><div class="col-md-3"><div class="info-box bg-warning"><span class="info-box-icon"><i class="fas fa-clock"></i></span><div class="info-box-content"><span class="info-box-text">متأخر اليوم</span><span class="info-box-number">{{ stats.today_late }}</span></div></div></div><div class="col-md-3"><div class="info-box bg-info"><span class="info-box-icon"><i class="fas fa-list"></i></span><div class="info-box-content"><span class="info-box-text">إجمالي السجلات</span><span class="info-box-number">{{ stats.total_records }}</span></div></div></div></div><!-- فلاتر البحث --><div class="card card-outline card-primary collapsed-card"><div class="card-header"><h3 class="card-title"><i class="fas fa-filter"></i>
                فلاتر البحث
              </h3><div class="card-tools"><button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fas fa-plus"></i></button></div></div><div class="card-body"><form method="GET" action="{{ url_for('attendance.index' }}"><div class="row"><div class="col-md-3"><div class="form-group"><label for="search">البحث</label><input type="text" class="form-control" id="search" name="search" 
                             value="{{ search }}" placeholder="اسم الموظف..."></div></div><div class="col-md-3"><div class="form-group"><label for="employee_id">الموظف</label><select class="form-control" id="employee_id" name="employee_id"><option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" 
                                {% if employee_id == employee.id %}selected{% endif %}>
                          {{ employee.full_name }}
                        </option>
                        {% endfor %}
                      </select></div></div><div class="col-md-2"><div class="form-group"><label for="status">الحالة</label><select class="form-control" id="status" name="status"><option value="">جميع الحالات</option><option value="present" {% if status == 'present' %}selected{% endif %}>حاضر</option><option value="absent" {% if status == 'absent' %}selected{% endif %}>غائب</option><option value="late" {% if status == 'late' %}selected{% endif %}>متأخر</option><option value="half_day" {% if status == 'half_day' %}selected{% endif %}>نصف يوم</option></select></div></div><div class="col-md-2"><div class="form-group"><label for="date_from">من تاريخ</label><input type="date" class="form-control" id="date_from" name="date_from" 
                             value="{{ date_from }}"></div></div><div class="col-md-2"><div class="form-group"><label for="date_to">إلى تاريخ</label><input type="date" class="form-control" id="date_to" name="date_to" 
                             value="{{ date_to }}"></div></div></div><div class="row"><div class="col-12"><button type="submit" class="btn btn-primary"><i class="fas fa-search"></i>
                      بحث
                    </button><a href="{{ url_for('attendance.index' }}" class="btn btn-secondary"><i class="fas fa-undo"></i>
                      إعادة تعيين
                    </a></div></div></form></div></div><!-- جدول سجلات الحضور --><div class="table-responsive"><table class="table table-bordered table-striped"><thead><tr><th>التاريخ</th><th>الموظف</th><th>وقت الحضور</th><th>وقت الانصراف</th><th>ساعات العمل</th><th>الحالة</th><th>الموقع</th><th>الإجراءات</th></tr></thead><tbody>
               ) {% for record in attendance_records %}
                <tr><td>{{ record.date.strftime("%Y-%m-%d") if record.date else "-" }}</td><td><strong>{{ record.employee.full_name }}</strong><br><small class="text-muted">{{ record.employee.employee_number }}</small></td><td>
                    {% if record.check_in %}
                      <span class="badge badge-success">
                        {{ record.check_in.strftime("%H:%M") if record.check_in else "-" }}
                      </span>
                    {% else %}
                      <span class="badge badge-secondary">-</span>
                    {% endif %}
                  </td><td>
                    {% if record.check_out %}
                      <span class="badge badge-info">
                        {{ record.check_out.strftime("%H:%M") if record.check_out else "-" }}
                      </span>
                    {% else %}
                      <span class="badge badge-secondary">-</span>
                    {% endif %}
                  </td><td>
                    {% if record.hours_worked %}
                      <strong>{{ "%.2f".format(record.hours_worked }} ساعة</strong>
                    {% else %}
                      <span class="text-muted">-</span>
                    {% endif %}
                  </td><td>
                    {% if record.status == 'present' %}
                      <span class="badge badge-success">حاضر</span>
                    {% elif record.status == 'absent' %}
                      <span class="badge badge-danger">غائب</span>
                    {% elif record.status == 'late' %}
                      <span class="badge badge-warning">متأخر</span>
                    {% elif record.status == 'half_day' %}
                      <span class="badge badge-info">نصف يوم</span>
                    {% else %}
                      <span class="badge badge-secondary">{{ record.status }}</span>
                    {% endif %}
                  </td><td><small class="text-muted">{{ record.location or '-') }}</small></td><td><div class="btn-group btn-group-sm"><a href="{{ url_for('attendance.edit', attendance_id=record.id) }}" 
                         class="btn btn-warning btn-sm" title="تعديل"><i class="fas fa-edit"></i></a><button type="button" class="btn btn-danger btn-sm"
                              onclick="deleteAttendance('{{ record.id) }}')" title="حذف"><i class="fas fa-trash"></i></button></div></td></tr>
                {% else %}
                <tr><td colspan="8" class="text-center text-muted"><i class="fas fa-inbox fa-2x mb-2"></i><br>
                    لا توجد سجلات حضور
                  </td></tr>
                {% endfor %}
              </tbody></table></div></div></div></div></div></div><!-- تسجيل حضور سريع --><div class="modal fade" id="quickCheckinModal" tabindex="-1"><div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h4 class="modal-title">تسجيل حضور سريع</h4><button type="button" class="close" data-dismiss="modal"><span>&times;</span></button></div><form id="quickCheckinForm"><div class="modal-body"><div class="form-group"><label for="quick_employee_id">الموظف</label><select class="form-control" id="quick_employee_id" name="employee_id" required><option value="">اختر الموظف</option>
              {% for employee in employees %}
              <option value="{{ employee.id }}">{{ employee.full_name }}</option>
              {% endfor %}
            </select></div></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button><button type="submit" class="btn btn-primary">تسجيل</button></div></form></div></div></div><script>
// حذف سجل حضور
function deleteAttendance(id) {
  if (confirm('هل أنت متأكد من حذف سجل الحضور؟') {
    fetch(`/attendance/${id}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json()
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('خطأ: ' + data.message);
      }
    })
    .catch(error => {
      alert('حدث خطأ في الاتصال');
    });
  }
}

// تسجيل حضور سريع
document.getElementById('quickCheckinForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  
  fetch('/attendance/quick-checkin', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json()
  .then(data => {
    if (data.success) {
      $('#quickCheckinModal').modal('hide');
      location.reload();
    } else {
      alert('خطأ: ' + data.message);
    }
  })
  .catch(error => {
    alert('حدث خطأ في الاتصال');
  });
});
</script>
{% endblock %}
