============================================================
🔗 تقرير تحليل العلاقات في النماذج
============================================================

📈 ملخص عام:
  - عدد النماذج: 23
  - إجمالي المفاتيح الخارجية: 0
  - إجمالي العلاقات: 42
  - مشاكل العلاقات: 0
  - تحذيرات: 0

📋 النماذج والجداول:
  - Appointment → appointments
  - Attendance → attendance
  - Case → cases
  - CaseNote → case_notes
  - CaseSession → case_sessions
  - CaseTimeline → case_timeline
  - Client → clients
  - ClientFile → client_files
  - Contract → contracts
  - Department → departments
  - Document → documents
  - Employee → employees
  - EmployeeDocument → employee_documents
  - Invoice → invoices
  - Lawyer → lawyers
  - LeaveRequest → leave_requests
  - Payment → payments
  - Penalty → penalties
  - Permission → permissions
  - Role → roles
  - SystemSettings → system_settings
  - User → users
  - Warning → warnings

🔗 العلاقات:
  - Role.permissions → Permission (backref: roles)
  - Role.users → User (backref: user_role)
  - User.lawyer_profile → Lawyer (backref: user)
  - User.uploaded_documents → Document (backref: uploader)
  - User.case_notes → CaseNote (backref: author)
  - User.timeline_events → CaseTimeline (backref: creator)
  - User.permissions → Permission (backref: users)
  - Client.cases → Case (backref: client)
  - Client.appointments → Appointment (backref: client)
  - Client.invoices → Invoice (backref: client)
  - Client.client_files → ClientFile (backref: client)
  - ClientFile.uploader → User (backref: uploaded_client_files)
  - Lawyer.cases → Case (backref: lawyer)
  - Lawyer.appointments → Appointment (backref: lawyer)
  - Case.sessions → CaseSession (backref: case)
  - Case.documents → Document (backref: case)
  - Case.invoices → Invoice (backref: case)
  - Case.notes_list → CaseNote (backref: case)
  - Case.timeline → CaseTimeline (backref: case)
  - Case.appointments → Appointment (backref: case)
  - Invoice.payments → Payment (backref: invoice)
  - Contract.client → Client (backref: contracts)
  - Contract.assigned_lawyer → Lawyer (backref: assigned_contracts)
  - Contract.created_by → User (backref: created_contracts)
  - Department.employees → Employee (backref: department)
  - Employee.user → User (backref: employee_profile)
  - Employee.creator → User
  - Employee.attendances → Attendance
  - Employee.penalties → Penalty
  - Employee.warnings → Warning
  - Employee.leave_requests → LeaveRequest
  - Employee.documents → EmployeeDocument (backref: employee)
  - EmployeeDocument.uploader → User (backref: uploaded_employee_documents)
  - Attendance.employee → Employee
  - Attendance.creator → User (backref: created_attendance)
  - LeaveRequest.employee → Employee
  - LeaveRequest.approver → User (backref: approved_leaves)
  - Warning.employee → Employee
  - Warning.creator → User (backref: created_warnings)
  - Penalty.employee → Employee
  - Penalty.creator → User (backref: created_penalties)
  - SystemSettings.updater → User (backref: updated_settings)

🎉 ممتاز! جميع العلاقات صحيحة ومتسقة