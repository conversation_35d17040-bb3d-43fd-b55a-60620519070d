{% extends "base.html" %}

{% block title %}العملاء - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2"><i class="fas fa-users me-2"></i>
        إدارة العملاء
    </h1><div class="btn-toolbar mb-2 mb-md-0"><div class="btn-group me-2"><a href="{{ url_for('clients.add' }}" class="btn btn-primary"><i class="fas fa-plus me-1"></i>
                إضافة عميل جديد
            </a><button onclick="exportClients()" class="btn btn-outline-success"><i class="fas fa-download me-1"></i>
                تصدير
            </button></div></div></div><!-- إحصائيات سريعة --><div class="row mb-4"><div class="col-md-3"><div class="card text-white bg-primary"><div class="card-body"><div class="d-flex justify-content-between"><div><h4 class="card-title">{{ stats.total }}</h4><p class="card-text">إجمالي العملاء</p></div><div class="align-self-center"><i class="fas fa-users fa-2x"></i></div></div></div></div></div><div class="col-md-3"><div class="card text-white bg-success"><div class="card-body"><div class="d-flex justify-content-between"><div><h4 class="card-title">{{ stats.active }}</h4><p class="card-text">العملاء النشطون</p></div><div class="align-self-center"><i class="fas fa-user-check fa-2x"></i></div></div></div></div></div><div class="col-md-3"><div class="card text-white bg-info"><div class="card-body"><div class="d-flex justify-content-between"><div><h4 class="card-title">{{ stats.individuals }}</h4><p class="card-text">أفراد</p></div><div class="align-self-center"><i class="fas fa-user fa-2x"></i></div></div></div></div></div><div class="col-md-3"><div class="card text-white bg-warning"><div class="card-body"><div class="d-flex justify-content-between"><div><h4 class="card-title">{{ stats.companies }}</h4><p class="card-text">شركات</p></div><div class="align-self-center"><i class="fas fa-building fa-2x"></i></div></div></div></div></div></div><!-- فلاتر البحث --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-search me-2"></i>
            البحث والفلترة
        </h5></div><div class="card-body"><form method="GET" action="{{ url_for('clients.index' }}"><div class="row"><div class="col-md-4"><div class="mb-3"><label for="search" class="form-label">البحث</label><input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="الاسم، البريد، الهاتف، رقم الهوية..."></div></div><div class="col-md-2"><div class="mb-3"><label for="client_type" class="form-label">نوع العميل</label><select class="form-select" id="client_type" name="client_type"><option value="">جميع الأنواع</option><option value="individual" {{ 'selected' if client_type == 'individual' }}>فرد</option><option value="company" {{ 'selected' if client_type == 'company' }}>شركة</option><option value="organization" {{ 'selected' if client_type == 'organization' }}>مؤسسة</option></select></div></div><div class="col-md-2"><div class="mb-3"><label for="status" class="form-label">الحالة</label><select class="form-select" id="status" name="status"><option value="">جميع الحالات</option><option value="active" {{ 'selected' if status_filter == 'active' }}>نشط</option><option value="inactive" {{ 'selected' if status_filter == 'inactive' }}>غير نشط</option></select></div></div><div class="col-md-2"><div class="mb-3"><label for="sort" class="form-label">ترتيب حسب</label><select class="form-select" id="sort" name="sort"><option value="created_at" {{ 'selected' if sort_by == 'created_at' }}>تاريخ الإنشاء</option><option value="name" {{ 'selected' if sort_by == 'name' }}>الاسم</option><option value="client_code" {{ 'selected' if sort_by == 'client_code' }}>رمز العميل</option></select></div></div><div class="col-md-1"><div class="mb-3"><label for="order" class="form-label">الترتيب</label><select class="form-select" id="order" name="order"><option value="desc" {{ 'selected' if sort_order == 'desc' }}>تنازلي</option><option value="asc" {{ 'selected' if sort_order == 'asc' }}>تصاعدي</option></select></div></div><div class="col-md-1"><div class="mb-3"><label class="form-label"></label><div class="d-grid"><button type="submit" class="btn btn-primary"><i class="fas fa-search"></i></button></div></div></div></div></form></div></div><!-- جدول العملاء --><div class="card"><div class="card-header"><h5 class="mb-0"><i class="fas fa-list me-2"></i>
            قائمة العملاء ({{ clients.total }} عميل)
        </h5></div><div class="card-body">
        {% if clients.items %}
        <div class="table-responsive"><table class="table table-hover"><thead><tr><th>رمز العميل</th><th>الاسم</th><th>النوع</th><th>البريد الإلكتروني</th><th>الهاتف</th><th>عدد القضايا</th><th>إجمالي القيمة</th><th>الحالة</th><th>تاريخ الإنشاء</th><th>الإجراءات</th></tr></thead><tbody>
                    {% for client in clients.items %}
                    <tr><td><strong>{{ client.client_code }}</strong></td><td><a href="{{ url_for('clients.view', id=client.id }}" class="text-decoration-none">
                                {{ client.full_name }}
                            </a></td><td>
                            {% if client.client_type == 'individual' %}
                                <span class="badge bg-info">فرد</span>
                            {% elif client.client_type == 'company' %}
                                <span class="badge bg-warning">شركة</span>
                            {% elif client.client_type == 'organization' %}
                                <span class="badge bg-secondary">مؤسسة</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ client.client_type or 'غير محدد' }}</span>
                            {% endif %}
                        </td><td>{{ client.email or '-' }}</td><td>{{ client.phone or '-' }}</td><td><span class="badge bg-primary">{{ client.cases_count or 0 }}</span></td><td><strong>{{ "{:,.0f}".format(client.total_value or 0 ) }}</strong><small class="text-muted">ريال</small></td><td>
                            {% if client.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td><td>{{ client.created_at.created_at else '-'|strftime("%Y-%m-%d") if client }}</td><td><div class="btn-group btn-group-sm"><a href="{{ url_for('clients.view', id=client.id }}" 
                                   class="btn btn-outline-primary" title="عرض"><i class="fas fa-eye"></i></a><a href="{{ url_for('clients.edit', id=client.id }}" 
                                   class="btn btn-outline-warning" title="تعديل"><i class="fas fa-edit"></i></a>
                                {% if client.is_active %}
                                <button onclick="deactivateClient('{{ client.id }}', '{{ client.full_name }}')"
                                        class="btn btn-outline-danger" title="إلغاء تفعيل"><i class="fas fa-user-times"></i></button>
                                {% else %}
                                <button onclick="activateClient('{{ client.id }}', '{{ client.full_name }}')"
                                        class="btn btn-outline-success" title="تفعيل"><i class="fas fa-user-check"></i></button>
                                {% endif %}
                            </div></td></tr>
                    {% endfor %}
                </tbody></table></div><!-- Pagination -->
        {% if clients.pages > 1 %}
        <nav aria-label="صفحات العملاء"><ul class="pagination justify-content-center">
                {% if clients.has_prev %}
                <li class="page-item"><a class="page-link" href="{{ url_for('clients.index', page=clients.prev_num, search=search, client_type=client_type, status=status_filter, sort=sort_by, order=sort_order }}">السابق</a></li>
                {% endif %}
                
                {% for page_num in clients.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != clients.page %}
                        <li class="page-item"><a class="page-link" href="{{ url_for('clients.index', page=page_num, search=search, client_type=client_type, status=status_filter, sort=sort_by, order=sort_order }}">{{ page_num }}</a></li>
                        {% else %}
                        <li class="page-item active"><span class="page-link">{{ page_num }}</span></li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled"><span class="page-link">...</span></li>
                    {% endif %}
                {% endfor %}
                
                {% if clients.has_next %}
                <li class="page-item"><a class="page-link" href="{{ url_for('clients.index', page=clients.next_num, search=search, client_type=client_type, status=status_filter, sort=sort_by, order=sort_order }}">التالي</a></li>
                {% endif %}
            </ul></nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5"><i class="fas fa-users fa-3x text-muted mb-3"></i><h5 class="text-muted">لا توجد عملاء</h5><p class="text-muted">لم يتم العثور على أي عملاء تطابق معايير البحث</p><a href="{{ url_for('clients.add' }}" class="btn btn-primary"><i class="fas fa-plus me-1"></i>
                إضافة عميل جديد
            </a></div>
        {% endif %}
    </div></div><script>
function deactivateClient(clientId, clientName) {
    if (confirm(`هل أنت متأكد من إلغاء تفعيل العميل "${clientName}"؟`) {
        fetch(`/clients/${clientId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json()
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
    }
}

function activateClient(clientId, clientName) {
    if (confirm(`هل أنت متأكد من تفعيل العميل "${clientName}"؟`) {
        fetch(`/clients/${clientId}/activate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json()
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
    }
}

function exportClients() {
    window.location.href = '/clients/export';
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid') || document.body;
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
