{% extends "base.html" %}

{% block title %}تقرير الإجازات والاستئذان{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-chart-bar"></i>
            {{ report_title }}
          </h3>
          <div class="card-tools">
            <button type="button" class="btn btn-primary btn-sm" onclick="printReport()">
              <i class="fas fa-print"></i>
              طباعة التقرير
            </button>
            <a href="{{ url_for('leaves.index' }}" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a>
          </div>
        </div>

        <div class="card-body">
          <!-- فلاتر التقرير -->
          <div class="card card-outline card-primary collapsed-card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-filter"></i>
                فلاتر التقرير
              </h3>
              <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
            <div class="card-body">
              <form method="GET" action="{{ url_for('leaves_report' }}">
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="employee_id">الموظف</label>
                      <select class="form-control" id="employee_id" name="employee_id">
                        <option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" 
                                {% if employee_id == employee.id %}selected{% endif %}>
                          {{ employee.full_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="department_id">القسم</label>
                      <select class="form-control" id="department_id" name="department_id">
                        <option value="">جميع الأقسام</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" 
                                {% if department_id == department.id %}selected{% endif %}>
                          {{ department.name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="leave_type">نوع الإجازة</label>
                      <select class="form-control" id="leave_type" name="leave_type">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in leave_types %}
                        <option value="{{ type_code }}" {% if leave_type == type_code %}selected{% endif %}>
                          {{ type_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="date_from">من تاريخ</label>
                      <input type="date" class="form-control" id="date_from" name="date_from" 
                             value="{{ date_from }}">
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="date_to">إلى تاريخ</label>
                      <input type="date" class="form-control" id="date_to" name="date_to" 
                             value="{{ date_to }}">
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-search"></i>
                      تحديث التقرير
                    </button>
                    <a href="{{ url_for('leaves_report' }}" class="btn btn-secondary">
                      <i class="fas fa-undo"></i>
                      إعادة تعيين
                    </a>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- إحصائيات التقرير -->
          <div class="row mb-4">
            <div class="col-md-2">
              <div class="info-box bg-info">
                <span class="info-box-icon"><i class="fas fa-list"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">إجمالي الطلبات</span>
                  <span class="info-box-number">{{ stats.total_requests }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-success">
                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">موافق عليها</span>
                  <span class="info-box-number">{{ stats.approved_requests }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-warning">
                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">في الانتظار</span>
                  <span class="info-box-number">{{ stats.pending_requests }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-danger">
                <span class="info-box-icon"><i class="fas fa-times"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">مرفوضة</span>
                  <span class="info-box-number">{{ stats.rejected_requests }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-primary">
                <span class="info-box-icon"><i class="fas fa-calendar-day"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">إجمالي الأيام</span>
                  <span class="info-box-number">{{ stats.total_approved_days }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-secondary">
                <span class="info-box-icon"><i class="fas fa-calculator"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">متوسط الأيام</span>
                  <span class="info-box-number">{{ "%.1f".format(stats.average_days_per_request }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- إحصائيات حسب النوع -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="card card-outline card-success">
                <div class="card-header">
                  <h3 class="card-title">إحصائيات حسب نوع الإجازة</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                    {% for type_code, type_data in stats.type_stats %}
                    <div class="col-md-3 mb-2">
                      <div class="info-box">
                        <span class="info-box-icon bg-gradient-primary">
                          <i class="fas fa-calendar-alt"></i>
                        </span>
                        <div class="info-box-content">
                          <span class="info-box-text">{{ type_data.name }}</span>
                          <span class="info-box-number">
                            {{ type_data.count }} طلب
                            <small>({{ type_data.days }} يوم)</small>
                          </span>
                        </div>
                      </div>
                    </div>
                    {% endfor %}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- جدول التقرير -->
          <div id="reportContent">
            <div class="report-header text-center mb-4" style="display: none;">
              <h2>{{ report_title }}</h2>
              <p><strong>الفترة:</strong> {{ report_period }}</p>
              <p><strong>تاريخ التقرير:</strong> {{ report_date }}</p>
              <hr>
            </div>

            <div class="table-responsive">
              <table class="table table-bordered table-striped">
                <thead>
                  <tr>
                    <th>الموظف</th>
                    <th>القسم</th>
                    <th>نوع الإجازة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>الحالة</th>
                    <th>تاريخ الطلب</th>
                    <th class="no-print">السبب</th>
                  </tr>
                </thead>
                <tbody>
                  {% for request in leave_requests %}
                  <tr>
                    <td>
                      <strong>{{ request.employee.full_name }}</strong><br>
                      <small class="text-muted">{{ request.employee.employee_number }}</small>
                    </td>
                    <td>
                      {% if request.employee.department %}
                        {{ request.employee.department.name }}
                      {% else %}
                        <span class="text-muted">-</span>
                      {% endif %}
                    </td>
                    <td>
                      {% for type_code, type_name in leave_types %}
                        {% if request.leave_type == type_code %}
                          {{ type_name }}
                          {% if not request.is_paid %}
                            <br><small class="text-danger">(بدون راتب)</small>
                          {% endif %}
                          
                        {% endif %}
                      {% endfor %}
                    </td>
                    <td>{{ request.start_date.strftime("%Y-%m-%d") if request.start_date else "-" }}</td>
                    <td>{{ request.end_date.strftime("%Y-%m-%d") if request.end_date else "-" }}</td>
                    <td><strong>{{ request.days_count }}</strong></td>
                    <td>
                      {% if request.status == 'pending' %}
                        في الانتظار
                      {% elif request.status == 'approved' %}
                        موافق عليها
                      {% elif request.status == 'rejected' %}
                        مرفوضة
                      {% endif %}
                    </td>
                    <td>{{ request.created_at.strftime("%Y-%m-%d") if request.created_at else "-" }}</td>
                    <td class="no-print">
                      <small>{{ request.reason|truncate(100 }}{% if request.reason|length > 100 %}...{% endif %}</small>
                    </td>
                  </tr>
                  {% else %}
                  <tr>
                    <td colspan="9" class="text-center text-muted">
                      <i class="fas fa-inbox fa-2x mb-2"></i><br>
                      لا توجد طلبات إجازة في الفترة المحددة
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>

            <!-- ملخص الإحصائيات للطباعة -->
            <div class="print-only mt-4">
              <h4>ملخص الإحصائيات:</h4>
              <div class="row">
                <div class="col-md-6">
                  <ul class="list-unstyled">
                    <li><strong>إجمالي الطلبات:</strong> {{ stats.total_requests }}</li>
                    <li><strong>الطلبات الموافق عليها:</strong> {{ stats.approved_requests }}</li>
                    <li><strong>الطلبات المرفوضة:</strong> {{ stats.rejected_requests }}</li>
                  </ul>
                </div>
                <div class="col-md-6">
                  <ul class="list-unstyled">
                    <li><strong>الطلبات في الانتظار:</strong> {{ stats.pending_requests }}</li>
                    <li><strong>إجمالي أيام الإجازة:</strong> {{ stats.total_approved_days }} يوم</li>
                    <li><strong>متوسط الأيام لكل طلب:</strong> {{ "%.1f".format(stats.average_days_per_request }} يوم</li>
                  </ul>
                </div>
              </div>

              <h5>إحصائيات حسب نوع الإجازة:</h5>
              <ul class="list-unstyled">
                {% for type_code, type_data in stats.type_stats %}
                <li><strong>{{ type_data.name }}:</strong> {{ type_data.count }} طلب ({{ type_data.days }} يوم)</li>
                {% endfor %}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .report-header {
    display: block !important;
  }
  
  .card-header,
  .card-tools,
  .btn,
  .sidebar,
  .main-header,
  .main-footer {
    display: none !important;
  }
  
  .content-wrapper {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .card {
    border: none !important;
    box-shadow: none !important;
  }
  
  .table {
    font-size: 12px;
  }
  
  .info-box {
    border: 1px solid #000 !important;
    background-color: transparent !important;
  }
}

.print-only {
  display: none;
}
</style>

<script>
function printReport() {
  // إظهار عناصر الطباعة
  document.querySelector('.report-header').style.display = 'block';
  
  // طباعة الصفحة
  window.print();
  
  // إخفاء عناصر الطباعة بعد الطباعة
  setTimeout() => {
    document.querySelector('.report-header').style.display = 'none';
  }, 1000);
}

// تحسين عرض التقرير
document.addEventListener('DOMContentLoaded', function() {
  // إضافة أرقام الصفوف
  const rows = document.querySelectorAll('tbody tr');
  rows.forEach(row, index) => {
    if (!row.querySelector('td[colspan]') {
      const firstCell = row.querySelector('td');
      if (firstCell) {
        firstCell.innerHTML = `<small class="text-muted">${index + 1}.</small> ${firstCell.innerHTML}`;
      }
    }
  });
});
</script>
{% endblock %}
