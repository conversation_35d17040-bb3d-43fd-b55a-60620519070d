# تشغيل نظام الشؤون القانونية
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    تشغيل نظام الشؤون القانونية" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔍 فحص التبعيات المطلوبة..." -ForegroundColor Blue

try {
    python -c "import flask, flask_sqlalchemy, flask_login" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ جميع التبعيات متوفرة" -ForegroundColor Green
    } else {
        throw "Dependencies missing"
    }
} catch {
    Write-Host "❌ بعض التبعيات مفقودة، جاري تثبيتها..." -ForegroundColor Red
    pip install -r requirements.txt
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم تثبيت التبعيات بنجاح" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل في تثبيت التبعيات" -ForegroundColor Red
        Write-Host "يرجى تشغيل: pip install -r requirements.txt" -ForegroundColor Yellow
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
}

Write-Host ""
Write-Host "🚀 بدء تشغيل التطبيق..." -ForegroundColor Blue
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   معلومات الوصول للنظام:" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🌐 الرابط المحلي: http://localhost:5000" -ForegroundColor White
Write-Host "👤 اسم المستخدم: admin" -ForegroundColor White
Write-Host "🔑 كلمة المرور: admin123" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📝 ملاحظة: اتركي هذه النافذة مفتوحة أثناء استخدام النظام" -ForegroundColor Yellow
Write-Host "🔄 لإيقاف النظام: اضغط Ctrl+C" -ForegroundColor Yellow
Write-Host ""

python app.py
