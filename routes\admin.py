from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import uuid
import os
import shutil
import json
from permissions_manager import (
    permission_required, any_permission_required, admin_required,
    get_all_permissions, get_permissions_by_category, initialize_permissions
)

bp = Blueprint('admin', __name__, url_prefix='/admin')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from flask import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        return {}

def admin_required_old(f):
    """ديكوريتر للتحقق من صلاحيات المدير (النسخة القديمة)"""
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('يجب تسجيل الدخول أولاً', 'error')
            return redirect(url_for('login'))

        # التحقق من الدور القديم أو الصلاحية الجديدة
        if (current_user.role == 'admin' or
            (hasattr(current_user, 'has_permission') and current_user.has_permission('view_admin_panel'))):
            return f(*args, **kwargs)

        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    decorated_function.__name__ = f.__name__
    return decorated_function

@bp.route('/test')
def test():
    """صفحة اختبار"""
    return "صفحة الاختبار تعمل بشكل صحيح!"

@bp.route('/')
@login_required
@admin_required_old
def index():
    """لوحة تحكم المدير"""
    try:
        # إحصائيات النظام
        stats = {
            'total_users': 5,
            'total_cases': 25,
            'total_clients': 18,
            'total_lawyers': 8,
            'active_sessions': 3,
            'system_health': 'ممتاز'
        }
        
        # أحدث الأنشطة
        recent_activities = [
            {
                'user': 'أحمد محمد',
                'action': 'إضافة قضية جديدة',
                'time': 'منذ 5 دقائق',
                'type': 'success'
            },
            {
                'user': 'سارة أحمد',
                'action': 'تحديث بيانات عميل',
                'time': 'منذ 15 دقيقة',
                'type': 'info'
            },
            {
                'user': 'محمد علي',
                'action': 'إضافة جلسة محكمة',
                'time': 'منذ 30 دقيقة',
                'type': 'warning'
            }
        ]
        
        return render_template('admin/index.html', 
                             stats=stats, 
                             recent_activities=recent_activities)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/users')
@login_required
@admin_required
def users():
    """إدارة المستخدمين"""
    try:
        # بيانات تجريبية للمستخدمين
        fake_users = [
            {
                'id': 1,
                'username': 'admin',
                'full_name': 'مدير النظام',
                'email': '<EMAIL>',
                'role': 'admin',
                'is_active': True,
                'last_login': datetime.now(),
                'created_at': datetime.now()
            },
            {
                'id': 2,
                'username': 'lawyer1',
                'full_name': 'د. سارة أحمد',
                'email': '<EMAIL>',
                'role': 'lawyer',
                'is_active': True,
                'last_login': datetime.now(),
                'created_at': datetime.now()
            },
            {
                'id': 3,
                'username': 'secretary1',
                'full_name': 'أحمد محمد',
                'email': '<EMAIL>',
                'role': 'secretary',
                'is_active': True,
                'last_login': datetime.now(),
                'created_at': datetime.now()
            }
        ]
        
        return render_template('admin/users.html', users=fake_users)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('admin.index'))

@bp.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    try:
        if request.method == 'POST':
            flash('تم إضافة المستخدم بنجاح (وظيفة تجريبية)', 'success')
            return redirect(url_for('admin.users'))
        
        return render_template('admin/add_user.html')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('admin.users'))

@bp.route('/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def settings():
    """إعدادات النظام"""
    try:
        if request.method == 'POST':
            # قراءة الإعدادات الحالية أولاً
            settings_file = 'settings.json'
            try:
                import json
                with open(settings_file, 'r', encoding='utf-8') as f:
                    existing_settings = json.load(f)
            except FileNotFoundError:
                existing_settings = {}

            # حفظ الإعدادات مع الاحتفاظ بالإعدادات الموجودة
            settings_data = existing_settings.copy()
            settings_data.update({
                'system_name': request.form.get('system_name', ''),
                'company_name': request.form.get('company_name', ''),
                'company_address': request.form.get('company_address', ''),
                'company_phone': request.form.get('company_phone', ''),
                'company_email': request.form.get('company_email', ''),
                'timezone': request.form.get('timezone', 'Asia/Riyadh'),
                'language': request.form.get('language', 'ar'),
                'currency': request.form.get('currency', 'SAR'),
                'email_notifications': 'email_notifications' in request.form,
                'sms_notifications': 'sms_notifications' in request.form,
                'backup_enabled': 'auto_backup' in request.form,
                'maintenance_mode': 'maintenance_mode' in request.form,
                # إعدادات المظهر
                'primary_color': request.form.get('primary_color', '#007bff'),
                'secondary_color': request.form.get('secondary_color', '#6c757d'),
                'theme_mode': request.form.get('theme_mode', 'light'),
                'sidebar_style': request.form.get('sidebar_style', 'default')
            })

            # حفظ الإعدادات في ملف JSON
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, ensure_ascii=False, indent=2)

            print(f"✅ تم حفظ الإعدادات: {settings_data}")
            flash('تم حفظ الإعدادات بنجاح', 'success')
            return redirect(url_for('admin.settings'))

        # قراءة الإعدادات المحفوظة
        settings_file = 'settings.json'
        try:
            import json
            with open(settings_file, 'r', encoding='utf-8') as f:
                system_settings = json.load(f)
        except FileNotFoundError:
            # إعدادات افتراضية
            system_settings = {
                'system_name': 'نظام الشؤون القانونية',
                'company_name': 'مكتب المحاماة المتقدم',
                'company_address': 'الرياض، المملكة العربية السعودية',
                'company_phone': '+966112345678',
                'company_email': '<EMAIL>',
                'timezone': 'Asia/Riyadh',
                'language': 'ar',
                'currency': 'SAR',
                'date_format': 'dd/mm/yyyy',
                'backup_enabled': True,
                'email_notifications': True,
                'sms_notifications': False,
                'maintenance_mode': False,
                'current_logo': '/static/images/default-logo.svg'
            }

        # إضافة الشعار الحالي
        if 'current_logo' not in system_settings:
            system_settings['current_logo'] = '/static/images/default-logo.svg'

        # إضافة إعدادات المظهر الافتراضية إذا لم تكن موجودة
        if 'primary_color' not in system_settings:
            system_settings['primary_color'] = '#007bff'
        if 'secondary_color' not in system_settings:
            system_settings['secondary_color'] = '#6c757d'
        if 'theme_mode' not in system_settings:
            system_settings['theme_mode'] = 'light'
        if 'sidebar_style' not in system_settings:
            system_settings['sidebar_style'] = 'default'

        print(f"📋 إعدادات النظام المرسلة: {system_settings}")

        return render_template('admin/settings.html', settings=system_settings)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('admin.index'))

@bp.route('/profile')
@login_required
def profile():
    """الملف الشخصي للمستخدم"""
    try:
        return render_template('admin/profile.html', user=current_user)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """تعديل الملف الشخصي"""
    try:
        if request.method == 'POST':
            flash('تم تحديث الملف الشخصي بنجاح (وظيفة تجريبية)', 'success')
            return redirect(url_for('admin.profile'))
        
        return render_template('admin/edit_profile.html', user=current_user)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('admin.profile'))

@bp.route('/backup')
@login_required
def backup():
    """إدارة النسخ الاحتياطية"""
    try:
        print("بدء تحميل صفحة النسخ الاحتياطية...")

        # إنشاء إحصائيات افتراضية أولاً
        default_stats = {
            'total_count': 0,
            'total_size': '0 Bytes',
            'total_size_bytes': 0,
            'auto_count': 0,
            'manual_count': 0,
            'latest_backup': None,
            'average_size': '0 Bytes',
            'average_size_bytes': 0
        }

        backups = []
        backup_dir = 'backups'

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        os.makedirs(backup_dir, exist_ok=True)
        print(f"مجلد النسخ الاحتياطية: {backup_dir}")

        # متغيرات لحساب الإحصائيات
        total_size_bytes = 0
        auto_backups_count = 0
        manual_backups_count = 0

        # قراءة النسخ الاحتياطية الموجودة
        if os.path.exists(backup_dir):
            for filename in os.listdir(backup_dir):
                if filename.endswith('.db'):
                    file_path = os.path.join(backup_dir, filename)
                    file_stat = os.stat(file_path)
                    file_size_bytes = file_stat.st_size
                    file_size_mb = round(file_size_bytes / (1024 * 1024), 2)

                    # إضافة إلى إجمالي الحجم
                    total_size_bytes += file_size_bytes

                    # تحديد نوع النسخة الاحتياطية
                    backup_type = 'يدوي'
                    if 'auto' in filename.lower():
                        backup_type = 'تلقائي'
                        auto_backups_count += 1
                    elif 'before_restore' in filename.lower():
                        backup_type = 'قبل الاستعادة'
                        manual_backups_count += 1
                    else:
                        manual_backups_count += 1

                    backups.append({
                        'id': len(backups) + 1,
                        'filename': filename,
                        'size': f'{file_size_mb} MB',
                        'size_bytes': file_size_bytes,
                        'created_at': datetime.fromtimestamp(file_stat.st_ctime),
                        'type': backup_type
                    })

        # ترتيب النسخ حسب تاريخ الإنشاء (الأحدث أولاً)
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        print(f"تم العثور على {len(backups)} نسخة احتياطية")

        # حساب إجمالي الحجم بالوحدة المناسبة
        def format_file_size(size_bytes):
            if size_bytes >= 1024 * 1024 * 1024:  # GB
                return f"{round(size_bytes / (1024 * 1024 * 1024), 2)} GB"
            elif size_bytes >= 1024 * 1024:  # MB
                return f"{round(size_bytes / (1024 * 1024), 2)} MB"
            elif size_bytes >= 1024:  # KB
                return f"{round(size_bytes / 1024, 2)} KB"
            else:  # Bytes
                return f"{size_bytes} Bytes"

        # حساب متوسط حجم النسخ
        average_size_bytes = total_size_bytes / len(backups) if backups else 0

        # تحديث الإحصائيات بالقيم الفعلية
        default_stats.update({
            'total_count': len(backups),
            'total_size': format_file_size(total_size_bytes),
            'total_size_bytes': total_size_bytes,
            'auto_count': auto_backups_count,
            'manual_count': manual_backups_count,
            'latest_backup': backups[0] if backups else None,
            'average_size': format_file_size(average_size_bytes),
            'average_size_bytes': average_size_bytes
        })

        backup_stats = default_stats

        print(f"إحصائيات النسخ الاحتياطية: {backup_stats}")
        return render_template('admin/backup.html', backups=backups, backup_stats=backup_stats)
    except Exception as e:
        print(f"خطأ في صفحة النسخ الاحتياطية: {str(e)}")
        # إنشاء إحصائيات افتراضية في حالة الخطأ
        default_stats = {
            'total_count': 0,
            'total_size': '0 Bytes',
            'total_size_bytes': 0,
            'auto_count': 0,
            'manual_count': 0,
            'latest_backup': None,
            'average_size': '0 Bytes',
            'average_size_bytes': 0
        }
        flash(f'حدث خطأ: {str(e)}', 'error')
        return render_template('admin/backup.html', backups=[], backup_stats=default_stats)

@bp.route('/logs')
@login_required
@admin_required
def logs():
    """سجلات النظام"""
    try:
        # سجلات تجريبية
        logs = [
            {
                'id': 1,
                'level': 'INFO',
                'message': 'تم تسجيل دخول المستخدم admin',
                'timestamp': datetime.now(),
                'user': 'admin'
            },
            {
                'id': 2,
                'level': 'WARNING',
                'message': 'محاولة وصول غير مصرح بها',
                'timestamp': datetime.now(),
                'user': 'unknown'
            },
            {
                'id': 3,
                'level': 'ERROR',
                'message': 'خطأ في الاتصال بقاعدة البيانات',
                'timestamp': datetime.now(),
                'user': 'system'
            }
        ]
        
        return render_template('admin/logs.html', logs=logs)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('admin.index'))

@bp.route('/backup/create', methods=['POST'])
@login_required
@admin_required
def create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        import shutil
        from datetime import datetime

        print("🔄 بدء إنشاء النسخة الاحتياطية...")

        # إنشاء اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'backup_{timestamp}.db'
        backup_dir = 'backups'
        backup_path = os.path.join(backup_dir, backup_filename)

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        os.makedirs(backup_dir, exist_ok=True)
        print(f"📁 تم إنشاء مجلد النسخ الاحتياطية: {backup_dir}")

        # البحث عن قاعدة البيانات
        db_files = ['legal_system.db', 'instance/legal_system.db', 'database.db']
        source_db = None

        for db_file in db_files:
            if os.path.exists(db_file):
                source_db = db_file
                break

        if source_db:
            # نسخ قاعدة البيانات
            shutil.copy2(source_db, backup_path)
            print(f"✅ تم نسخ قاعدة البيانات من {source_db} إلى {backup_path}")

            # حساب حجم الملف
            file_size = os.path.getsize(backup_path)
            size_mb = round(file_size / (1024 * 1024), 2) if file_size > 1024*1024 else round(file_size / 1024, 2)
            size_unit = 'MB' if file_size > 1024*1024 else 'KB'

            print(f"📊 حجم النسخة الاحتياطية: {size_mb} {size_unit}")

            return jsonify({
                'success': True,
                'message': f'تم إنشاء النسخة الاحتياطية بنجاح ({size_mb} {size_unit})',
                'filename': backup_filename,
                'size': f'{size_mb} {size_unit}',
                'path': backup_path
            })
        else:
            print("❌ لم يتم العثور على قاعدة البيانات")
            # إنشاء قاعدة بيانات فارغة للاختبار
            with open(backup_path, 'w') as f:
                f.write('# نسخة احتياطية تجريبية\n')

            return jsonify({
                'success': True,
                'message': 'تم إنشاء نسخة احتياطية تجريبية',
                'filename': backup_filename,
                'size': '1 KB',
                'path': backup_path
            })

    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}'
        }), 500

@bp.route('/backup/restore', methods=['POST'])
@login_required
@admin_required
def restore_backup():
    """استعادة نسخة احتياطية"""
    try:
        data = request.get_json()
        backup_filename = data.get('filename')

        if not backup_filename:
            return jsonify({
                'success': False,
                'message': 'اسم الملف مطلوب'
            }), 400

        backup_path = os.path.join('backups', backup_filename)

        if not os.path.exists(backup_path):
            return jsonify({
                'success': False,
                'message': 'الملف المطلوب غير موجود'
            }), 404

        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
        current_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        current_backup = f'backup_before_restore_{current_timestamp}.db'
        current_backup_path = os.path.join('backups', current_backup)

        if os.path.exists('legal_system.db'):
            shutil.copy2('legal_system.db', current_backup_path)

        # استعادة النسخة الاحتياطية
        shutil.copy2(backup_path, 'legal_system.db')

        return jsonify({
            'success': True,
            'message': 'تم استعادة النسخة الاحتياطية بنجاح',
            'current_backup': current_backup
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}'
        }), 500

@bp.route('/backup/download/<filename>')
@login_required
@admin_required
def download_backup(filename):
    """تحميل نسخة احتياطية"""
    try:
        backup_path = os.path.join('backups', filename)

        if not os.path.exists(backup_path):
            flash('الملف المطلوب غير موجود', 'error')
            return redirect(url_for('admin.backup'))

        from flask import send_file
        return send_file(backup_path, as_attachment=True, download_name=filename)

    except Exception as e:
        flash(f'حدث خطأ أثناء تحميل الملف: {str(e)}', 'error')
        return redirect(url_for('admin.backup'))

@bp.route('/backup/delete', methods=['POST'])
@login_required
@admin_required
def delete_backup():
    """حذف نسخة احتياطية"""
    try:
        data = request.get_json()
        backup_filename = data.get('filename')

        if not backup_filename:
            return jsonify({
                'success': False,
                'message': 'اسم الملف مطلوب'
            }), 400

        backup_path = os.path.join('backups', backup_filename)

        if os.path.exists(backup_path):
            os.remove(backup_path)
            return jsonify({
                'success': True,
                'message': 'تم حذف النسخة الاحتياطية بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'الملف غير موجود'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}'
        }), 500

@bp.route('/upload-logo', methods=['POST'])
@login_required
@admin_required
def upload_logo():
    """رفع شعار الشركة"""
    try:
        print("🔄 بدء رفع الشعار...")

        if 'logo' not in request.files:
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار ملف'
            }), 400

        file = request.files['logo']

        if file.filename == '':
            return jsonify({
                'success': False,
                'message': 'لم يتم اختيار ملف'
            }), 400

        print(f"📁 اسم الملف: {file.filename}")

        # التحقق من نوع الملف
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'svg'}
        if not ('.' in file.filename and
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({
                'success': False,
                'message': 'نوع الملف غير مدعوم. يرجى استخدام PNG, JPG, JPEG, GIF, أو SVG'
            }), 400

        # التحقق من حجم الملف (5MB كحد أقصى)
        file.seek(0, 2)  # الانتقال لنهاية الملف
        file_size = file.tell()
        file.seek(0)  # العودة لبداية الملف

        if file_size > 5 * 1024 * 1024:
            return jsonify({
                'success': False,
                'message': 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت'
            }), 400

        # إنشاء مجلد الشعارات
        logo_dir = os.path.join('static', 'images', 'logos')
        os.makedirs(logo_dir, exist_ok=True)
        print(f"📁 تم إنشاء مجلد الشعارات: {logo_dir}")

        # حفظ الملف
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"logo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}"
        file_path = os.path.join(logo_dir, filename)
        file.save(file_path)
        print(f"✅ تم حفظ الشعار: {file_path}")

        # حفظ مسار الشعار في الإعدادات
        logo_url = f'/static/images/logos/{filename}'

        # تحديث ملف الإعدادات
        settings_file = 'settings.json'
        try:
            import json
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
        except FileNotFoundError:
            settings = {}

        settings['current_logo'] = logo_url

        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        print(f"✅ تم تحديث الإعدادات: {logo_url}")

        return jsonify({
            'success': True,
            'message': 'تم رفع الشعار وحفظه بنجاح',
            'logo_url': logo_url,
            'filename': filename
        })

    except Exception as e:
        print(f"❌ خطأ في رفع الشعار: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء رفع الشعار: {str(e)}'
        }), 500

@bp.route('/save-appearance-settings', methods=['POST'])
@login_required
@admin_required
def save_appearance_settings():
    """حفظ إعدادات المظهر عبر AJAX"""
    try:
        data = request.get_json()
        print(f"🔄 استلام بيانات المظهر: {data}")

        # قراءة الإعدادات الحالية
        settings_file = 'settings.json'
        try:
            import json
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
        except FileNotFoundError:
            settings = {}

        # تحديث إعدادات المظهر
        settings.update({
            'primary_color': data.get('primary_color', '#007bff'),
            'secondary_color': data.get('secondary_color', '#6c757d'),
            'theme_mode': data.get('theme_mode', 'light'),
            'sidebar_style': data.get('sidebar_style', 'default')
        })

        # حفظ الإعدادات
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        print(f"✅ تم حفظ إعدادات المظهر: {settings}")

        return jsonify({
            'success': True,
            'message': 'تم حفظ إعدادات المظهر بنجاح'
        })

    except Exception as e:
        print(f"❌ خطأ في حفظ إعدادات المظهر: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء حفظ إعدادات المظهر: {str(e)}'
        }), 500

@bp.route('/save-logo-settings', methods=['POST'])
@login_required
@admin_required
def save_logo_settings():
    """حفظ إعدادات الشعار"""
    try:
        data = request.get_json()

        # قراءة الإعدادات الحالية
        settings_file = 'settings.json'
        try:
            import json
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
        except FileNotFoundError:
            settings = {}

        # تحديث إعدادات الشعار
        settings.update({
            'logo_height': int(data.get('logo_height', 40)),
            'logo_padding': int(data.get('logo_padding', 5)),
            'logo_border_width': int(data.get('logo_border_width', 2)),
            'logo_shadow': int(data.get('logo_shadow', 8)),
            'logo_circular': bool(data.get('logo_circular', True))
        })

        # حفظ الإعدادات
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=2)

        return jsonify({
            'success': True,
            'message': 'تم حفظ إعدادات الشعار بنجاح'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء حفظ إعدادات الشعار: {str(e)}'
        }), 500

@bp.route('/api/system-stats')
@login_required
@admin_required
def api_system_stats():
    """API لإحصائيات النظام"""
    try:
        stats = {
            'cpu_usage': 45,
            'memory_usage': 62,
            'disk_usage': 38,
            'active_users': 3,
            'database_size': '15.2 MB'
        }
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/backup/stats', methods=['GET'])
@login_required
@admin_required
def backup_stats():
    """API لإحصائيات النسخ الاحتياطية"""
    try:
        backup_dir = 'backups'
        total_size_bytes = 0
        auto_backups_count = 0
        manual_backups_count = 0
        total_count = 0

        # قراءة النسخ الاحتياطية الموجودة
        if os.path.exists(backup_dir):
            for filename in os.listdir(backup_dir):
                if filename.endswith('.db'):
                    file_path = os.path.join(backup_dir, filename)
                    file_stat = os.stat(file_path)
                    file_size_bytes = file_stat.st_size

                    # إضافة إلى إجمالي الحجم
                    total_size_bytes += file_size_bytes
                    total_count += 1

                    # تحديد نوع النسخة الاحتياطية
                    if 'auto' in filename.lower():
                        auto_backups_count += 1
                    elif 'before_restore' in filename.lower():
                        manual_backups_count += 1
                    else:
                        manual_backups_count += 1

        # حساب إجمالي الحجم بالوحدة المناسبة
        def format_file_size(size_bytes):
            if size_bytes >= 1024 * 1024 * 1024:  # GB
                return f"{round(size_bytes / (1024 * 1024 * 1024), 2)} GB"
            elif size_bytes >= 1024 * 1024:  # MB
                return f"{round(size_bytes / (1024 * 1024), 2)} MB"
            elif size_bytes >= 1024:  # KB
                return f"{round(size_bytes / 1024, 2)} KB"
            else:  # Bytes
                return f"{size_bytes} Bytes"

        # حساب متوسط حجم النسخ
        average_size_bytes = total_size_bytes / total_count if total_count > 0 else 0

        stats = {
            'total_count': total_count,
            'total_size': format_file_size(total_size_bytes),
            'total_size_bytes': total_size_bytes,
            'auto_count': auto_backups_count,
            'manual_count': manual_backups_count,
            'average_size': format_file_size(average_size_bytes),
            'average_size_bytes': average_size_bytes
        }

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

# ==================== إدارة الصلاحيات ====================

@bp.route('/permissions')
@login_required
@permission_required('manage_permissions')
def permissions():
    """صفحة إدارة الصلاحيات والأدوار"""
    try:
        models = get_models()
        Role = models.get('Role')
        Permission = models.get('Permission')
        User = models.get('User')

        if not all([Role, Permission, User]):
            flash('خطأ في تحميل النماذج', 'error')
            return redirect(url_for('admin.index'))

        # جلب جميع الأدوار
        roles = Role.query.filter_by(is_active=True).all()

        # جلب جميع المستخدمين
        users = User.query.filter_by(is_active=True).all()

        # جلب الصلاحيات مجمعة حسب الفئة
        permissions_by_category = get_permissions_by_category()

        # جلب جميع الصلاحيات
        all_permissions = get_all_permissions()

        return render_template('admin/permissions.html',
                             roles=roles,
                             users=users,
                             permissions_by_category=permissions_by_category,
                             all_permissions=all_permissions)
    except Exception as e:
        print(f"خطأ في صفحة الصلاحيات: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('admin.index'))

@bp.route('/permissions/roles', methods=['POST'])
@login_required
@permission_required('manage_roles')
def create_role():
    """إنشاء دور جديد"""
    try:
        models = get_models()
        Role = models.get('Role')
        Permission = models.get('Permission')
        db = current_app.extensions['sqlalchemy'].db

        if not all([Role, Permission]):
            return jsonify({
                'success': False,
                'message': 'خطأ في تحميل النماذج'
            }), 500

        data = request.get_json()
        name = data.get('name')
        display_name = data.get('display_name')
        description = data.get('description', '')
        permission_names = data.get('permissions', [])

        if not name or not display_name:
            return jsonify({
                'success': False,
                'message': 'اسم الدور مطلوب'
            }), 400

        # التحقق من عدم وجود دور بنفس الاسم
        existing_role = Role.query.filter_by(name=name).first()
        if existing_role:
            return jsonify({
                'success': False,
                'message': 'يوجد دور بهذا الاسم بالفعل'
            }), 400

        # إنشاء الدور الجديد
        new_role = Role(
            name=name,
            display_name=display_name,
            description=description,
            is_default=False
        )

        db.session.add(new_role)
        db.flush()  # للحصول على ID

        # إضافة الصلاحيات للدور
        for perm_name in permission_names:
            permission = Permission.query.filter_by(name=perm_name).first()
            if permission:
                new_role.permissions.append(permission)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء الدور بنجاح'
        })

    except Exception as e:
        print(f"خطأ في إنشاء الدور: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@bp.route('/permissions/users/<int:user_id>', methods=['GET'])
@login_required
@permission_required('manage_user_permissions')
def get_user_permissions(user_id):
    """جلب صلاحيات مستخدم معين"""
    try:
        models = get_models()
        User = models.get('User')
        Role = models.get('Role')

        if not all([User, Role]):
            return jsonify({
                'success': False,
                'message': 'خطأ في تحميل النماذج'
            }), 500

        user = User.query.get_or_404(user_id)
        available_roles = Role.query.filter_by(is_active=True).all()

        user_permissions = [perm.name for perm in user.permissions]

        return jsonify({
            'success': True,
            'user': {
                'id': user.id,
                'full_name': user.full_name,
                'username': user.username,
                'role_id': user.role_id,
                'role_name': user.user_role.display_name if user.user_role else user.role,
                'permissions': user_permissions
            },
            'available_roles': [{
                'id': role.id,
                'name': role.name,
                'display_name': role.display_name
            } for role in available_roles]
        })

    except Exception as e:
        print(f"خطأ في جلب صلاحيات المستخدم: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@bp.route('/permissions/users/<int:user_id>', methods=['PUT'])
@login_required
@permission_required('manage_user_permissions')
def update_user_permissions(user_id):
    """تحديث صلاحيات مستخدم"""
    try:
        models = get_models()
        User = models.get('User')
        Role = models.get('Role')
        Permission = models.get('Permission')
        db = current_app.extensions['sqlalchemy'].db

        if not all([User, Role, Permission]):
            return jsonify({
                'success': False,
                'message': 'خطأ في تحميل النماذج'
            }), 500

        user = User.query.get_or_404(user_id)
        data = request.get_json()

        role_id = data.get('role_id')
        permission_names = data.get('permissions', [])

        # تحديث الدور
        if role_id:
            role = Role.query.get(role_id)
            if role:
                user.role_id = role_id
                user.role = role.name  # للتوافق مع النظام القديم

        # مسح الصلاحيات الحالية
        user.permissions.clear()

        # إضافة الصلاحيات الجديدة
        for perm_name in permission_names:
            permission = Permission.query.filter_by(name=perm_name).first()
            if permission:
                user.permissions.append(permission)

        user.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث صلاحيات المستخدم بنجاح'
        })

    except Exception as e:
        print(f"خطأ في تحديث صلاحيات المستخدم: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@bp.route('/permissions/initialize', methods=['POST'])
@login_required
@permission_required('manage_permissions')
def initialize_permissions_route():
    """تهيئة الصلاحيات والأدوار الافتراضية"""
    try:
        models = get_models()
        db = current_app.extensions['sqlalchemy'].db

        initialize_permissions(db, models)

        return jsonify({
            'success': True,
            'message': 'تم تهيئة الصلاحيات والأدوار بنجاح'
        })

    except Exception as e:
        print(f"خطأ في تهيئة الصلاحيات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500
