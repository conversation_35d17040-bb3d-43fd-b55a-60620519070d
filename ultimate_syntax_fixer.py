#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الإصلاح النهائي والقوي لجميع أخطاء بناء الجملة
"""

import os
import re
import glob

def fix_critical_syntax_errors():
    """إصلاح الأخطاء الحرجة في بناء الجملة"""
    
    print("🔥 إصلاح الأخطاء الحرجة...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # إصلاحات قوية ومحددة
            
            # 1. إصلاح "unexpected '}', expected ')'"
            content = re.sub(r'\{\{\s*([^}]+?)\(\s*([^}]*?)\s*\}\s*\}\}', r'{{ \1(\2) }}', content)
            content = re.sub(r'\{\{\s*([^}]+?)\s*\}\s*\}\}', r'{{ \1 }}', content)
            
            # 2. إصلاح "unexpected ')'"
            content = re.sub(r'\{\{\s*([^}]+?)\)\s*\)\s*\}\}', r'{{ \1) }}', content)
            content = re.sub(r'\{\{\s*([^}]+?)\(\s*\(\s*([^}]*?)\s*\}\}', r'{{ \1(\2 }}', content)
            
            # 3. إصلاح "expected token 'end of print statement', got 'else'"
            content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s*\n\s*([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}', content, flags=re.MULTILINE)
            content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}', content)
            
            # 4. إصلاح الأسطر المقسمة
            content = re.sub(r'\{\{\s*([^}]+?)\n\s*([^}]+?)\s*\}\}', r'{{ \1 \2 }}', content, flags=re.MULTILINE)
            content = re.sub(r'\{\%\s*([^%]+?)\n\s*([^%]+?)\s*\%\}', r'{% \1 \2 %}', content, flags=re.MULTILINE)
            
            # 5. إصلاح الأقواس المضاعفة والمفقودة
            content = re.sub(r'\{\{\s*\{\{', r'{{', content)
            content = re.sub(r'\}\}\s*\}\}', r'}}', content)
            content = re.sub(r'\{\%\s*\{\%', r'{%', content)
            content = re.sub(r'\%\}\s*\%\}', r'%}', content)
            
            # 6. إصلاح المسافات الإضافية
            content = re.sub(r'\{\{\s{2,}', r'{{ ', content)
            content = re.sub(r'\s{2,}\}\}', r' }}', content)
            content = re.sub(r'\{\%\s{2,}', r'{% ', content)
            content = re.sub(r'\s{2,}\%\}', r' %}', content)
            
            # 7. إصلاح علامات Django في Jinja2
            content = re.sub(r'\{\%\s*url\s+([^%]+?)\s*\%\}', r'{{ url_for(\1) }}', content)
            content = re.sub(r'\{\%\s*widthratio\s+([^%]+?)\s*\%\}', r'{{ (\1) }}', content)
            
            # 8. إصلاح الفلاتر الخاطئة
            content = re.sub(r'\|date:"([^"]+)"', r'|strftime("\1")', content)
            content = re.sub(r'\|floatformat:(\d+)', r'|round(\1)', content)
            content = re.sub(r'\|default:([^}]+)', r'|default(\1)', content)
            content = re.sub(r'\|truncatechars:(\d+)', r'|truncate(\1)', content)
            
            # 9. إصلاح format مضاعف
            content = re.sub(r'format\(format\(([^)]+)\)\)', r'format(\1)', content)
            content = re.sub(r'\.format\(\.format\(([^)]+)\)\)', r'.format(\1)', content)
            
            # 10. إصلاحات نصية مباشرة للمشاكل الشائعة
            direct_fixes = [
                # dashboard.html
                ('case.created_at else\n                "-"', 'case.created_at else "-"'),
                ('case.created_at else "-")', 'case.created_at else "-"'),
                
                # مشاكل عامة
                ('if user.email else\n                user.username', 'if user.email else user.username'),
                ('if user.full_name else\n                user.username', 'if user.full_name else user.username'),
                ('if employee.user.email else\n                employee.user.username', 'if employee.user.email else employee.user.username'),
                
                # أقواس إضافية
                ('))', ')'),
                ('((', '('),
                ('}})', '}}'),
                ('{{{', '{{'),
                ('%%}', '%}'),
                ('{%%', '{%'),
                
                # مسافات
                ('  }}', ' }}'),
                ('{{  ', '{{ '),
                ('  %}', ' %}'),
                ('{%  ', '{% '),
                
                # علامات مختلطة
                ('{% url ', '{{ url_for('),
                (' %}"', ') }}"'),
                ('{% widthratio', '{{ ('),
                (' %} %', ') }} %'),
                
                # إصلاحات خاصة
                ('else "-"  }}', 'else "-" }}'),
                ('case.lawyer  %}', 'case.lawyer %}'),
                ('endif  %}', 'endif %}'),
                ('case.client.full_name  }}', 'case.client.full_name }}'),
                ('case.lawyer.user.full_name   }}', 'case.lawyer.user.full_name }}'),
            ]
            
            for old, new in direct_fixes:
                content = content.replace(old, new)
            
            # حفظ الملف إذا تم تغييره
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # حساب عدد الإصلاحات
                fixes = len(re.findall(r'{{|{%', content)) - len(re.findall(r'{{|{%', original_content))
                if fixes < 0:
                    fixes = abs(fixes)
                else:
                    fixes = 1  # على الأقل إصلاح واحد
                
                print(f"  🔧 {file_path}: تم إصلاحه")
                total_fixes += 1
            
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} ملف")
    return total_fixes

def validate_and_test():
    """التحقق واختبار النتائج"""
    
    print("\n🧪 اختبار النتائج...")
    
    critical_files = [
        'templates/base.html',
        'templates/dashboard.html',
        'templates/login.html',
        'templates/admin/index.html',
        'templates/cases/index.html',
        'templates/clients/index.html',
        'templates/employees/index.html',
    ]
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        valid_count = 0
        error_count = 0
        errors = []
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                try:
                    relative_path = os.path.relpath(file_path, 'templates').replace('\\', '/')
                    template = env.get_template(relative_path)
                    print(f"  ✅ {relative_path}: صحيح")
                    valid_count += 1
                    
                except TemplateSyntaxError as e:
                    error_msg = str(e)[:80] + "..." if len(str(e)) > 80 else str(e)
                    print(f"  ❌ {relative_path}: {error_msg}")
                    errors.append(f"{relative_path}: {error_msg}")
                    error_count += 1
                except Exception as e:
                    error_msg = str(e)[:80] + "..." if len(str(e)) > 80 else str(e)
                    print(f"  ⚠️ {relative_path}: {error_msg}")
                    errors.append(f"{relative_path}: {error_msg}")
                    error_count += 1
        
        print(f"\n📊 نتائج الاختبار:")
        print(f"  ✅ ملفات صحيحة: {valid_count}")
        print(f"  ❌ ملفات بها أخطاء: {error_count}")
        
        return error_count == 0, errors
        
    except ImportError:
        print("❌ لا يمكن اختبار بناء الجملة - Jinja2 غير متوفر")
        return False, ["لا يمكن الاختبار"]

def create_minimal_working_templates():
    """إنشاء قوالب أساسية تعمل بشكل مضمون"""
    
    print("\n🛠️ إنشاء قوالب أساسية احتياطية...")
    
    # قالب أساسي مبسط
    minimal_base = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام الشؤون القانونية{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        {% block content %}{% endblock %}
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>'''
    
    # لوحة تحكم مبسطة
    minimal_dashboard = '''{% extends "base.html" %}
{% block title %}لوحة التحكم{% endblock %}
{% block content %}
<div class="row">
    <div class="col-12">
        <h1>لوحة التحكم</h1>
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h4>القضايا</h4>
                        <p>إدارة القضايا</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h4>العملاء</h4>
                        <p>إدارة العملاء</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h4>الموظفين</h4>
                        <p>إدارة الموظفين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <h4>التقارير</h4>
                        <p>عرض التقارير</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
    
    # حفظ القوالب الاحتياطية
    backup_templates = {
        'templates/base_minimal.html': minimal_base,
        'templates/dashboard_minimal.html': minimal_dashboard,
    }
    
    for file_path, content in backup_templates.items():
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ تم إنشاء {file_path}")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء {file_path}: {e}")

def main():
    """الدالة الرئيسية للإصلاح النهائي"""
    
    print("🔥 بدء الإصلاح النهائي والقوي...")
    
    # إصلاح الأخطاء الحرجة
    fixed_files = fix_critical_syntax_errors()
    
    # اختبار النتائج
    is_valid, errors = validate_and_test()
    
    # إنشاء قوالب احتياطية
    create_minimal_working_templates()
    
    print("\n" + "="*60)
    print("🔥 تقرير الإصلاح النهائي")
    print("="*60)
    print(f"🔧 ملفات تم إصلاحها: {fixed_files}")
    print(f"📁 الملفات الحرجة: {'✅ جميعها صحيحة' if is_valid else f'❌ {len(errors)} بها أخطاء'}")
    
    if is_valid:
        print("\n🎉 تم الإصلاح النهائي بنجاح!")
        print("✅ جميع الملفات الحرجة تعمل بشكل صحيح")
        print("🚀 النظام جاهز للاستخدام")
    else:
        print("\n⚠️ لا تزال هناك بعض المشاكل:")
        for error in errors[:3]:
            print(f"  - {error}")
        if len(errors) > 3:
            print(f"  ... و {len(errors) - 3} أخطاء أخرى")
        print("\n💡 تم إنشاء قوالب احتياطية تعمل بشكل مضمون")
    
    print("="*60)
    
    return is_valid

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
