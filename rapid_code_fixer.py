#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع وشامل لجميع مشاكل الأكواد
"""

import os
import re
import glob

def rapid_fix_all_code_issues():
    """إصلاح سريع لجميع مشاكل الأكواد"""
    
    print("⚡ بدء الإصلاح السريع لجميع مشاكل الأكواد...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # إصلاحات سريعة وشاملة
            rapid_fixes = [
                # إصلاح الأقواس المفقودة في default
                (r'\|default\((\d+)\s+\}\}', r'|default(\1) }}'),
                (r'\|default\(([^)]+)\s+\}\}', r'|default(\1) }}'),
                
                # إصلاح الأقواس المفقودة في format
                (r'\.format\(([^)]+)\s+\}\}', r'.format(\1) }}'),
                (r'"%.0f"\.format\(([^)]+)\s+\}\}', r'"%.0f".format(\1) }}'),
                (r'"%.1f"\.format\(([^)]+)\s+\}\}', r'"%.1f".format(\1) }}'),
                (r'"%.2f"\.format\(([^)]+)\s+\}\}', r'"%.2f".format(\1) }}'),
                (r'"{:,.0f}"\.format\(([^)]+)\s+\}\}', r'"{:,.0f}".format(\1) }}'),
                (r'"{:,.1f}"\.format\(([^)]+)\s+\}\}', r'"{:,.1f}".format(\1) }}'),
                (r'"{:,.2f}"\.format\(([^)]+)\s+\}\}', r'"{:,.2f}".format(\1) }}'),
                
                # إصلاح علامات الإغلاق المفقودة
                (r'</small\s*>', r'</small>'),
                (r'</div\s*>', r'</div>'),
                (r'</span\s*>', r'</span>'),
                (r'</p\s*>', r'</p>'),
                (r'</h[1-6]\s*>', r'</h\1>'),
                
                # إصلاح الأقواس المضاعفة
                (r'\}\}\s*\}\}', r'}}'),
                (r'\{\{\s*\{\{', r'{{'),
                (r'\%\}\s*\%\}', r'%}'),
                (r'\{\%\s*\{\%', r'{%'),
                
                # إصلاح المسافات الإضافية
                (r'\{\{\s{2,}', r'{{ '),
                (r'\s{2,}\}\}', r' }}'),
                (r'\{\%\s{2,}', r'{% '),
                (r'\s{2,}\%\}', r' %}'),
                
                # إصلاح التعبيرات الشرطية المقسمة
                (r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}'),
                
                # إصلاح الأسطر المقسمة
                (r'\{\{\s*([^}]+?)\n\s*([^}]+?)\s*\}\}', r'{{ \1 \2 }}'),
                (r'\{\%\s*([^%]+?)\n\s*([^%]+?)\s*\%\}', r'{% \1 \2 %}'),
                
                # إصلاح علامات HTML المكسورة
                (r'<([a-zA-Z]+)([^>]*?)>\s*>', r'<\1\2>'),
                (r'<\s*>', r''),
                (r'>\s*<\s*', r'><'),
                
                # إصلاح الفلاتر الخاطئة
                (r'\|date:"([^"]+)"', r'|strftime("\1")'),
                (r'\|floatformat:(\d+)', r'|round(\1)'),
                (r'\|truncatechars:(\d+)', r'|truncate(\1)'),
                
                # إصلاح format مضاعف
                (r'format\(format\(([^)]+)\)\)', r'format(\1)'),
                
                # إصلاح الأقواس في strftime
                (r'\.strftime\(([^)]+)\s+if\s+([^}]+?)\s+else\s+([^}]+?)', r'.strftime(\1) if \2 else \3'),
            ]
            
            for pattern, replacement in rapid_fixes:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                if matches:
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
                    file_fixes += len(matches)
            
            # إصلاحات نصية مباشرة للمشاكل الشائعة
            text_fixes = [
                # dashboard.html - الأقواس المفقودة
                ('|default(0 }}', '|default(0) }}'),
                ('|default(1 }}', '|default(1) }}'),
                ('|default("" }}', '|default("") }}'),
                ('.format(total_revenue }}', '.format(total_revenue) }}'),
                ('.format(pending_revenue }}', '.format(pending_revenue) }}'),
                ('.format(total_cases }}', '.format(total_cases) }}'),
                ('.format(active_cases }}', '.format(active_cases) }}'),
                ('.format(completed_cases }}', '.format(completed_cases) }}'),
                
                # علامات HTML مكسورة
                ('</small>', '</small>'),
                ('</div>', '</div>'),
                ('</span>', '</span>'),
                ('</p>', '</p>'),
                ('</h1>', '</h1>'),
                ('</h2>', '</h2>'),
                ('</h3>', '</h3>'),
                ('</h4>', '</h4>'),
                ('</h5>', '</h5>'),
                ('</h6>', '</h6>'),
                
                # أقواس إضافية
                ('))', ')'),
                ('((', '('),
                ('}})', '}}'),
                ('{{{', '{{'),
                ('%%}', '%}'),
                ('{%%', '{%'),
                
                # مسافات إضافية
                ('  }}', ' }}'),
                ('{{  ', '{{ '),
                ('  %}', ' %}'),
                ('{%  ', '{% '),
                
                # علامات مختلطة
                ('{% url ', '{{ url_for('),
                (' %}"', ') }}"'),
                ('{% widthratio', '{{ ('),
                (' %} %', ') }} %'),
                
                # إصلاحات خاصة
                ('> >', '>'),
                ('< <', '<'),
                ('> •', ' • '),
                ('• <', ' • <'),
                
                # تعبيرات شرطية مقسمة
                ('if user.email else\n                user.username', 'if user.email else user.username'),
                ('if user.full_name else\n                user.username', 'if user.full_name else user.username'),
                ('if employee.user.email else\n                employee.user.username', 'if employee.user.email else employee.user.username'),
                ('if case.created_at else\n                "-"', 'if case.created_at else "-"'),
                
                # JavaScript/CSS مكسور
                ('});', '});'),
                ('};', '};'),
                ('{;', '{'),
                (';}', '}'),
            ]
            
            for old, new in text_fixes:
                if old in content:
                    content = content.replace(old, new)
                    file_fixes += 1
            
            # حفظ الملف إذا تم إجراء إصلاحات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ⚡ {file_path}: تم إصلاح {file_fixes} مشكلة")
                total_fixes += file_fixes
            
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} مشكلة إجمالي")
    return total_fixes

def fix_dashboard_specifically():
    """إصلاح dashboard.html بشكل محدد"""
    
    print("\n🎯 إصلاح dashboard.html بشكل محدد...")
    
    dashboard_file = 'templates/dashboard.html'
    if not os.path.exists(dashboard_file):
        print("❌ dashboard.html غير موجود")
        return False
    
    try:
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # إصلاحات محددة لـ dashboard
        specific_fixes = [
            # الأقواس المفقودة
            ('|default(0 }}', '|default(0) }}'),
            ('|default(1 }}', '|default(1) }}'),
            ('.format(total_revenue }}', '.format(total_revenue) }}'),
            ('.format(pending_revenue }}', '.format(pending_revenue) }}'),
            ('.format(total_cases }}', '.format(total_cases) }}'),
            ('.format(active_cases }}', '.format(active_cases) }}'),
            ('.format(completed_cases }}', '.format(completed_cases) }}'),
            ('.format(total_clients }}', '.format(total_clients) }}'),
            ('.format(total_employees }}', '.format(total_employees) }}'),
            
            # علامات HTML مكسورة
            ('</small>', '</small>'),
            ('> >', '>'),
            ('< <', '<'),
            
            # تنظيف المسافات
            ('  }}', ' }}'),
            ('{{  ', '{{ '),
            ('  %}', ' %}'),
            ('{%  ', '{% '),
            
            # إصلاح التعبيرات الشرطية
            ('case.created_at else\n                "-"', 'case.created_at else "-"'),
            ('case.created_at else "-")', 'case.created_at else "-"'),
        ]
        
        fixes_count = 0
        for old, new in specific_fixes:
            if old in content:
                content = content.replace(old, new)
                fixes_count += 1
        
        if content != original_content:
            with open(dashboard_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ تم إصلاح {fixes_count} مشكلة في dashboard.html")
            return True
        else:
            print("ℹ️ dashboard.html لا يحتاج إصلاح")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح dashboard.html: {e}")
        return False

def validate_critical_syntax():
    """التحقق السريع من بناء الجملة"""
    
    print("\n🔍 التحقق السريع من بناء الجملة...")
    
    critical_files = [
        'templates/dashboard.html',
        'templates/base.html',
        'templates/login.html',
    ]
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        valid_count = 0
        error_count = 0
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                try:
                    relative_path = os.path.relpath(file_path, 'templates').replace('\\', '/')
                    template = env.get_template(relative_path)
                    print(f"  ✅ {relative_path}: صحيح")
                    valid_count += 1
                    
                except TemplateSyntaxError as e:
                    print(f"  ❌ {relative_path}: خطأ في بناء الجملة")
                    error_count += 1
                except Exception as e:
                    print(f"  ⚠️ {relative_path}: خطأ في التحميل")
                    error_count += 1
        
        print(f"\n📊 النتائج:")
        print(f"  ✅ ملفات صحيحة: {valid_count}")
        print(f"  ❌ ملفات بها أخطاء: {error_count}")
        
        return error_count == 0
        
    except ImportError:
        print("❌ لا يمكن التحقق من بناء الجملة")
        return False

def main():
    """الدالة الرئيسية للإصلاح السريع"""
    
    print("⚡ بدء الإصلاح السريع الشامل...")
    
    # إصلاح dashboard أولاً
    dashboard_fixed = fix_dashboard_specifically()
    
    # إصلاح شامل لجميع الملفات
    total_fixes = rapid_fix_all_code_issues()
    
    # التحقق السريع
    syntax_valid = validate_critical_syntax()
    
    print("\n" + "="*50)
    print("⚡ تقرير الإصلاح السريع")
    print("="*50)
    print(f"🎯 dashboard.html: {'✅ مُصلح' if dashboard_fixed else '❌ يحتاج مراجعة'}")
    print(f"🔧 إجمالي الإصلاحات: {total_fixes}")
    print(f"📁 بناء الجملة: {'✅ صحيح' if syntax_valid else '❌ يحتاج مراجعة'}")
    
    if dashboard_fixed and syntax_valid:
        print("\n🎉 تم الإصلاح السريع بنجاح!")
        print("✅ النظام جاهز للاستخدام")
    else:
        print("\n⚠️ قد تحتاج بعض الملفات مراجعة إضافية")
    
    print("="*50)
    
    return dashboard_fixed and syntax_valid

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
