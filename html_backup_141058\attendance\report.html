{% extends "base.html" %}

{% block title %}تقرير الحضور والغياب{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-chart-bar"></i>
            {{ report_title }}
          </h3>
          <div class="card-tools">
            <button type="button" class="btn btn-primary btn-sm" onclick="printReport()">
              <i class="fas fa-print"></i>
              طباعة التقرير
            </button>
            <a href="{{ url_for('attendance.index' }}" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a>
          </div>
        </div>

        <div class="card-body">
          <!-- فلاتر التقرير -->
          <div class="card card-outline card-primary collapsed-card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-filter"></i>
                فلاتر التقرير
              </h3>
              <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
            <div class="card-body">
              <form method="GET" action="{{ url_for('attendance_report' }}">
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="employee_id">الموظف</label>
                      <select class="form-control" id="employee_id" name="employee_id">
                        <option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" 
                                {% if employee_id == employee.id %}selected{% endif %}>
                          {{ employee.full_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="department_id">القسم</label>
                      <select class="form-control" id="department_id" name="department_id">
                        <option value="">جميع الأقسام</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" 
                                {% if department_id == department.id %}selected{% endif %}>
                          {{ department.name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="date_from">من تاريخ</label>
                      <input type="date" class="form-control" id="date_from" name="date_from" 
                             value="{{ date_from }}">
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="date_to">إلى تاريخ</label>
                      <input type="date" class="form-control" id="date_to" name="date_to" 
                             value="{{ date_to }}">
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-search"></i>
                      تحديث التقرير
                    </button>
                    <a href="{{ url_for('attendance_report' }}" class="btn btn-secondary">
                      <i class="fas fa-undo"></i>
                      إعادة تعيين
                    </a>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- إحصائيات التقرير -->
          <div class="row mb-4">
            <div class="col-md-2">
              <div class="info-box bg-info">
                <span class="info-box-icon"><i class="fas fa-list"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">إجمالي السجلات</span>
                  <span class="info-box-number">{{ stats.total_records }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-success">
                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">حاضر</span>
                  <span class="info-box-number">{{ stats.present_count }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-danger">
                <span class="info-box-icon"><i class="fas fa-times"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">غائب</span>
                  <span class="info-box-number">{{ stats.absent_count }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-warning">
                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">متأخر</span>
                  <span class="info-box-number">{{ stats.late_count }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-primary">
                <span class="info-box-icon"><i class="fas fa-hourglass-half"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">إجمالي الساعات</span>
                  <span class="info-box-number">{{ "%.1f".format(stats.total_hours }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="info-box bg-secondary">
                <span class="info-box-icon"><i class="fas fa-percentage"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">نسبة الحضور</span>
                  <span class="info-box-number">{{ "%.1f".format(stats.attendance_rate }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- جدول التقرير -->
          <div id="reportContent">
            <div class="report-header text-center mb-4" style="display: none;">
              <h2>{{ report_title }}</h2>
              <p><strong>الفترة:</strong> {{ report_period }}</p>
              <p><strong>تاريخ التقرير:</strong> {{ report_date) }}</p>
              <hr>
            </div>

            <div class="table-responsive">
              <table class="table table-bordered table-striped">
                <thead>
                  <tr>
                    <th>التاريخ</th>
                    <th>الموظف</th>
                    <th>القسم</th>
                    <th>وقت الحضور</th>
                    <th>وقت الانصراف</th>
                    <th>ساعات العمل</th>
                    <th>الحالة</th>
                    <th>الموقع</th>
                    <th class="no-print">ملاحظات</th>
                  </tr>
                </thead>
                <tbody>
                  {% for record in attendance_records %}
                  <tr>
                    <td>{{ record.date.strftime("%Y-%m-%d") if record.date else "-" }}</td>
                    <td>
                      <strong>{{ record.employee.full_name }}</strong><br>
                      <small class="text-muted">{{ record.employee.employee_number }}</small>
                    </td>
                    <td>
                      {% if record.employee.department %}
                        {{ record.employee.department.name }}
                      {% else %}
                        <span class="text-muted">-</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if record.check_in %}
                        {{ record.check_in.strftime("%H:%M") if record.check_in else "-" }}
                      {% else %}
                        <span class="text-muted">-</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if record.check_out %}
                        {{ record.check_out.strftime("%H:%M") if record.check_out else "-" }}
                      {% else %}
                        <span class="text-muted">-</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if record.hours_worked %}
                        {{ "%.2f".format(record.hours_worked }} ساعة
                      {% else %}
                        <span class="text-muted">-</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if record.status == 'present' %}
                        <span class="badge badge-success">حاضر</span>
                      {% elif record.status == 'absent' %}
                        <span class="badge badge-danger">غائب</span>
                      {% elif record.status == 'late' %}
                        <span class="badge badge-warning">متأخر</span>
                      {% elif record.status == 'half_day' %}
                        <span class="badge badge-info">نصف يوم</span>
                      {% else %}
                        <span class="badge badge-secondary">{{ record.status }}</span>
                      {% endif %}
                    </td>
                    <td>
                      <small>{{ record.location or '-' }}</small>
                    </td>
                    <td class="no-print">
                      <small class="text-muted">{{ record.notes or '-' }}</small>
                    </td>
                  </tr>
                  {% else %}
                  <tr>
                    <td colspan="9" class="text-center text-muted">
                      <i class="fas fa-inbox fa-2x mb-2"></i><br>
                      لا توجد سجلات حضور في الفترة المحددة
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>

            <!-- ملخص الإحصائيات للطباعة -->
            <div class="print-only mt-4">
              <h4>ملخص الإحصائيات:</h4>
              <div class="row">
                <div class="col-md-6">
                  <ul class="list-unstyled">
                    <li><strong>إجمالي السجلات:</strong> {{ stats.total_records }}</li>
                    <li><strong>عدد الحاضرين:</strong> {{ stats.present_count }}</li>
                    <li><strong>عدد الغائبين:</strong> {{ stats.absent_count }}</li>
                  </ul>
                </div>
                <div class="col-md-6">
                  <ul class="list-unstyled">
                    <li><strong>عدد المتأخرين:</strong> {{ stats.late_count }}</li>
                    <li><strong>إجمالي ساعات العمل:</strong> {{ "%.1f".format(stats.total_hours }} ساعة</li>
                    <li><strong>نسبة الحضور:</strong> {{ "%.1f".format(stats.attendance_rate) }}%</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .report-header {
    display: block !important;
  }
  
  .card-header,
  .card-tools,
  .btn,
  .sidebar,
  .main-header,
  .main-footer {
    display: none !important;
  }
  
  .content-wrapper {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .card {
    border: none !important;
    box-shadow: none !important;
  }
  
  .table {
    font-size: 12px;
  }
  
  .badge {
    color: #000 !important;
    background-color: transparent !important;
    border: 1px solid #000 !important;
  }
}

.print-only {
  display: none;
}
</style>

<script>
function printReport() {
  // إظهار عناصر الطباعة
  document.querySelector('.report-header').style.display = 'block';
  
  // طباعة الصفحة
  window.print();
  
  // إخفاء عناصر الطباعة بعد الطباعة
  setTimeout() => {
    document.querySelector('.report-header').style.display = 'none';
  }, 1000);
}

// تحسين عرض التقرير
document.addEventListener('DOMContentLoaded', function() {
  // إضافة أرقام الصفوف
  const rows = document.querySelectorAll('tbody tr');
  rows.forEach(row, index) => {
    if (!row.querySelector('td[colspan]') {
      const firstCell = row.querySelector('td');
      if (firstCell) {
        firstCell.innerHTML = `<small class="text-muted">${index + 1}.</small> ${firstCell.innerHTML}`;
      }
    }
  });
});
</script>
{% endblock %}
