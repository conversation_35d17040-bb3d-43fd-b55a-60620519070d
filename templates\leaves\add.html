{% extends "base.html" %} {% block title %}طلب إجازة جديد{% endblock %} {% block content %}
<div class="container-fluid"><div class="row"><div class="col-12"><div class="card"><div class="card-header"><h3 class="card-title"><i class="fas fa-plus"></i>
            طلب إجازة جديد
          </h3><div class="card-tools"><a
              href="{{ url_for ('leaves.index' }}"
              class="btn btn-secondary btn-sm"
        ><i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a></div></div><form id="leaveForm" enctype="multipart/form-data"><div class="card-body"><div class="row"><div class="col-md-6"><div class="form-group"><label for="employee_id"
                >الموظف <span class="text-danger">*</span></label><select
                    class="form-control"
                    id="employee_id"
                    name="employee_id"
                    required
              ><option value="">اختر الموظف</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}">
                      {{ employee.full_name }} - {{ employee.employee_number }}
                    </option>
                    {% endfor %}
                  </select></div></div><div class="col-md-6"><div class="form-group"><label for="leave_type"
                >نوع الإجازة <span class="text-danger">*</span></label><select
                    class="form-control"
                    id="leave_type"
                    name="leave_type"
                    required
              ><option value="">اختر نوع الإجازة</option>
                    {% for type_code, type_name in leave_types %}
                    <option value="{{ type_code }}">{{ type_name }}</option>
                   ) {% endfor %}
                  </select></div></div></div><div class="row"><div class="col-md-4"><div class="form-group"><label for="start_date"
                >تاريخ البداية <span class="text-danger">*</span></label><input
                    type="date"
                    class="form-control"
                    id="start_date"
                    name="start_date"
                    min="{{ today }}"
                    required
                  /></div></div><div class="col-md-4"><div class="form-group"><label for="end_date"
                >تاريخ النهاية <span class="text-danger">*</span></label><input
                    type="date"
                    class="form-control"
                    id="end_date"
                    name="end_date"
                    min="{{ today }}"
                    required
                  /></div></div><div class="col-md-4"><div class="form-group"><label for="days_count">عدد الأيام</label><input
                    type="number"
                    class="form-control"
                    id="days_count"
                    name="days_count"
                    min="1"
                    readonly
                  /><small class="form-text text-muted"
                >يتم حسابها تلقائياً</small></div></div></div><div class="row"><div class="col-md-6"><div class="form-group"><label for="is_paid">نوع الإجازة</label><div class="form-check"><input
                      class="form-check-input"
                      type="checkbox"
                      id="is_paid"
                      name="is_paid"
                      checked
                    /><label class="form-check-label" for="is_paid">
                      إجازة براتب
                    </label></div><small class="form-text text-muted"
                >إذا لم تكن محددة، ستكون إجازة بدون راتب</small></div></div><div class="col-md-6"><div class="form-group"><label for="attachment">مرفق (اختياري)</label><input
                    type="file"
                    class="form-control-file"
                    id="attachment"
                    name="attachment"
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  /><small class="form-text text-muted">
                    الملفات المسموحة: PDF, DOC, DOCX, JPG, PNG (حد أقصى 5
                    ميجابايت)
                  </small></div></div></div><div class="row"><div class="col-12"><div class="form-group"><label for="reason"
                >سبب الإجازة <span class="text-danger">*</span></label><textarea
                    class="form-control"
                    id="reason"
                    name="reason"
                    rows="4"
                    placeholder="اذكر سبب طلب الإجازة بالتفصيل..."
                    required
              ></textarea></div></div></div><!-- معلومات إضافية --><div class="row"><div class="col-12"><div class="card card-outline card-info"><div class="card-header"><h3 class="card-title">معلومات مهمة</h3></div><div class="card-body"><ul class="list-unstyled"><li><i class="fas fa-info-circle text-info"></i> يجب تقديم
                        طلب الإجازة قبل التاريخ المطلوب بوقت كافٍ
                      </li><li><i class="fas fa-info-circle text-info"></i> الإجازات
                        المرضية تتطلب تقرير طبي
                      </li><li><i class="fas fa-info-circle text-info"></i> الإجازات
                        الطارئة قد تحتاج موافقة فورية
                      </li><li><i class="fas fa-info-circle text-info"></i> يمكن إرفاق
                        المستندات الداعمة للطلب
                      </li></ul></div></div></div></div></div><div class="card-footer"><button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane"></i>
              إرسال طلب الإجازة
            </button><a href="{{ url_for('leaves.index' }}" class="btn btn-secondary"><i class="fas fa-times"></i>
              إلغاء
            </a></div></form></div></div></div></div><script>
  // حساب عدد الأيام تلقائياً
  function calculateDays() {
    const startDate = document.getElementById("start_date").value;
    const endDate = document.getElementById("end_date").value;

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (end >= start) {
        const timeDiff = end.getTime() - start.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24) + 1;
        document.getElementById("days_count").value = daysDiff;
      } else {
        document.getElementById("days_count").value = "";
        showAlert("danger", "تاريخ النهاية يجب أن يكون بعد تاريخ البداية");
      }
    }
  }

  // ربط الأحداث
  document.getElementById("start_date").addEventListener("change", function () {
    // تحديث الحد الأدنى لتاريخ النهاية
    document.getElementById("end_date").min = this.value;
    calculateDays();
  });

  document.getElementById("end_date").addEventListener("change", calculateDays);

  // تحديث نوع الإجازة حسب النوع المختار
  document.getElementById("leave_type").addEventListener("change", function () {
    const leaveType = this.value;
    const isPaidCheckbox = document.getElementById("is_paid");

    // بعض أنواع الإجازات تكون بدون راتب افتراضياً
    if (leaveType === "unpaid") {
      isPaidCheckbox.checked = false;
      isPaidCheckbox.disabled = true;
    } else {
      isPaidCheckbox.disabled = false;
      if (
        leaveType === "annual" ||
        leaveType === "sick" ||
        leaveType === "maternity" ||
        leaveType === "paternity"
      ) {
        isPaidCheckbox.checked = true;
      }
    }
  });

  // التحقق من حجم الملف
  document.getElementById("attachment").addEventListener("change", function () {
    const file = this.files[0];
    if (file) {
      const maxSize = 5 * 1024 * 1024; // 5 ميجابايت
      if (file.size > maxSize) {
        alert("حجم الملف يجب أن يكون أقل من 5 ميجابايت");
        this.value = "";
      }
    }
  });

  // إرسال النموذج
  document.getElementById("leaveForm").addEventListener("submit", function (e) {
    e.preventDefault();

    // التحقق من صحة التواريخ
    const startDate = new Date(document.getElementById("start_date").value);
    const endDate = new Date(document.getElementById("end_date").value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (startDate < today) {
      showAlert("danger", "لا يمكن تقديم طلب إجازة لتاريخ سابق");
      return;
    }

    if (endDate < startDate) {
      showAlert("danger", "تاريخ النهاية يجب أن يكون بعد تاريخ البداية");
      return;
    }

    const formData = new FormData(this);

    // إظهار مؤشر التحميل
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML =
      '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
    submitBtn.disabled = true;

    fetch('{{ url_for ("leaves:add" %}',) {
      method: "POST",
      body: formData,
    })
      .then(response) => response.json()
      .then(data) => {
        if (data.success) {
          // إظهار رسالة نجاح
          showAlert("success", data.message);

          // إعادة توجيه بعد ثانيتين
          setTimeout() => {
            window.location.href = '{{ url_for("leaves:index" %}';
          }, 2000);
        } else {
          showAlert("danger", data.message);

          // إعادة تفعيل الزر
          submitBtn.innerHTML = originalText;
          submitBtn.disabled = false;
        }
      })
      .catch(error) => {
        showAlert("danger", "حدث خطأ في الاتصال");

        // إعادة تفعيل الزر
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
      });
  });

  function showAlert(type, message) {
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert"><span>&times;</span></button>
  `;

    // إدراج التنبيه في أعلى الصفحة
    const container = document.querySelector(".container-fluid");
    container.insertBefore(alertDiv, container.firstChild);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout() => {
      alertDiv.remove();
    }, 5000);
  }

  // تعيين التاريخ الافتراضي
  document.addEventListener("DOMContentLoaded", function () {
    const today = new Date().toISOString().split("T")[0];
    document.getElementById("start_date").value = today;
    document.getElementById("end_date").value = today;
    calculateDays();
  });
</script>
{% endblock %}
