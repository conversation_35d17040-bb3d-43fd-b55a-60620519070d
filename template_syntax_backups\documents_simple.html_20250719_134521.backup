{% extends "base.html" %}

{% block title %}مستندات الموظف: {{ employee.full_name }} - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open me-2"></i>
        مستندات الموظف: {{ employee.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لتفاصيل الموظف
            </a>
        </div>
    </div>
</div>

<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <strong>رقم الموظف:</strong> {{ employee.employee_number }}
            </div>
            <div class="col-md-3">
                <strong>الاسم:</strong> {{ employee.full_name }}
            </div>
            <div class="col-md-3">
                <strong>القسم:</strong> {{ employee.department.name if employee.department else 'غير محدد' }}
            </div>
            <div class="col-md-3">
                <strong>المنصب:</strong> {{ employee.position or 'غير محدد' }}
            </div>
        </div>
    </div>
</div>

<!-- رسالة تطوير الميزة -->
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-tools fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">نظام إدارة المستندات</h4>
        <p class="text-muted mb-4">
            هذه الميزة قيد التطوير حالياً وستكون متاحة قريباً.<br>
            ستتمكن من رفع وإدارة جميع مستندات الموظف من خلال هذه الصفحة.
        </p>
        
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-info-circle me-1"></i>
                        الميزات المخططة:
                    </h6>
                    <ul class="text-start mb-0">
                        <li>رفع مستندات متعددة الأنواع (PDF, DOC, صور)</li>
                        <li>تصنيف المستندات (عقود، شهادات، تقارير طبية)</li>
                        <li>نظام بحث وفلترة متقدم</li>
                        <li>تتبع تواريخ انتهاء الصلاحية</li>
                        <li>مستندات سرية مع حماية إضافية</li>
                        <li>تحميل وطباعة المستندات</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="{{ url_for('employees.view', id=employee.id) }}" class="btn btn-primary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لتفاصيل الموظف
            </a>
            <a href="{{ url_for('employees.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list me-1"></i>
                قائمة الموظفين
            </a>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات الموظف
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>رقم الموظف:</strong></td>
                        <td>{{ employee.employee_number }}</td>
                    </tr>
                    <tr>
                        <td><strong>البريد الإلكتروني:</strong></td>
                        <td>{{ employee.email or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>الهاتف:</strong></td>
                        <td>{{ employee.phone or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>تاريخ التوظيف:</strong></td>
                        <td>{{ employee.hire_date.hire_date else 'غير محدد' |strftime("%Y-%m-%d") if employee }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    معلومات وظيفية
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>القسم:</strong></td>
                        <td>{{ employee.department.name if employee.department else 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>المنصب:</strong></td>
                        <td>{{ employee.position or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>نوع التوظيف:</strong></td>
                        <td>
                            {% if employee.employment_type == 'full_time' %}
                                دوام كامل
                            {% elif employee.employment_type == 'part_time' %}
                                دوام جزئي
                            {% elif employee.employment_type == 'contract' %}
                                عقد
                            {% elif employee.employment_type == 'intern' %}
                                متدرب
                            {% else %}
                                {{ employee.employment_type or 'غير محدد' }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>الحالة:</strong></td>
                        <td>
                            {% if employee.employment_status == 'active' %}
                                <span class="badge bg-success">نشط</span>
                            {% elif employee.employment_status == 'inactive' %}
                                <span class="badge bg-warning">غير نشط</span>
                            {% elif employee.employment_status == 'terminated' %}
                                <span class="badge bg-danger">منتهي الخدمة</span>
                            {% elif employee.employment_status == 'resigned' %}
                                <span class="badge bg-secondary">مستقيل</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ employee.employment_status or 'غير محدد' }}</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
