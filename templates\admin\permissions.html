{% extends "base.html" %} {% block title %}إدارة الصلاحيات - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
><h1 class="h2"><i class="fas fa-shield-alt me-2"></i>
    إدارة الصلاحيات والأدوار
  </h1><div class="btn-toolbar mb-2 mb-md-0"><div class="btn-group me-2"><button
        type="button"
        class="btn btn-primary"
        onclick="showCreateRoleModal()"
  ><i class="fas fa-plus me-1"></i>
        إضافة دور جديد
      </button><a href="{{ url_for ('admin.index' }}" class="btn btn-outline-secondary"><i class="fas fa-arrow-right me-1"></i>
        العودة للوحة التحكم
      </a></div></div></div><!-- التبويبات --><ul class="nav nav-tabs" id="permissionTabs" role="tablist"><li class="nav-item" role="presentation"><button
      class="nav-link active"
      id="roles-tab"
      data-bs-toggle="tab"
      data-bs-target="#roles"
      type="button"
      role="tab"
><i class="fas fa-users-cog me-1"></i>
      الأدوار
    </button></li><li class="nav-item" role="presentation"><button
      class="nav-link"
      id="permissions-tab"
      data-bs-toggle="tab"
      data-bs-target="#permissions"
      type="button"
      role="tab"
><i class="fas fa-key me-1"></i>
      الصلاحيات
    </button></li><li class="nav-item" role="presentation"><button
      class="nav-link"
      id="user-permissions-tab"
      data-bs-toggle="tab"
      data-bs-target="#user-permissions"
      type="button"
      role="tab"
><i class="fas fa-user-shield me-1"></i>
      صلاحيات المستخدمين
    </button></li></ul><div class="tab-content" id="permissionTabsContent"><!-- تبويب الأدوار --><div class="tab-pane fade show active" id="roles" role="tabpanel"><div class="card mt-3"><div class="card-header"><h5 class="mb-0">قائمة الأدوار</h5></div><div class="card-body"><div class="table-responsive"><table class="table table-hover"><thead><tr><th>اسم الدور</th><th>الوصف</th><th>عدد المستخدمين</th><th>عدد الصلاحيات</th><th>الحالة</th><th>الإجراءات</th></tr></thead><tbody id="roles-table-body">
              {% for role in roles %}
              <tr><td><strong>{{ role.display_name }}</strong>
                  {% if role.is_default %}
                  <span class="badge bg-info ms-1">افتراضي</span>
                  {% endif %}
                </td><td>{{ role.description }}</td><td><span class="badge bg-primary">{{ role.users|length }}</span></td><td><span class="badge bg-success"
                >{{ role.permissions|length }}</span></td><td>
                  {% if role.is_active %}
                  <span class="badge bg-success">نشط</span>
                  {% else %}
                  <span class="badge bg-warning">غير نشط</span>
                 ) {% endif %}
                </td><td><div class="btn-group btn-group-sm"><button
                      onclick="viewRolePermissions('{{ role.id }}')"
                      class="btn btn-outline-primary"
                      title="عرض الصلاحيات"
                ><i class="fas fa-eye"></i></button><button
                      onclick="editRole('{{ role.id }}')"
                      class="btn btn-outline-warning"
                      title="تعديل"
                ><i class="fas fa-edit"></i></button>
                    {% if not role.is_default %}
                    <button
                      onclick="deleteRole('{{ role.id }}')"
                      class="btn btn-outline-danger"
                      title="حذف"
                ><i class="fas fa-trash"></i></button>
                    {% endif %}
                  </div></td></tr>
              {% endfor %}
            </tbody></table></div></div></div></div><!-- تبويب الصلاحيات --><div class="tab-pane fade" id="permissions" role="tabpanel"><div class="card mt-3"><div class="card-header"><h5 class="mb-0">قائمة الصلاحيات</h5></div><div class="card-body">
        {% for category, perms in permissions_by_category %}
        <div class="mb-4"><h6 class="text-primary"><i class="fas fa-folder me-1"></i>
            {{ category|title }}
          </h6><div class="row">
            {% for perm_name, perm_display in perms %}
            <div class="col-md-6 col-lg-4 mb-2"><div class="card border-light"><div class="card-body p-2"><small class="text-muted">{{ perm_name }}</small><div class="fw-bold">{{ perm_display }}</div></div></div></div>
            {% endfor %}
          </div></div>
        {% endfor %}
      </div></div></div><!-- تبويب صلاحيات المستخدمين --><div class="tab-pane fade" id="user-permissions" role="tabpanel"><div class="card mt-3"><div class="card-header"><h5 class="mb-0">صلاحيات المستخدمين</h5></div><div class="card-body"><div class="table-responsive"><table class="table table-hover"><thead><tr><th>المستخدم</th><th>الدور</th><th>الصلاحيات الإضافية</th><th>آخر نشاط</th><th>الإجراءات</th></tr></thead><tbody>
              {% for user in users %}
              <tr><td><div class="d-flex align-items-center"><img
                      src="https://ui-avatars.com/api/?name={{ user.full_name }}&background=007bff&color=fff&size=32"
                      class="rounded-circle me-2"
                      alt="{{ user.full_name }}"
                    /><div><strong>{{ user.full_name }}</strong><br /><small class="text-muted"
                    >{{ user.username }}</small></div></div></td><td>
                  {% if user.user_role %}
                  <span class="badge bg-primary"
                >{{ user.user_role.display_name }}</span>
                  {% else %}
                  <span class="badge bg-secondary">{{ user.role }}</span>
                  {% endif %}
                </td><td><span class="badge bg-info"
                >{{ user.permissions|length }}</span></td><td>
                  {% if user.last_login %} {{ user.last_login.strftime("%Y-%m-%d %H:%M") if user.last_login else "-" }} {% else %} لم يسجل دخول {% endif %}
                </td><td><div class="btn-group btn-group-sm"><button
                      onclick="manageUserPermissions('{{ user.id }}')"
                      class="btn btn-outline-primary"
                      title="إدارة الصلاحيات"
                ><i class="fas fa-key"></i></button><button
                      onclick="viewUserActivity('{{ user.id }}')"
                      class="btn btn-outline-info"
                      title="عرض النشاط"
                ><i class="fas fa-history"></i></button></div></td></tr>
              {% endfor %}
            </tbody></table></div></div></div></div></div><!-- Modal إنشاء دور جديد --><div class="modal fade" id="createRoleModal" tabindex="-1"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title">إنشاء دور جديد</h5><button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
    ></button></div><div class="modal-body"><form id="createRoleForm"><div class="row"><div class="col-md-6"><div class="mb-3"><label for="roleName" class="form-label"
              >اسم الدور (بالإنجليزية)</label><input
                  type="text"
                  class="form-control"
                  id="roleName"
                  required
                /></div></div><div class="col-md-6"><div class="mb-3"><label for="roleDisplayName" class="form-label"
              >اسم الدور (بالعربية)</label><input
                  type="text"
                  class="form-control"
                  id="roleDisplayName"
                  required
                /></div></div></div><div class="mb-3"><label for="roleDescription" class="form-label">وصف الدور</label><textarea
              class="form-control"
              id="roleDescription"
              rows="3"
        ></textarea></div><div class="mb-3"><label class="form-label">الصلاحيات</label><div
              id="permissionsCheckboxes"
              class="border rounded p-3"
              style="max-height: 300px; overflow-y: auto"
        ><!-- سيتم ملؤها بـ JavaScript --></div></div></form></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إلغاء
        </button><button type="button" class="btn btn-primary" onclick="createRole()">
          إنشاء الدور
        </button></div></div></div></div><!-- Modal إدارة صلاحيات المستخدم --><div class="modal fade" id="userPermissionsModal" tabindex="-1"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title">إدارة صلاحيات المستخدم</h5><button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
    ></button></div><div class="modal-body" id="userPermissionsContent"><!-- سيتم ملؤها بـ JavaScript --></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          إلغاء
        </button><button
          type="button"
          class="btn btn-primary"
          onclick="saveUserPermissions()"
    >
          حفظ التغييرات
        </button></div></div></div></div><!-- بيانات الصلاحيات --><script id="permissions-data" type="application/json">
  {
    "all_permissions": {{ all_permissions|tojson }},
    "permissions_by_category": {{ permissions_by_category|tojson }}
  }
</script><script>
  // متغيرات عامة
  let currentUserId = null;
  const permissionsData = JSON.parse(
    document.getElementById("permissions-data").textContent
  );
  let allPermissions = permissionsData.all_permissions;

  // إظهار modal إنشاء دور جديد
  function showCreateRoleModal() {
    // ملء checkboxes الصلاحيات
    const container = document.getElementById("permissionsCheckboxes");
    container.innerHTML = "";

    const permissionsByCategory = permissionsData.permissions_by_category;

    for (const [category, permissions] of Object.entries(
      permissionsByCategory
    ) {
      const categoryDiv = document.createElement("div");
      categoryDiv.className = "mb-3";
      categoryDiv.innerHTML = `
              <h6 class="text-primary">${category}</h6><div class="row">
                  ${Object.entries(permissions)
                    .map(
                      ([name, display]) => `
                      <div class="col-md-6"><div class="form-check"><input class="form-check-input" type="checkbox" value="${name}" id="perm_${name}"><label class="form-check-label" for="perm_${name}">
                                  ${display}
                              </label></div></div>
                  `
                    )
                    .join("")}
              </div>
          `;
      container.appendChild(categoryDiv);
    }

    const modal = new bootstrap.Modal(
      document.getElementById("createRoleModal")
    );
    modal.show();
  }

  // إنشاء دور جديد
  function createRole() {
    const name = document.getElementById("roleName").value;
    const displayName = document.getElementById("roleDisplayName").value;
    const description = document.getElementById("roleDescription").value;

    const selectedPermissions = [];
    document
      .querySelectorAll('#permissionsCheckboxes input[type="checkbox"]:checked')
      .forEach(checkbox) => {
        selectedPermissions.push(checkbox.value);
      });

    if (!name || !displayName) {
      showAlert("error", "يرجى ملء جميع الحقول المطلوبة");
      return;
    }

    fetch("/admin/permissions/roles", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        name: name,
        display_name: displayName,
        description: description,
        permissions: selectedPermissions,
      }),
    })
      .then(response) => response.json()
      .then(data) => {
        if (data.success) {
          showAlert("success", data.message);
          bootstrap.Modal.getInstance(
            document.getElementById("createRoleModal")
          ).hide();
          setTimeout() => location.reload(), 1500);
        } else {
          showAlert("error", data.message);
        }
      })
      .catch(error) => {
        console.error("Error:", error);
        showAlert("error", "حدث خطأ في الاتصال بالخادم");
      });
  }

  // عرض صلاحيات الدور
  function viewRolePermissions(roleId) {
    // سيتم تنفيذها لاحقاً
    showAlert("info", "سيتم إضافة هذه الوظيفة قريباً");
  }

  // تعديل دور
  function editRole(roleId) {
    // سيتم تنفيذها لاحقاً
    showAlert("info", "سيتم إضافة هذه الوظيفة قريباً");
  }

  // حذف دور
  function deleteRole(roleId) {
    if (confirm("هل أنت متأكد من حذف هذا الدور؟") {
      // سيتم تنفيذها لاحقاً
      showAlert("info", "سيتم إضافة هذه الوظيفة قريباً");
    }
  }

  // إدارة صلاحيات المستخدم
  function manageUserPermissions(userId) {
    currentUserId = userId;

    // جلب بيانات المستخدم وصلاحياته
    fetch(`/admin/permissions/users/${userId}`)
      .then(response) => response.json()
      .then(data) => {
        if (data.success) {
          const content = document.getElementById("userPermissionsContent");
          content.innerHTML = `
                  <div class="mb-3"><h6>المستخدم: ${data.user.full_name}</h6><p class="text-muted">الدور الحالي: ${
                        data.user.role_name || "غير محدد"
                      }</p></div><div class="mb-3"><label class="form-label">تغيير الدور</label><select class="form-select" id="userRole"><option value="">اختر دور</option>
                          ${data.available_roles
                            .map(
                              (role) => `
                              <option value="${role.id}" ${
                                role.id === data.user.role_id ? "selected" : ""
                              }>
                                  ${role.display_name}
                              </option>
                          `
                            )
                            .join("")}
                      </select></div><div class="mb-3"><label class="form-label">صلاحيات إضافية</label><div id="userPermissionsCheckboxes" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;"><!-- سيتم ملؤها --></div></div>
              `;

          // ملء checkboxes الصلاحيات
          const permissionsContainer = document.getElementById(
            "userPermissionsCheckboxes"
          );
          const permissionsByCategory = permissionsData.permissions_by_category;

          for (const [category, permissions] of Object.entries(
            permissionsByCategory
          ) {
            const categoryDiv = document.createElement("div");
            categoryDiv.className = "mb-3";
            categoryDiv.innerHTML = `
                      <h6 class="text-primary">${category}</h6><div class="row">
                          ${Object.entries(permissions)
                            .map(
                              ([name, display]) => `
                              <div class="col-md-6"><div class="form-check"><input class="form-check-input" type="checkbox" value="${name}" id="user_perm_${name}"
                                             ${
                                               data.user.permissions.includes(
                                                 name
                                               )
                                                 ? "checked"
                                                 : ""
                                             }><label class="form-check-label" for="user_perm_${name}">
                                          ${display}
                                      </label></div></div>
                          `
                            )
                            .join("")}
                      </div>
                  `;
            permissionsContainer.appendChild(categoryDiv);
          }

          const modal = new bootstrap.Modal(
            document.getElementById("userPermissionsModal")
          );
          modal.show();
        } else {
          showAlert("error", data.message);
        }
      })
      .catch(error) => {
        console.error("Error:", error);
        showAlert("error", "حدث خطأ في جلب بيانات المستخدم");
      });
  }

  // حفظ صلاحيات المستخدم
  function saveUserPermissions() {
    const roleId = document.getElementById("userRole").value;
    const selectedPermissions = [];

    document
      .querySelectorAll(
        '#userPermissionsCheckboxes input[type="checkbox"]:checked'
      )
      .forEach(checkbox) => {
        selectedPermissions.push(checkbox.value);
      });

    fetch(`/admin/permissions/users/${currentUserId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        role_id: roleId || null,
        permissions: selectedPermissions,
      }),
    })
      .then(response) => response.json()
      .then(data) => {
        if (data.success) {
          showAlert("success", data.message);
          bootstrap.Modal.getInstance(
            document.getElementById("userPermissionsModal")
          ).hide();
          setTimeout() => location.reload(), 1500);
        } else {
          showAlert("error", data.message);
        }
      })
      .catch(error) => {
        console.error("Error:", error);
        showAlert("error", "حدث خطأ في حفظ الصلاحيات");
      });
  }

  // عرض نشاط المستخدم
  function viewUserActivity(userId) {
    // سيتم تنفيذها لاحقاً
    showAlert("info", "سيتم إضافة هذه الوظيفة قريباً");
  }

  // دالة عرض التنبيهات
  function showAlert(type, message) {
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${
      type === "error" ? "danger" : type
    } alert-dismissible fade show`;
    alertDiv.innerHTML = `
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;

    const container =
      document.querySelector(".container-fluid") || document.body;
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }
</script>
{% endblock %}
