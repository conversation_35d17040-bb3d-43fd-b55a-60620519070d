{% extends "base.html" %}

{% block title %}الإجازات والاستئذان{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-calendar-alt"></i>
            إدارة الإجازات والاستئذان
          </h3>
          <div class="card-tools">
            <a href="{{ url_for('leaves_add' }}" class="btn btn-primary btn-sm">
              <i class="fas fa-plus"></i>
              طلب إجازة جديد
            </a>
            <a href="{{ url_for('leaves_report' }}" class="btn btn-info btn-sm">
              <i class="fas fa-chart-bar"></i>
              تقرير الإجازات
            </a>
          </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-3">
              <div class="info-box bg-info">
                <span class="info-box-icon"><i class="fas fa-list"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">إجمالي الطلبات</span>
                  <span class="info-box-number">{{ stats.total_requests }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="info-box bg-warning">
                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">في الانتظار</span>
                  <span class="info-box-number">{{ stats.pending_requests }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="info-box bg-success">
                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">موافق عليها</span>
                  <span class="info-box-number">{{ stats.approved_requests }}</span>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="info-box bg-danger">
                <span class="info-box-icon"><i class="fas fa-times"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">مرفوضة</span>
                  <span class="info-box-number">{{ stats.rejected_requests }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- فلاتر البحث -->
          <div class="card card-outline card-primary collapsed-card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-filter"></i>
                فلاتر البحث
              </h3>
              <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
            <div class="card-body">
              <form method="GET" action="{{ url_for('leaves.index' }}">
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="search">البحث</label>
                      <input type="text" class="form-control" id="search" name="search" 
                             value="{{ search }}" placeholder="اسم الموظف...">
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="employee_id">الموظف</label>
                      <select class="form-control" id="employee_id" name="employee_id">
                        <option value="">جميع الموظفين</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" 
                                {% if employee_id == employee.id %}selected{% endif %}>
                          {{ employee.full_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="leave_type">نوع الإجازة</label>
                      <select class="form-control" id="leave_type" name="leave_type">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in leave_types %}
                        <option value="{{ type_code }}" {% if leave_type == type_code %}selected{% endif %}>
                          {{ type_name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="status">الحالة</label>
                      <select class="form-control" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" {% if status == 'pending' %}selected{% endif %}>في الانتظار</option>
                        <option value="approved" {% if status == 'approved' %}selected{% endif %}>موافق عليها</option>
                        <option value="rejected" {% if status == 'rejected' %}selected{% endif %}>مرفوضة</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                      <label for="date_from">من تاريخ</label>
                      <input type="date" class="form-control" id="date_from" name="date_from" 
                             value="{{ date_from }}">
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-search"></i>
                      بحث
                    </button>
                    <a href="{{ url_for('leaves.index' }}" class="btn btn-secondary">
                      <i class="fas fa-undo"></i>
                      إعادة تعيين
                    </a>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- جدول طلبات الإجازة -->
          <div class="table-responsive">
            <table class="table table-bordered table-striped">
              <thead>
                <tr>
                  <th>الموظف</th>
                  <th>نوع الإجازة</th>
                  <th>تاريخ البداية</th>
                  <th>تاريخ النهاية</th>
                  <th>عدد الأيام</th>
                  <th>السبب</th>
                  <th>الحالة</th>
                  <th>تاريخ الطلب</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {% for request in leave_requests %}
                <tr>
                  <td>
                    <strong>{{ request.employee.full_name }}</strong><br>
                    <small class="text-muted">{{ request.employee.employee_number }}</small>
                  </td>
                  <td>
                    {% for type_code, type_name in leave_types %}
                      {% if request.leave_type == type_code %}
                        <span class="badge badge-info">{{ type_name }}</span>
                        
                      {% endif %}
                    {% endfor %}
                  </td>
                  <td>{{ request.start_date.strftime("%Y-%m-%d") if request.start_date else "-" }}</td>
                  <td>{{ request.end_date.strftime("%Y-%m-%d") if request.end_date else "-" }}</td>
                  <td>
                    <strong>{{ request.days_count }} يوم</strong>
                    {% if not request.is_paid %}
                      <br><small class="text-danger">بدون راتب</small>
                    {% endif %}
                  </td>
                  <td>
                    <small>{{ request.reason|truncate(50) }}{% if request.reason|length > 50 %}...{% endif %}</small>
                  </td>
                  <td>
                    {% if request.status == 'pending' %}
                      <span class="badge badge-warning">في الانتظار</span>
                    {% elif request.status == 'approved' %}
                      <span class="badge badge-success">موافق عليها</span>
                    {% elif request.status == 'rejected' %}
                      <span class="badge badge-danger">مرفوضة</span>
                    {% endif %}
                  </td>
                  <td>
                    <small>{{ request.created_at.strftime("%Y-%m-%d") if request.created_at else "-" }}</small>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      {% if request.status == 'pending' %}
                        <button type="button" class="btn btn-success btn-sm"
                                onclick="approveLeave('{{ request.id }}')" title="موافقة">
                          <i class="fas fa-check"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-sm"
                                onclick="rejectLeave('{{ request.id }}')" title="رفض">
                          <i class="fas fa-times"></i>
                        </button>
                      {% endif %}
                      <button type="button" class="btn btn-info btn-sm"
                              onclick="viewLeave('{{ request.id }}')" title="عرض">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button type="button" class="btn btn-danger btn-sm"
                              onclick="deleteLeave('{{ request.id }}')" title="حذف">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                {% else %}
                <tr>
                  <td colspan="9" class="text-center text-muted">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    لا توجد طلبات إجازة
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة عرض تفاصيل الطلب -->
<div class="modal fade" id="viewLeaveModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">تفاصيل طلب الإجازة</h4>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body" id="leaveDetails">
        <!-- سيتم تحميل التفاصيل هنا -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<!-- نافذة الموافقة/الرفض -->
<div class="modal fade" id="approvalModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="approvalModalTitle">موافقة على الطلب</h4>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <form id="approvalForm">
        <div class="modal-body">
          <input type="hidden" id="leave_id" name="leave_id">
          <input type="hidden" id="approval_action" name="approval_action">
          <div class="form-group">
            <label for="approval_notes">ملاحظات</label>
            <textarea class="form-control" id="approval_notes" name="approval_notes" 
                      rows="3" placeholder="أدخل ملاحظات الموافقة أو الرفض..."></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
          <button type="submit" class="btn btn-primary" id="approvalSubmitBtn">تأكيد</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
// عرض تفاصيل الطلب
function viewLeave(id) {
  // هنا يمكن إضافة استدعاء AJAX لجلب تفاصيل الطلب
  $('#viewLeaveModal').modal('show');
}

// الموافقة على الطلب
function approveLeave(id) {
  document.getElementById('leave_id').value = id;
  document.getElementById('approval_action').value = 'approve';
  document.getElementById('approvalModalTitle').textContent = 'الموافقة على طلب الإجازة';
  document.getElementById('approvalSubmitBtn').textContent = 'موافقة';
  document.getElementById('approvalSubmitBtn').className = 'btn btn-success';
  document.getElementById('approval_notes').placeholder = 'ملاحظات الموافقة (اختيارية)...';
  $('#approvalModal').modal('show');
}

// رفض الطلب
function rejectLeave(id) {
  document.getElementById('leave_id').value = id;
  document.getElementById('approval_action').value = 'reject';
  document.getElementById('approvalModalTitle').textContent = 'رفض طلب الإجازة';
  document.getElementById('approvalSubmitBtn').textContent = 'رفض';
  document.getElementById('approvalSubmitBtn').className = 'btn btn-danger';
  document.getElementById('approval_notes').placeholder = 'سبب الرفض (مطلوب)...';
  $('#approvalModal').modal('show');
}

// حذف الطلب
function deleteLeave(id) {
  if (confirm('هل أنت متأكد من حذف طلب الإجازة؟') {
    fetch(`/leaves/${id}/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json()
    .then(data => {
      if (data.success) {
        location.reload();
      } else {
        alert('خطأ: ' + data.message);
      }
    })
    .catch(error => {
      alert('حدث خطأ في الاتصال');
    });
  }
}

// إرسال نموذج الموافقة/الرفض
document.getElementById('approvalForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const leaveId = formData.get('leave_id');
  const action = formData.get('approval_action');
  const notes = formData.get('approval_notes');
  
  // التحقق من وجود ملاحظات في حالة الرفض
  if (action === 'reject' && !notes.trim() {
    alert('سبب الرفض مطلوب');
    return;
  }
  
  const url = action === 'approve' ? `/leaves/${leaveId}/approve` : `/leaves/${leaveId}/reject`;
  
  fetch(url, {
    method: 'POST',
    body: formData
  })
  .then(response => response.json()
  .then(data => {
    if (data.success) {
      $('#approvalModal').modal('hide');
      location.reload();
    } else {
      alert('خطأ: ' + data.message);
    }
  })
  .catch(error => {
    alert('حدث خطأ في الاتصال');
  });
});
</script>
{% endblock %}
