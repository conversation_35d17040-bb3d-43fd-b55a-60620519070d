#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح فوري وشامل لجميع أخطاء بناء الجملة
"""

import os
import re
import glob

def emergency_fix_all_syntax():
    """إصلاح فوري لجميع أخطاء بناء الجملة"""
    
    print("🚨 بدء الإصلاح الفوري لجميع أخطاء بناء الجملة...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # إصلاحات فورية شاملة
            fixes = [
                # إصلاح الأقواس الإضافية
                (r'\{\{\s*([^}]+?)\s*\)\s*\}\}', r'{{ \1 }}'),
                (r'\{\{\s*([^}]+?)\(\s*([^}]*?)\s*\)\s*\)\s*\}\}', r'{{ \1(\2) }}'),
                
                # إصلاح التعبيرات الشرطية المقسمة
                (r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s*\n\s*([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}'),
                (r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}'),
                
                # إصلاح المسافات الإضافية
                (r'\{\{\s*([^}]+?)\s{2,}\}\}', r'{{ \1 }}'),
                (r'\{\%\s*([^%]+?)\s{2,}\%\}', r'{% \1 %}'),
                
                # إصلاح الأسطر المقسمة
                (r'\{\{\s*([^}]+?)\n\s*([^}]+?)\s*\}\}', r'{{ \1 \2 }}'),
                (r'\{\%\s*([^%]+?)\n\s*([^%]+?)\s*\%\}', r'{% \1 \2 %}'),
                
                # إصلاح الأقواس المضاعفة
                (r'\{\{\s*\{\{', r'{{'),
                (r'\}\}\s*\}\}', r'}}'),
                (r'\{\%\s*\{\%', r'{%'),
                (r'\%\}\s*\%\}', r'%}'),
                
                # إصلاح علامات Django
                (r'\{\%\s*url\s+([^%]+?)\s*\%\}', r'{{ url_for(\1) }}'),
                
                # إصلاح الفلاتر
                (r'\|date:', r'|strftime('),
                (r'\|floatformat:', r'|round('),
                (r'\|default:', r'|default('),
                (r'\|truncatechars:', r'|truncate('),
                
                # إصلاح format مضاعف
                (r'format\(format\(([^)]+)\)\)', r'format(\1)'),
                
                # إصلاح الدوال المقسمة
                (r'\.strftime\(\s*([^)]+?)\s*\)\s+if\s+([^}]+?)\s+else\s+([^}]+?)', r'.strftime(\1) if \2 else \3'),
            ]
            
            for pattern, replacement in fixes:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                if matches:
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
                    file_fixes += len(matches)
            
            # إصلاحات نصية مباشرة
            text_fixes = [
                # dashboard.html
                ('case.created_at else\n                "-"', 'case.created_at else "-"'),
                ('case.created_at else "-")', 'case.created_at else "-"'),
                ('case.client.full_name  }}', 'case.client.full_name }}'),
                ('case.lawyer  %}', 'case.lawyer %}'),
                ('case.lawyer.user.full_name   }}', 'case.lawyer.user.full_name }}'),
                ('endif  %}', 'endif %}'),
                
                # إصلاحات عامة
                ('if user.email else\n                user.username', 'if user.email else user.username'),
                ('if user.full_name else\n                user.username', 'if user.full_name else user.username'),
                ('if employee.user.email else\n                employee.user.username', 'if employee.user.email else employee.user.username'),
                
                # إصلاح الأقواس
                ('))', ')'),
                ('((', '('),
                ('  }}', ' }}'),
                ('{{  ', '{{ '),
                ('  %}', ' %}'),
                ('{%  ', '{% '),
                
                # إصلاح علامات مختلطة
                ('{% url ', '{{ url_for('),
                (' %}"', ') }}"'),
                ('{% widthratio', '{{ ('),
                (' %} %', ') }} %'),
            ]
            
            for old, new in text_fixes:
                if old in content:
                    content = content.replace(old, new)
                    file_fixes += 1
            
            # حفظ الملف إذا تم إجراء إصلاحات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  🔧 {file_path}: تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
            
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} خطأ إجمالي")
    return total_fixes

def validate_critical_files():
    """التحقق من الملفات الحرجة"""
    
    print("\n🔍 التحقق من الملفات الحرجة...")
    
    critical_files = [
        'templates/base.html',
        'templates/dashboard.html',
        'templates/login.html',
        'templates/admin/index.html',
        'templates/cases/index.html',
        'templates/clients/index.html',
        'templates/employees/index.html',
    ]
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        valid_count = 0
        error_count = 0
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                try:
                    relative_path = os.path.relpath(file_path, 'templates').replace('\\', '/')
                    template = env.get_template(relative_path)
                    print(f"  ✅ {relative_path}: صحيح")
                    valid_count += 1
                    
                except TemplateSyntaxError as e:
                    print(f"  ❌ {relative_path}: {str(e)[:100]}...")
                    error_count += 1
                except Exception as e:
                    print(f"  ⚠️ {relative_path}: {str(e)[:100]}...")
                    error_count += 1
        
        print(f"\n📊 الملفات الحرجة:")
        print(f"  ✅ صحيحة: {valid_count}")
        print(f"  ❌ بها أخطاء: {error_count}")
        
        return error_count == 0
        
    except ImportError:
        print("❌ لا يمكن التحقق من بناء الجملة")
        return False

def fix_dashboard_specifically():
    """إصلاح dashboard.html بشكل محدد"""
    
    print("\n🎯 إصلاح dashboard.html بشكل محدد...")
    
    dashboard_file = 'templates/dashboard.html'
    if not os.path.exists(dashboard_file):
        print("❌ dashboard.html غير موجود")
        return False
    
    try:
        with open(dashboard_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # إصلاحات محددة لـ dashboard
        specific_fixes = [
            # إصلاح السطر 331-333
            ('{{ case.created_at.strftime("%Y-%m-%d") if case.created_at else\n                "-" }}', 
             '{{ case.created_at.strftime("%Y-%m-%d") if case.created_at else "-" }}'),
            
            # إصلاح السطر 326
            ('{{ case.client.full_name }} {% if case.lawyer %} • <i class="fas fa-user-tie me-1"></i>{{ case.lawyer.user.full_name }}{% endif %}',
             '{{ case.client.full_name }}{% if case.lawyer %} • <i class="fas fa-user-tie me-1"></i>{{ case.lawyer.user.full_name }}{% endif %}'),
            
            # إصلاحات إضافية
            ('case.created_at else "-")', 'case.created_at else "-"'),
            ('format(format(', 'format('),
            ('))', ')'),
        ]
        
        fixes_count = 0
        for old, new in specific_fixes:
            if old in content:
                content = content.replace(old, new)
                fixes_count += 1
        
        if content != original_content:
            with open(dashboard_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ تم إصلاح {fixes_count} مشكلة في dashboard.html")
            return True
        else:
            print("ℹ️ dashboard.html لا يحتاج إصلاح")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح dashboard.html: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح الفوري"""
    
    print("🚨 بدء الإصلاح الفوري الشامل...")
    
    # إصلاح dashboard أولاً
    dashboard_fixed = fix_dashboard_specifically()
    
    # إصلاح شامل لجميع الملفات
    total_fixes = emergency_fix_all_syntax()
    
    # التحقق من الملفات الحرجة
    critical_valid = validate_critical_files()
    
    print("\n" + "="*50)
    print("🚨 تقرير الإصلاح الفوري")
    print("="*50)
    print(f"🎯 dashboard.html: {'✅ مُصلح' if dashboard_fixed else '❌ يحتاج مراجعة'}")
    print(f"🔧 إجمالي الإصلاحات: {total_fixes}")
    print(f"📁 الملفات الحرجة: {'✅ صحيحة' if critical_valid else '❌ بها أخطاء'}")
    
    if dashboard_fixed and critical_valid:
        print("\n🎉 تم الإصلاح الفوري بنجاح!")
        print("✅ النظام جاهز للاستخدام")
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج مراجعة")
    
    print("="*50)
    
    return dashboard_fixed and critical_valid

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
