{% extends "base.html" %} {% block title %}تقارير العقود - نظام الشؤون
القانونية{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
><h1 class="h2"><i class="fas fa-chart-bar me-2"></i>
    تقارير العقود
  </h1><div class="btn-toolbar mb-2 mb-md-0"><div class="btn-group me-2"><button onclick="exportToPDF()" class="btn btn-success"><i class="fas fa-file-pdf me-1"></i>
        تصدير PDF
      </button><button onclick="exportToExcel()" class="btn btn-info"><i class="fas fa-file-excel me-1"></i>
        تصدير Excel
      </button></div><a
      href="{{ url_for('contracts.index' }}"
      class="btn btn-outline-secondary"
  ><i class="fas fa-arrow-right me-1"></i>
      العودة للعقود
    </a></div></div><!-- إحصائيات عامة --><div class="row mb-4"><div class="col-md-3"><div class="card text-white bg-primary"><div class="card-body"><div class="d-flex justify-content-between"><div><h4 class="card-title">{{ stats.total }}</h4><p class="card-text">إجمالي العقود</p></div><div class="align-self-center"><i class="fas fa-file-contract fa-2x"></i></div></div></div></div></div><div class="col-md-3"><div class="card text-white bg-success"><div class="card-body"><div class="d-flex justify-content-between"><div><h4 class="card-title">
              {{ "{:,.0f}".format(stats.financial.total_value ) }}
            </h4><p class="card-text">إجمالي القيمة (ريال)</p></div><div class="align-self-center"><i class="fas fa-money-bill fa-2x"></i></div></div></div></div></div><div class="col-md-3"><div class="card text-white bg-info"><div class="card-body"><div class="d-flex justify-content-between"><div><h4 class="card-title">
              {{ "{:,.0f}".format(stats.financial.active_value ) }}
            </h4><p class="card-text">قيمة العقود النشطة</p></div><div class="align-self-center"><i class="fas fa-chart-line fa-2x"></i></div></div></div></div></div><div class="col-md-3"><div class="card text-white bg-warning"><div class="card-body"><div class="d-flex justify-content-between"><div><h4 class="card-title">
              {{ "{:,.0f}".format(stats.financial.average_value ) }}
            </h4><p class="card-text">متوسط قيمة العقد</p></div><div class="align-self-center"><i class="fas fa-calculator fa-2x"></i></div></div></div></div></div></div><div class="row"><!-- إحصائيات حسب الحالة --><div class="col-md-6"><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>
          توزيع العقود حسب الحالة
        </h5></div><div class="card-body"><canvas id="statusChart" width="400" height="200"></canvas><div class="mt-3"><div class="row">
            {% for status, count in stats.by_status %}
            <div class="col-6 mb-2"><div class="d-flex justify-content-between"><span>
                  {% if status == 'draft' %}
                  <i class="fas fa-circle text-secondary me-1"></i>مسودة {% elif status == 'active' %}
                  <i class="fas fa-circle text-success me-1"></i>نشط {% elif status == 'completed' %}
                  <i class="fas fa-circle text-primary me-1"></i>مكتمل {% elif status == 'cancelled' %}
                  <i class="fas fa-circle text-danger me-1"></i>ملغي {% elif status == 'expired' %}
                  <i class="fas fa-circle text-warning me-1"></i>منتهي {% else %} <i class="fas fa-circle text-muted me-1"></i>{{ status }}
                  {% endif %}
                </span><strong>{{ count }}</strong></div></div>
            {% endfor %}
          </div></div></div></div></div><!-- إحصائيات حسب النوع --><div class="col-md-6"><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>
          توزيع العقود حسب النوع
        </h5></div><div class="card-body"><canvas id="typeChart" width="400" height="200"></canvas><div class="mt-3">
          {% for type, count in stats.by_type %}
          <div class="d-flex justify-content-between mb-2"><span>{{ type }}</span><strong>{{ count }}</strong></div>
          {% endfor %}
        </div></div></div></div></div><!-- تقرير مفصل --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-table me-2"></i>
      التقرير المفصل
    </h5></div><div class="card-body"><div class="table-responsive"><table class="table table-striped" id="detailedReport"><thead><tr><th>المؤشر</th><th>القيمة</th><th>النسبة المئوية</th><th>الوصف</th></tr></thead><tbody><tr><td><strong>إجمالي العقود</strong></td><td>{{ stats.total }}</td><td>100%</td><td>العدد الكلي للعقود في النظام</td></tr>
          {% for status, count in stats.by_status %}
          <tr><td>
              {% if status == 'draft' %}مسودات {% elif status == 'active' %}عقود
              نشطة {% elif status == 'completed' %}عقود مكتملة {% elif status == 'cancelled' %}عقود ملغية {% elif status == 'expired' %}عقود منتهية
              {% else %}{{ status }} {% endif %}
            </td><td>{{ count }}</td><td>
              {{ "%.1f".format(count / stats.total * 100) if stats.total > 0 else 0 }}%
            </td><td>
              {% if status == 'draft' %}العقود في مرحلة الإعداد {% elif status == 'active' %}العقود السارية حالياً {% elif status == 'completed' %}العقود المنجزة بنجاح {% elif status == 'cancelled' %}العقود
              الملغاة {% elif status == 'expired' %}العقود منتهية الصلاحية {% else %}{{ status }} {% endif %}
            </td></tr>
          {% endfor %}
          <tr class="table-info"><td><strong>إجمالي القيمة المالية</strong></td><td><strong>{{ "{:,.2f}".format(stats.financial.total_value ) }} ريال</strong></td><td>-</td><td>مجموع قيم جميع العقود</td></tr><tr class="table-success"><td><strong>قيمة العقود النشطة</strong></td><td><strong>{{ "{:,.2f}".format(stats.financial.active_value ) }} ريال</strong></td><td>
              {{ "%.1f".format(stats.financial.active_value / stats.financial.total_value * 100) if stats.financial.total_value > 0 else 0 }}%
            </td><td>قيمة العقود السارية حالياً</td></tr><tr><td><strong>متوسط قيمة العقد</strong></td><td>{{ "{:,.2f}".format(stats.financial.average_value ) }} ريال</td><td>-</td><td>متوسط قيمة العقد الواحد</td></tr></tbody></table></div></div></div><!-- معلومات إضافية --><div class="row"><div class="col-md-12"><div class="card"><div class="card-header"><h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>
          معلومات التقرير
        </h5></div><div class="card-body"><div class="row"><div class="col-md-6"><p><strong>تاريخ إنشاء التقرير:</strong><span id="reportDate"></span></p><p><strong>المستخدم:</strong> {{ current_user.full_name }}</p></div><div class="col-md-6"><p><strong>نطاق البيانات:</strong> جميع العقود في النظام</p><p><strong>آخر تحديث:</strong><span id="lastUpdate"></span></p></div></div></div></div></div></div><!-- تضمين Chart.js --><script src="https://cdn.jsdelivr.net/npm/chart.js"></script><!-- بيانات الرسوم البيانية --><script id="chart-data" type="application/json">
  {
    "statusData": {
      "labels": [
        {% for status in stats.by_status.keys() %}
        "{% if status == 'draft' %}مسودة{% elif status == 'active' %}نشط{% elif status == 'completed' %}مكتمل{% elif status == 'cancelled' %}ملغي{% elif status == 'expired' %}منتهي{% else %}{{ status }}{% endif) }}"{% if not loop.last %},{% endif %}
        {% endfor %}
      ],
      "values": [{{ stats.by_status.values()|join(', ' }}]
    },
    "typeData": {
      "labels": [
        {% for type in stats.by_type.keys() %}
        "{{ type }}"{% if not loop.last %},{% endif %}
        {% endfor %}
      ],
      "values": [{{ stats.by_type.values()|join(', ' }}]
    }
  }
</script><script>
  // قراءة البيانات
  const chartDataElement = document.getElementById("chart-data");
  const chartData = JSON.parse(chartDataElement.textContent);

  // رسم بياني للحالات
  const statusCtx = document.getElementById("statusChart").getContext("2d");
  const statusData = {
    labels: chartData.statusData.labels,
    datasets: [
      {
        data: chartData.statusData.values,
        backgroundColor: [
          "#6c757d", // مسودة - رمادي
          "#28a745", // نشط - أخضر
          "#007bff", // مكتمل - أزرق
          "#dc3545", // ملغي - أحمر
          "#ffc107", // منتهي - أصفر
        ],
        borderWidth: 2,
        borderColor: "#fff",
      },
    ],
  };

  new Chart(statusCtx, {
    type: "doughnut",
    data: statusData,
    options: {
      responsive: true,
      plugins: {
        legend: {
          display: false,
        },
      },
    },
  });

  // رسم بياني للأنواع
  const typeCtx = document.getElementById("typeChart").getContext("2d");
  const typeData = {
    labels: chartData.typeData.labels,
    datasets: [
      {
        label: "عدد العقود",
        data: chartData.typeData.values,
        backgroundColor: [
          "#007bff",
          "#28a745",
          "#ffc107",
          "#dc3545",
          "#6f42c1",
          "#fd7e14",
        ],
        borderWidth: 1,
      },
    ],
  };

  new Chart(typeCtx, {
    type: "bar",
    data: typeData,
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1,
          },
        },
      },
      plugins: {
        legend: {
          display: false,
        },
      },
    },
  });

  // وظائف التصدير
  function exportToPDF() {
    // سيتم تنفيذها لاحقاً
    alert("سيتم إضافة وظيفة تصدير PDF قريباً");
  }

  function exportToExcel() {
    // تصدير الجدول إلى Excel
    const table = document.getElementById("detailedReport");
    const wb = XLSX.utils.table_to_book(table, { sheet: "تقرير العقود" });
    XLSX.writeFile(
      wb,
      `تقرير_العقود_${new Date().toISOString().split("T")[0]}.xlsx`
    );
  }

  // طباعة التقرير
  function printReport() {
    window.print();
  }

  // إضافة أزرار إضافية
  document.addEventListener("DOMContentLoaded", function () {
    // تعيين التاريخ والوقت الحالي
    const now = new Date();
    const dateString = now.toLocaleString("ar-SA", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });

    document.getElementById("reportDate").textContent = dateString;
    document.getElementById("lastUpdate").textContent = dateString;
  });
</script><!-- تضمين SheetJS للتصدير إلى Excel --><script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script><style>
  @media print {
    .btn-toolbar,
    .no-print {
      display: none !important;
    }

    .card {
      border: 1px solid #ddd !important;
      box-shadow: none !important;
    }

    .card-header {
      background-color: #f8f9fa !important;
      color: #000 !important;
    }
  }
</style>
{% endblock %}
