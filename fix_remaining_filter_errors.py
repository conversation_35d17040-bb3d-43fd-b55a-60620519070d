#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح الأخطاء المتبقية في فلاتر Jinja2
"""

import os
import re
import glob

def fix_remaining_filter_errors():
    """إصلاح الأخطاء المتبقية في فلاتر Jinja2"""
    
    print("🔧 إصلاح الأخطاء المتبقية في فلاتر Jinja2...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # إصلاح الأنماط المختلفة للفلاتر الخاطئة
            patterns = [
                # {{ variable|filter.attribute }} → {{ variable.attribute|filter }}
                (r'\{\{\s*([^|}]+)\|([^.}]+)\.([^}]+)\s*\}\}', r'{{ \1.\3|\2 }}'),
                
                # {{ variable|strftime.format }} → {{ variable|strftime(format) }}
                (r'\{\{\s*([^|}]+)\|strftime\.([^}]+)\s*\}\}', r'{{ \1|strftime(\2) }}'),
                
                # إصلاح فلاتر التاريخ
                (r'\{\{\s*([^|}]+)\|strftime\("([^"]+)"\)\s*\}\}', r'{{ \1.strftime("\2") if \1 else "-" }}'),
                
                # إصلاح فلاتر الأرقام
                (r'\{\{\s*([^|}]+)\|round\.(\d+)\s*\}\}', r'{{ \1|round(\2) }}'),
                
                # إصلاح فلاتر النصوص
                (r'\{\{\s*([^|}]+)\|truncate\.(\d+)\s*\}\}', r'{{ \1|truncate(\2) }}'),
                
                # إصلاح فلاتر القوائم
                (r'\{\{\s*([^|}]+)\|length\.count\s*\}\}', r'{{ \1|length }}'),
                
                # إصلاح فلاتر مخصصة
                (r'\{\{\s*([^|}]+)\|format\.([^}]+)\s*\}\}', r'{{ \2.format(\1) }}'),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            # حفظ الملف إذا تم إجراء تغييرات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
            
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"\n🎉 تم إصلاح {total_fixes} خطأ إضافي")
    return total_fixes

def fix_specific_date_filters():
    """إصلاح فلاتر التاريخ المحددة"""
    
    print("\n🔧 إصلاح فلاتر التاريخ...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # إصلاح فلاتر التاريخ الشائعة
            date_patterns = [
                # {{ date|strftime("%Y-%m-%d") }} - صحيح
                # {{ date.strftime|format }} → {{ date.strftime("%Y-%m-%d") if date else "-" }}
                (r'\{\{\s*([^|}]+)\.strftime\|([^}]+)\s*\}\}', r'{{ \1.strftime("%Y-%m-%d") if \1 else "-" }}'),
                
                # {{ date|date.format }} → {{ date.strftime("%Y-%m-%d") if date else "-" }}
                (r'\{\{\s*([^|}]+)\|date\.([^}]+)\s*\}\}', r'{{ \1.strftime("%Y-%m-%d") if \1 else "-" }}'),
                
                # إصلاح فلاتر الوقت
                (r'\{\{\s*([^|}]+)\|time\.([^}]+)\s*\}\}', r'{{ \1.strftime("%H:%M") if \1 else "-" }}'),
                
                # إصلاح فلاتر التاريخ والوقت
                (r'\{\{\s*([^|}]+)\|datetime\.([^}]+)\s*\}\}', r'{{ \1.strftime("%Y-%m-%d %H:%M") if \1 else "-" }}'),
            ]
            
            for pattern, replacement in date_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} فلتر تاريخ")
                total_fixes += file_fixes
                
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"✅ تم إصلاح {total_fixes} فلتر تاريخ")
    return total_fixes

def fix_number_filters():
    """إصلاح فلاتر الأرقام"""
    
    print("\n🔧 إصلاح فلاتر الأرقام...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # إصلاح فلاتر الأرقام
            number_patterns = [
                # {{ number|format.currency }} → {{ "{:,.2f}".format(number) }}
                (r'\{\{\s*([^|}]+)\|format\.currency\s*\}\}', r'{{ "{:,.2f}".format(\1) }}'),
                
                # {{ number|format.decimal }} → {{ "{:,.2f}".format(number) }}
                (r'\{\{\s*([^|}]+)\|format\.decimal\s*\}\}', r'{{ "{:,.2f}".format(\1) }}'),
                
                # {{ number|format.integer }} → {{ "{:,}".format(number) }}
                (r'\{\{\s*([^|}]+)\|format\.integer\s*\}\}', r'{{ "{:,}".format(\1) }}'),
                
                # {{ number|format.percentage }} → {{ "{:.1f}%".format(number) }}
                (r'\{\{\s*([^|}]+)\|format\.percentage\s*\}\}', r'{{ "{:.1f}%".format(\1) }}'),
            ]
            
            for pattern, replacement in number_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    file_fixes += len(matches)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} فلتر رقم")
                total_fixes += file_fixes
                
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"✅ تم إصلاح {total_fixes} فلتر رقم")
    return total_fixes

def final_validation():
    """التحقق النهائي من صحة الفلاتر"""
    
    print("\n🔍 التحقق النهائي من صحة الفلاتر...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    errors = []
    warnings = []
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن أخطاء متبقية
            error_patterns = [
                (r'\{\{\s*[^}]*\|[^}]*\.[^}]*\s*\}\}', 'فلتر خاطئ - ترتيب خاطئ'),
                (r'\{\{\s*"[^"]+"\|[^}]+\s*\}\}', 'فلتر خاطئ - استخدم .format()'),
            ]
            
            for pattern, error_type in error_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    errors.append(f"{file_path}: {error_type} ({len(matches)} مرة)")
            
            # البحث عن تحذيرات
            warning_patterns = [
                (r'\{\{\s*[^}]*\.strftime\([^}]*\)\s*\}\}', 'استخدام strftime مباشر'),
                (r'\{\{\s*[^}]*\.format\([^}]*\)\s*\}\}', 'استخدام format مباشر'),
            ]
            
            for pattern, warning_type in warning_patterns:
                matches = re.findall(pattern, content)
                if matches and len(matches) > 5:  # فقط إذا كان كثير
                    warnings.append(f"{file_path}: {warning_type} ({len(matches)} مرة)")
        
        except Exception as e:
            errors.append(f"{file_path}: خطأ في القراءة - {e}")
    
    if errors:
        print("❌ أخطاء متبقية:")
        for error in errors[:10]:  # أول 10 أخطاء فقط
            print(f"  - {error}")
        if len(errors) > 10:
            print(f"  ... و {len(errors) - 10} خطأ آخر")
        return False
    else:
        print("✅ جميع فلاتر Jinja2 صحيحة!")
        
        if warnings:
            print("\nℹ️ تحذيرات (غير مؤثرة):")
            for warning in warnings[:5]:
                print(f"  - {warning}")
        
        return True

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إصلاح الأخطاء المتبقية...")
    
    # إصلاح الأخطاء المتبقية
    remaining_fixes = fix_remaining_filter_errors()
    
    # إصلاح فلاتر التاريخ
    date_fixes = fix_specific_date_filters()
    
    # إصلاح فلاتر الأرقام
    number_fixes = fix_number_filters()
    
    # التحقق النهائي
    is_valid = final_validation()
    
    total_fixes = remaining_fixes + date_fixes + number_fixes
    
    print("\n" + "="*50)
    print(f"🎉 تم إصلاح {total_fixes} خطأ إضافي")
    if is_valid:
        print("✅ جميع فلاتر Jinja2 تعمل بشكل صحيح الآن")
    else:
        print("⚠️ لا تزال هناك بعض الأخطاء البسيطة")
    print("="*50)

if __name__ == "__main__":
    main()
