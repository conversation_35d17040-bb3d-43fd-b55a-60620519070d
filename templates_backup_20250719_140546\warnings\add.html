{% extends "base.html" %}

{% block title %}إضافة إنذار جديد{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-plus"></i>
            إضافة إنذار جديد
          </h3>
          <div class="card-tools">
            <a href="{{ url_for('warnings.index' }}" class="btn btn-secondary btn-sm">
              <i class="fas fa-arrow-left"></i>
              العودة للقائمة
            </a>
          </div>
        </div>

        <form id="warningForm" enctype="multipart/form-data">
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="employee_id">الموظف <span class="text-danger">*</span></label>
                  <select class="form-control" id="employee_id" name="employee_id" required>
                    <option value="">اختر الموظف</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}">
                      {{ employee.full_name }} - {{ employee.employee_number }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="warning_type">نوع الإنذار <span class="text-danger">*</span></label>
                  <select class="form-control" id="warning_type" name="warning_type" required>
                    <option value="">اختر نوع الإنذار</option>
                    {% for type_code, type_name in warning_types %}
                    <option value="{{ type_code }}">{{ type_name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="category">الفئة <span class="text-danger">*</span></label>
                  <select class="form-control" id="category" name="category" required>
                    <option value="">اختر الفئة</option>
                    {% for cat_code, cat_name in categories %}
                    <option value="{{ cat_code }}">{{ cat_name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="severity">مستوى الخطورة</label>
                  <select class="form-control" id="severity" name="severity">
                    {% for sev_code, sev_name in severities %}
                    <option value="{{ sev_code }}" {% if sev_code == 'medium' %}selected{% endif %}>
                      {{ sev_name }}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="title">عنوان الإنذار <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" id="title" name="title" 
                         placeholder="عنوان مختصر للإنذار..." required>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="description">وصف الإنذار <span class="text-danger">*</span></label>
                  <textarea class="form-control" id="description" name="description" rows="4" 
                            placeholder="وصف تفصيلي للمخالفة أو السلوك المؤدي للإنذار..." required></textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="incident_date">تاريخ الحادثة <span class="text-danger">*</span></label>
                  <input type="date" class="form-control" id="incident_date" name="incident_date" 
                         value="{{ today }}" required>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="deadline">الموعد النهائي للتصحيح</label>
                  <input type="date" class="form-control" id="deadline" name="deadline">
                  <small class="form-text text-muted">إذا كان هناك إجراء مطلوب</small>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="follow_up_date">تاريخ المتابعة</label>
                  <input type="date" class="form-control" id="follow_up_date" name="follow_up_date">
                  <small class="form-text text-muted">تاريخ مراجعة الإنذار</small>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label for="action_required">الإجراء المطلوب</label>
                  <textarea class="form-control" id="action_required" name="action_required" rows="3" 
                            placeholder="الإجراءات التصحيحية المطلوبة من الموظف..."></textarea>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="attachment">مرفق (اختياري)</label>
                  <input type="file" class="form-control-file" id="attachment" name="attachment" 
                         accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                  <small class="form-text text-muted">
                    الملفات المسموحة: PDF, DOC, DOCX, JPG, PNG (حد أقصى 5 ميجابايت)
                  </small>
                </div>
              </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="row">
              <div class="col-12">
                <div class="card card-outline card-warning">
                  <div class="card-header">
                    <h3 class="card-title">إرشادات مهمة</h3>
                  </div>
                  <div class="card-body">
                    <ul class="list-unstyled">
                      <li><i class="fas fa-info-circle text-warning"></i> تأكد من دقة المعلومات قبل إصدار الإنذار</li>
                      <li><i class="fas fa-info-circle text-warning"></i> الإنذار الشفهي يسبق الإنذار الكتابي عادة</li>
                      <li><i class="fas fa-info-circle text-warning"></i> الإنذار النهائي يعني أن أي مخالفة أخرى قد تؤدي للفصل</li>
                      <li><i class="fas fa-info-circle text-warning"></i> يجب توثيق جميع الإنذارات في ملف الموظف</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card-footer">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i>
              إصدار الإنذار
            </button>
            <a href="{{ url_for('warnings.index' }}" class="btn btn-secondary">
              <i class="fas fa-times"></i>
              إلغاء
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
// تحديث الموعد النهائي تلقائياً حسب نوع الإنذار
document.getElementById('warning_type').addEventListener('change', function() {
  const warningType = this.value;
  const incidentDate = document.getElementById('incident_date').value;
  
  if (incidentDate && warningType) {
    const incident = new Date(incidentDate);
    let deadlineDays = 0;
    
    // تحديد عدد الأيام حسب نوع الإنذار
    switch(warningType) {
      case 'verbal':
        deadlineDays = 7; // أسبوع للإنذار الشفهي
        break;
      case 'written':
        deadlineDays = 14; // أسبوعين للإنذار الكتابي
        break;
      case 'final':
        deadlineDays = 30; // شهر للإنذار النهائي
        break;
    }
    
    if (deadlineDays > 0) {
      const deadline = new Date(incident);
      deadline.setDate(deadline.getDate() + deadlineDays);
      document.getElementById('deadline').value = deadline.toISOString().split('T')[0];
      
      // تاريخ المتابعة (نصف المدة)
      const followUp = new Date(incident);
      followUp.setDate(followUp.getDate() + Math.floor(deadlineDays / 2);
      document.getElementById('follow_up_date').value = followUp.toISOString().split('T')[0];
    }
  }
});

// تحديث مستوى الخطورة حسب الفئة
document.getElementById('category').addEventListener('change', function() {
  const category = this.value;
  const severitySelect = document.getElementById('severity');
  
  // تحديد مستوى الخطورة الافتراضي حسب الفئة
  switch(category) {
    case 'safety':
      severitySelect.value = 'high';
      break;
    case 'attendance':
      severitySelect.value = 'medium';
      break;
    case 'performance':
      severitySelect.value = 'medium';
      break;
    case 'conduct':
      severitySelect.value = 'high';
      break;
    case 'policy':
      severitySelect.value = 'medium';
      break;
    default:
      severitySelect.value = 'medium';
  }
});

// التحقق من حجم الملف
document.getElementById('attachment').addEventListener('change', function() {
  const file = this.files[0];
  if (file) {
    const maxSize = 5 * 1024 * 1024; // 5 ميجابايت
    if (file.size > maxSize) {
      alert('حجم الملف يجب أن يكون أقل من 5 ميجابايت');
      this.value = '';
    }
  }
});

// إرسال النموذج
document.getElementById('warningForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  // التحقق من صحة التواريخ
  const incidentDate = new Date(document.getElementById('incident_date').value);
  const deadline = document.getElementById('deadline').value;
  const followUpDate = document.getElementById('follow_up_date').value;
  
  if (deadline) {
    const deadlineDate = new Date(deadline);
    if (deadlineDate <= incidentDate) {
      alert('الموعد النهائي يجب أن يكون بعد تاريخ الحادثة');
      return;
    }
  }
  
  if (followUpDate) {
    const followUpDateObj = new Date(followUpDate);
    if (followUpDateObj <= incidentDate) {
      alert('تاريخ المتابعة يجب أن يكون بعد تاريخ الحادثة');
      return;
    }
  }
  
  const formData = new FormData(this);
  
  // إظهار مؤشر التحميل
  const submitBtn = this.querySelector('button[type="submit"]');
  const originalText = submitBtn.innerHTML;
  submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
  submitBtn.disabled = true;
  
  fetch('{{ url_for("warnings:add" %}', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json()
  .then(data => {
    if (data.success) {
      // إظهار رسالة نجاح
      showAlert('success', data.message);
      
      // إعادة توجيه بعد ثانيتين
      setTimeout() => {
        window.location.href = '{{ url_for("warnings:index" %}';
      }, 2000);
    } else {
      showAlert('danger', data.message);
      
      // إعادة تفعيل الزر
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }
  })
  .catch(error => {
    showAlert('danger', 'حدث خطأ في الاتصال');
    
    // إعادة تفعيل الزر
    submitBtn.innerHTML = originalText;
    submitBtn.disabled = false;
  });
});

function showAlert(type, message) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert">
      <span>&times;</span>
    </button>
  `;
  
  // إدراج التنبيه في أعلى الصفحة
  const container = document.querySelector('.container-fluid');
  container.insertBefore(alertDiv, container.firstChild);
  
  // إزالة التنبيه بعد 5 ثوان
  setTimeout() => {
    alertDiv.remove();
  }, 5000);
}

// تعيين التاريخ الحالي كحد أقصى لتاريخ الحادثة
document.addEventListener('DOMContentLoaded', function() {
  const today = new Date().toISOString().split('T')[0];
  document.getElementById('incident_date').max = today;
});
</script>
{% endblock %}
