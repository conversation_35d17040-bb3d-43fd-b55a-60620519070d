============================================================
📊 تقرير فحص مسميات قواعد البيانات
============================================================

📈 ملخص عام:
  - عدد الجداول في النماذج: 23
  - عدد الكلاسات: 23
  - عدد الجداول في قاعدة البيانات: 40
  - عدد الحقول الفريدة: 163
  - عدد المفاتيح الخارجية: 41

✅ لا يوجد تضارب في الأسماء

⚠️ تحذيرات (13):
  - حقل مكرر كثيراً: description (11 مرة)
  - حقل مكرر كثيراً: category (4 مرة)
  - حقل مكرر كثيراً: notes (7 مرة)
  - حقل مكرر كثيراً: client_id (5 مرة)
  - حقل مكرر كثيراً: file_size (4 مرة)
  - حقل مكرر كثيراً: case_id (6 مرة)
  - حقل مكرر كثيراً: status (6 مرة)
  - حقل مكرر كثيراً: created_by (5 مرة)
  - حقل مكرر كثيراً: title (4 مرة)
  - حقل مكرر كثيراً: employee_id (5 مرة)
  - مفتاح خارجي مستخدم كثيراً: users.id (15 مرة)
  - مفتاح خارجي مستخدم كثيراً: cases.id (6 مرة)
  - مفتاح خارجي مستخدم كثيراً: employees.id (6 مرة)

📋 أسماء الجداول:
  - appointments
  - attendance
  - case_notes
  - case_sessions
  - case_timeline
  - cases
  - client_files
  - clients
  - contracts
  - departments
  - documents
  - employee_documents
  - employees
  - invoices
  - lawyers
  - leave_requests
  - payments
  - penalties
  - permissions
  - roles
  - system_settings
  - users
  - warnings

🔤 الحقول الأكثر استخداماً:
  - id: 23 مرة
  - created_at: 22 مرة
  - updated_at: 16 مرة
  - description: 11 مرة
  - is_active: 11 مرة
  - notes: 7 مرة
  - case_id: 6 مرة
  - status: 6 مرة
  - client_id: 5 مرة
  - created_by: 5 مرة