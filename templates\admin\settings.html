{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام الشؤون القانونية{% endblock %}

{% block extra_css %}
<style>
.nav-tabs .nav-link {
    color: #495057;
    border: none;
    border-bottom: 2px solid transparent;
    background: none;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: none;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #007bff;
    background: none;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.logo-preview {
    background: white;
    border-radius: 50%;
    padding: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.logo-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.temp-logo-preview {
    background: white;
    border-radius: 50%;
    padding: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    border: 2px solid #28a745;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
    }
    50% {
        box-shadow: 0 4px 20px rgba(40, 167, 69, 0.6);
    }
    100% {
        box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
    }
}

.current-logo-preview {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.form-range {
    margin-bottom: 10px;
}

.input-group .form-range {
    margin-bottom: 0;
    margin-right: 10px;
}

.logo-size-controls {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.logo-size-controls .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.preset-buttons .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

.logo-preview-live {
    position: sticky;
    top: 20px;
    background: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.size-indicator {
    font-size: 0.8em;
    color: #6c757d;
    margin-top: 5px;
}
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2"><i class="fas fa-cog me-2"></i>
        إعدادات النظام
    </h1><div class="btn-toolbar mb-2 mb-md-0"><div class="btn-group me-2"><button type="button" class="btn btn-primary" onclick="saveSettings()"><i class="fas fa-save me-1"></i>
                حفظ الإعدادات
            </button><a href="{{ url_for ('admin.index' }}" class="btn btn-outline-secondary"><i class="fas fa-arrow-right me-1"></i>
                العودة للوحة التحكم
            </a></div></div></div><div class="row"><div class="col-lg-3"><!-- قائمة التبويبات الجانبية --><div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical"><button class="nav-link active" id="v-pills-general-tab" data-bs-toggle="pill" data-bs-target="#v-pills-general" type="button" role="tab"><i class="fas fa-building me-2"></i>
                الإعدادات العامة
            </button><button class="nav-link" id="v-pills-appearance-tab" data-bs-toggle="pill" data-bs-target="#v-pills-appearance" type="button" role="tab"><i class="fas fa-palette me-2"></i>
                المظهر والواجهة
            </button><button class="nav-link" id="v-pills-notifications-tab" data-bs-toggle="pill" data-bs-target="#v-pills-notifications" type="button" role="tab"><i class="fas fa-bell me-2"></i>
                الإشعارات
            </button><button class="nav-link" id="v-pills-security-tab" data-bs-toggle="pill" data-bs-target="#v-pills-security" type="button" role="tab"><i class="fas fa-shield-alt me-2"></i>
                الأمان
            </button><button class="nav-link" id="v-pills-backup-tab" data-bs-toggle="pill" data-bs-target="#v-pills-backup" type="button" role="tab"><i class="fas fa-database me-2"></i>
                النسخ الاحتياطية
            </button><button class="nav-link" id="v-pills-system-tab" data-bs-toggle="pill" data-bs-target="#v-pills-system" type="button" role="tab"><i class="fas fa-server me-2"></i>
                إعدادات النظام
            </button></div></div><div class="col-lg-9"><div class="tab-content" id="v-pills-tabContent"><!-- الإعدادات العامة --><div class="tab-pane fade show active" id="v-pills-general" role="tabpanel"><div class="card"><div class="card-header"><h5 class="mb-0">الإعدادات العامة</h5></div><div class="card-body"><form id="generalSettingsForm"><div class="row"><div class="col-md-6 mb-3"><label for="system_name" class="form-label">اسم النظام</label><input type="text" class="form-control" id="system_name" name="system_name" value="{{ settings.system_name or 'نظام الشؤون القانونية' }}"></div><div class="col-md-6 mb-3"><label for="company_name" class="form-label">اسم الشركة</label><input type="text" class="form-control" id="company_name" name="company_name" value="{{ settings.company_name or 'مكتب المحاماة المتقدم' }}"></div></div><div class="mb-3"><label for="company_address" class="form-label">عنوان الشركة</label><textarea class="form-control" id="company_address" name="company_address" rows="2">{{ settings.company_address or 'الرياض، المملكة العربية السعودية' }}</textarea></div><div class="row"><div class="col-md-6 mb-3"><label for="company_phone" class="form-label">هاتف الشركة</label><input type="tel" class="form-control" id="company_phone" name="company_phone" value="{{ settings.company_phone or '+966112345678' }}"></div><div class="col-md-6 mb-3"><label for="company_email" class="form-label">بريد الشركة</label><input type="email" class="form-control" id="company_email" name="company_email" value="{{ settings.company_email or '<EMAIL>' }}"></div></div><div class="row"><div class="col-md-4 mb-3"><label for="timezone" class="form-label">المنطقة الزمنية</label><select class="form-select" id="timezone" name="timezone"><option value="Asia/Riyadh") {% if settings.timezone == 'Asia/Riyadh' %}selected{% endif %}>الرياض (GMT+3)</option><option value="Asia/Dubai" {% if settings.timezone == 'Asia/Dubai' %}selected{% endif %}>دبي (GMT+4)</option><option value="Asia/Kuwait" {% if settings.timezone == 'Asia/Kuwait' %}selected{% endif %}>الكويت (GMT+3)</option><option value="Africa/Cairo" {% if settings.timezone == 'Africa/Cairo' %}selected{% endif %}>القاهرة (GMT+2)</option></select></div><div class="col-md-4 mb-3"><label for="language" class="form-label">اللغة</label><select class="form-select" id="language" name="language"><option value="ar" {% if settings.language == 'ar' %}selected{% endif %}>العربية</option><option value="en" {% if settings.language == 'en' %}selected{% endif %}>English</option></select></div><div class="col-md-4 mb-3"><label for="currency" class="form-label">العملة</label><select class="form-select" id="currency" name="currency"><option value="SAR" {% if settings.currency == 'SAR' %}selected{% endif %}>ريال سعودي (SAR)</option><option value="AED" {% if settings.currency == 'AED' %}selected{% endif %}>درهم إماراتي (AED)</option><option value="KWD" {% if settings.currency == 'KWD' %}selected{% endif %}>دينار كويتي (KWD)</option><option value="EGP" {% if settings.currency == 'EGP' %}selected{% endif %}>جنيه مصري (EGP)</option><option value="USD" {% if settings.currency == 'USD' %}selected{% endif %}>دولار أمريكي (USD)</option><option value="EUR" {% if settings.currency == 'EUR' %}selected{% endif %}>يورو (EUR)</option></select></div></div></form></div></div></div><!-- المظهر والواجهة --><div class="tab-pane fade" id="v-pills-appearance" role="tabpanel"><div class="card"><div class="card-header"><h5 class="mb-0">المظهر والواجهة</h5></div><div class="card-body"><form><div class="row"><div class="col-md-6 mb-3"><label for="theme" class="form-label">السمة</label><select class="form-select" id="theme"><option value="light">فاتح</option><option value="dark">داكن</option><option value="auto">تلقائي</option></select></div><div class="col-md-6 mb-3"><label for="primary_color" class="form-label">اللون الأساسي</label><input type="color" class="form-control form-control-color" id="primary_color"
                                           value="{{ settings.primary_color or '#007bff' }}"></div><div class="col-md-6 mb-3"><label for="secondary_color" class="form-label">اللون الثانوي</label><input type="color" class="form-control form-control-color" id="secondary_color"
                                           value="{{ settings.secondary_color or '#6c757d' }}"></div></div><div class="row"><div class="col-md-6 mb-3"><label for="theme_mode" class="form-label">وضع السمة</label><select class="form-select" id="theme_mode"><option value="light" {{ 'selected' if settings.theme_mode == 'light' else '' }}>فاتح</option><option value="dark" {{ 'selected' if settings.theme_mode == 'dark' else '' }}>داكن</option><option value="auto" {{ 'selected' if settings.theme_mode == 'auto' else '' }}>تلقائي</option></select></div><div class="col-md-6 mb-3"><label for="sidebar_style" class="form-label">نمط الشريط الجانبي</label><select class="form-select" id="sidebar_style"><option value="default" {{ 'selected' if settings.sidebar_style == 'default' else '' }}>افتراضي</option><option value="compact" {{ 'selected' if settings.sidebar_style == 'compact' else '' }}>مضغوط</option><option value="minimal" {{ 'selected' if settings.sidebar_style == 'minimal' else '' }}>بسيط</option></select></div></div><div class="mb-3"><label for="logo" class="form-label">شعار الشركة</label><div class="row"><div class="col-md-8"><input type="file" class="form-control" id="logo" accept="image/*"><div class="form-text">يفضل صورة بحجم 200x60 بكسل (PNG, JPG, SVG)</div></div><div class="col-md-4"><div class="current-logo-preview"><img id="logoPreview" src="{{ settings.current_logo or '/static/images/default-logo.svg' }}"
                                                 alt="الشعار الحالي" class="logo-preview" style="max-height: 60px;"><div class="mt-2"><button type="button" class="btn btn-sm btn-outline-danger" onclick="removeLogo()"><i class="fas fa-trash me-1"></i>
                                                    إزالة الشعار
                                                </button></div></div></div></div><div class="mt-2"><button type="button" class="btn btn-primary btn-sm" onclick="uploadLogo()"><i class="fas fa-upload me-1"></i>
                                        رفع الشعار
                                    </button></div></div><!-- إعدادات حجم الشعار --><div class="mb-3 logo-size-controls"><label class="form-label"><i class="fas fa-ruler me-2"></i>
                                    التحكم في حجم ومظهر الشعار
                                </label><div class="row"><div class="col-md-6"><label for="logo_height" class="form-label">الارتفاع (بكسل)</label><div class="input-group"><input type="range" class="form-range" id="logo_height_range"
                                                   min="20" max="80" value="{{ settings.logo_height or 40 }}"
                                                   oninput="updateLogoSize()"><input type="number" class="form-control" id="logo_height"
                                                   min="20" max="80" value="{{ settings.logo_height or 40 }}"
                                                   style="max-width: 80px;" onchange="updateLogoSize()"><span class="input-group-text">px</span></div></div><div class="col-md-6"><label for="logo_padding" class="form-label">المسافة الداخلية</label><div class="input-group"><input type="range" class="form-range" id="logo_padding_range"
                                                   min="0" max="15" value="{{ settings.logo_padding or 5 }}"
                                                   oninput="updateLogoSize()"><input type="number" class="form-control" id="logo_padding"
                                                   min="0" max="15" value="{{ settings.logo_padding or 5 }}"
                                                   style="max-width: 80px;" onchange="updateLogoSize()"><span class="input-group-text">px</span></div></div></div><div class="row mt-2"><div class="col-md-6"><label for="logo_border_width" class="form-label">سمك الحدود</label><div class="input-group"><input type="range" class="form-range" id="logo_border_width_range"
                                                   min="0" max="5" value="{{ settings.logo_border_width or 2 }}"
                                                   oninput="updateLogoSize()"><input type="number" class="form-control" id="logo_border_width"
                                                   min="0" max="5" value="{{ settings.logo_border_width or 2 }}"
                                                   style="max-width: 80px;" onchange="updateLogoSize()"><span class="input-group-text">px</span></div></div><div class="col-md-6"><label for="logo_shadow" class="form-label">شدة الظل</label><div class="input-group"><input type="range" class="form-range" id="logo_shadow_range"
                                                   min="0" max="20" value="{{ settings.logo_shadow or 8 }}"
                                                   oninput="updateLogoSize()"><input type="number" class="form-control" id="logo_shadow"
                                                   min="0" max="20" value="{{ settings.logo_shadow or 8 }}"
                                                   style="max-width: 80px;" onchange="updateLogoSize()"><span class="input-group-text">px</span></div></div></div><div class="mt-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="logo_circular"
                                               {{ 'checked' if settings.logo_circular else '' }} onchange="updateLogoSize()"><label class="form-check-label" for="logo_circular">
                                            خلفية دائرية
                                        </label></div></div><div class="mt-3 d-flex gap-2"><button type="button" class="btn btn-outline-primary btn-sm" onclick="applyPreset('small')"><i class="fas fa-compress me-1"></i>
                                        صغير
                                    </button><button type="button" class="btn btn-outline-primary btn-sm" onclick="applyPreset('medium')"><i class="fas fa-expand-arrows-alt me-1"></i>
                                        متوسط
                                    </button><button type="button" class="btn btn-outline-primary btn-sm" onclick="applyPreset('large')"><i class="fas fa-expand me-1"></i>
                                        كبير
                                    </button><button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetLogoSettings()"><i class="fas fa-undo me-1"></i>
                                        إعادة تعيين
                                    </button></div><div class="mt-2"><small class="text-muted">معاينة مباشرة في شريط التنقل أعلاه</small></div></div><div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="show_sidebar" checked><label class="form-check-label" for="show_sidebar">
                                        إظهار الشريط الجانبي افتراضياً
                                    </label></div></div></form></div></div></div><!-- الإشعارات --><div class="tab-pane fade" id="v-pills-notifications" role="tabpanel"><div class="card"><div class="card-header"><h5 class="mb-0">إعدادات الإشعارات</h5></div><div class="card-body"><form><div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" {% if settings.email_notifications %}checked{% endif %}><label class="form-check-label" for="email_notifications">
                                        تفعيل الإشعارات عبر البريد الإلكتروني
                                    </label></div></div><div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications" {% if settings.sms_notifications %}checked{% endif %}><label class="form-check-label" for="sms_notifications">
                                        تفعيل الإشعارات عبر الرسائل النصية
                                    </label></div></div><div class="mb-3"><label for="smtp_server" class="form-label">خادم البريد الإلكتروني</label><input type="text" class="form-control" id="smtp_server" placeholder="smtp.gmail.com"></div><div class="row"><div class="col-md-6 mb-3"><label for="smtp_port" class="form-label">منفذ SMTP</label><input type="number" class="form-control" id="smtp_port" value="587"></div><div class="col-md-6 mb-3"><label for="smtp_username" class="form-label">اسم المستخدم</label><input type="text" class="form-control" id="smtp_username"></div></div></form></div></div></div><!-- الأمان --><div class="tab-pane fade" id="v-pills-security" role="tabpanel"><div class="card"><div class="card-header"><h5 class="mb-0">إعدادات الأمان</h5></div><div class="card-body"><form><div class="row"><div class="col-md-6 mb-3"><label for="session_timeout" class="form-label">انتهاء الجلسة (دقيقة)</label><input type="number" class="form-control" id="session_timeout" value="30"></div><div class="col-md-6 mb-3"><label for="max_login_attempts" class="form-label">محاولات الدخول القصوى</label><input type="number" class="form-control" id="max_login_attempts" value="5"></div></div><div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="two_factor_auth"><label class="form-check-label" for="two_factor_auth">
                                        تفعيل المصادقة الثنائية
                                    </label></div></div><div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="force_password_change" checked><label class="form-check-label" for="force_password_change">
                                        إجبار تغيير كلمة المرور كل 90 يوم
                                    </label></div></div></form></div></div></div><!-- النسخ الاحتياطية --><div class="tab-pane fade" id="v-pills-backup" role="tabpanel"><div class="card"><div class="card-header"><h5 class="mb-0">إعدادات النسخ الاحتياطية</h5></div><div class="card-body"><form><div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="auto_backup" name="auto_backup" {% if settings.backup_enabled %}checked{% endif %}><label class="form-check-label" for="auto_backup">
                                        تفعيل النسخ الاحتياطية التلقائية
                                    </label></div></div><div class="row"><div class="col-md-6 mb-3"><label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطية</label><select class="form-select" id="backup_frequency"><option value="daily">يومي</option><option value="weekly" selected>أسبوعي</option><option value="monthly">شهري</option></select></div><div class="col-md-6 mb-3"><label for="backup_time" class="form-label">وقت النسخ الاحتياطي</label><input type="time" class="form-control" id="backup_time" value="02:00"></div></div><div class="mb-3"><label for="backup_retention" class="form-label">الاحتفاظ بالنسخ (أيام)</label><input type="number" class="form-control" id="backup_retention" value="30"></div></form></div></div></div><!-- إعدادات النظام --><div class="tab-pane fade" id="v-pills-system" role="tabpanel"><!-- معلومات الشبكة الحالية --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>
                            معلومات الشبكة الحالية
                        </h5></div><div class="card-body"><div class="row"><div class="col-md-6"><div class="info-item mb-3"><strong><i class="fas fa-globe me-2"></i>عنوان IP المحلي:</strong><span id="current-local-ip" class="text-primary">جاري التحميل...</span></div><div class="info-item mb-3"><strong><i class="fas fa-plug me-2"></i>المنفذ الحالي:</strong><span id="current-port" class="text-primary">جاري التحميل...</span></div><div class="info-item mb-3"><strong><i class="fas fa-link me-2"></i>رابط الوصول المحلي:</strong><a href="#" id="local-access-link" target="_blank" class="text-decoration-none">جاري التحميل...</a></div></div><div class="col-md-6"><div class="info-item mb-3"><strong><i class="fas fa-network-wired me-2"></i>رابط الوصول من الشبكة:</strong><a href="#" id="network-access-link" target="_blank" class="text-decoration-none">جاري التحميل...</a></div><div class="info-item mb-3"><strong><i class="fas fa-shield-alt me-2"></i>حالة الأمان:</strong><span id="security-status" class="badge">جاري التحميل...</span></div><div class="info-item mb-3"><strong><i class="fas fa-tachometer-alt me-2"></i>وضع التشغيل:</strong><span id="debug-status" class="badge">جاري التحميل...</span></div></div></div><div class="alert alert-info mt-3"><i class="fas fa-lightbulb me-2"></i><strong>نصائح للوصول من الشبكة:</strong><ul class="mb-0 mt-2"><li>تأكد من أن جميع الأجهزة متصلة بنفس الشبكة</li><li>قد تحتاج لفتح المنفذ في جدار الحماية</li><li>استخدم HTTPS في البيئة الإنتاجية</li></ul></div><div class="text-end"><button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshNetworkInfo()"><i class="fas fa-sync me-1"></i>
                                تحديث معلومات الشبكة
                            </button></div></div></div><!-- إعدادات الخادم --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-server me-2"></i>
                            إعدادات الخادم
                        </h5></div><div class="card-body"><form id="serverSettingsForm"><div class="row"><div class="col-md-6 mb-3"><label for="server_host" class="form-label"><i class="fas fa-globe me-1"></i>
                                        عنوان الخادم (Host)
                                    </label><input type="text" class="form-control" id="server_host" value="0.0.0.0"><div class="form-text">0.0.0.0 للوصول من جميع الشبكات، 127.0.0.1 للوصول المحلي فقط</div></div><div class="col-md-6 mb-3"><label for="server_port" class="form-label"><i class="fas fa-plug me-1"></i>
                                        منفذ الخادم (Port)
                                    </label><input type="number" class="form-control" id="server_port" value="5000" min="1" max="65535"><div class="form-text">المنفذ الذي سيعمل عليه الخادم (1-65535)</div></div></div><div class="row"><div class="col-md-6 mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="server_debug" checked><label class="form-check-label" for="server_debug"><i class="fas fa-bug me-1"></i>
                                            وضع التطوير (Debug Mode)
                                        </label></div><div class="form-text">يُفعل إعادة التحميل التلقائي وعرض الأخطاء التفصيلية</div></div><div class="col-md-6 mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="server_threaded" checked><label class="form-check-label" for="server_threaded"><i class="fas fa-tasks me-1"></i>
                                            المعالجة المتوازية (Threaded)
                                        </label></div><div class="form-text">يسمح بمعالجة عدة طلبات في نفس الوقت</div></div></div><div class="row"><div class="col-md-6 mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="server_reloader"><label class="form-check-label" for="server_reloader"><i class="fas fa-sync-alt me-1"></i>
                                            إعادة التحميل التلقائي (Auto Reload)
                                        </label></div><div class="form-text">إعادة تشغيل الخادم عند تغيير الملفات</div></div><div class="col-md-6 mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="allow_external_access" checked><label class="form-check-label" for="allow_external_access"><i class="fas fa-network-wired me-1"></i>
                                            السماح بالوصول الخارجي
                                        </label></div><div class="form-text">السماح للأجهزة الأخرى بالوصول للنظام</div></div></div><div class="row"><div class="col-md-6 mb-3"><label for="max_connections" class="form-label"><i class="fas fa-users me-1"></i>
                                        الحد الأقصى للاتصالات
                                    </label><input type="number" class="form-control" id="max_connections" value="100" min="1" max="1000"><div class="form-text">عدد الاتصالات المتزامنة المسموحة</div></div><div class="col-md-6 mb-3"><label for="connection_timeout" class="form-label"><i class="fas fa-clock me-1"></i>
                                        مهلة الاتصال (ثانية)
                                    </label><input type="number" class="form-control" id="connection_timeout" value="30" min="5" max="300"><div class="form-text">المدة المسموحة للاتصال قبل انتهاء الصلاحية</div></div></div><hr class="my-4"><h6 class="mb-3"><i class="fas fa-shield-alt me-2"></i>
                                إعدادات الأمان
                            </h6><div class="row"><div class="col-md-6 mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="enable_https"><label class="form-check-label" for="enable_https"><i class="fas fa-lock me-1"></i>
                                            تفعيل HTTPS
                                        </label></div><div class="form-text">يتطلب شهادة SSL صالحة</div></div><div class="col-md-6 mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="enable_compression" checked><label class="form-check-label" for="enable_compression"><i class="fas fa-compress me-1"></i>
                                            تفعيل ضغط البيانات
                                        </label></div><div class="form-text">يقلل من استهلاك البيانات</div></div></div><div class="row" id="sslSettings" style="display: none;"><div class="col-md-6 mb-3"><label for="ssl_cert_path" class="form-label"><i class="fas fa-certificate me-1"></i>
                                        مسار شهادة SSL
                                    </label><input type="text" class="form-control" id="ssl_cert_path" placeholder="/path/to/cert.pem"></div><div class="col-md-6 mb-3"><label for="ssl_key_path" class="form-label"><i class="fas fa-key me-1"></i>
                                        مسار مفتاح SSL
                                    </label><input type="text" class="form-control" id="ssl_key_path" placeholder="/path/to/key.pem"></div></div><div class="row"><div class="col-md-6 mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="enable_caching" checked><label class="form-check-label" for="enable_caching"><i class="fas fa-memory me-1"></i>
                                            تفعيل التخزين المؤقت
                                        </label></div><div class="form-text">يحسن من أداء النظام</div></div><div class="col-md-6 mb-3"><label for="cache_timeout" class="form-label"><i class="fas fa-stopwatch me-1"></i>
                                        مدة التخزين المؤقت (ثانية)
                                    </label><input type="number" class="form-control" id="cache_timeout" value="300" min="60" max="3600"></div></div><div class="d-flex justify-content-between"><button type="button" class="btn btn-success" onclick="saveServerSettings()"><i class="fas fa-save me-1"></i>
                                    حفظ إعدادات الخادم
                                </button><button type="button" class="btn btn-warning" onclick="restartServer()"><i class="fas fa-redo me-1"></i>
                                    إعادة تشغيل الخادم
                                </button><button type="button" class="btn btn-info" onclick="loadCurrentServerSettings()"><i class="fas fa-sync me-1"></i>
                                    تحديث الإعدادات
                                </button></div></form></div></div><!-- إعدادات النظام المتقدمة --><div class="card"><div class="card-header"><h5 class="mb-0"><i class="fas fa-cogs me-2"></i>
                            إعدادات النظام المتقدمة
                        </h5></div><div class="card-body"><form><div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" {% if settings.maintenance_mode %}checked{% endif %}><label class="form-check-label" for="maintenance_mode">
                                        وضع الصيانة
                                    </label></div><div class="form-text">سيمنع وصول المستخدمين العاديين للنظام</div></div><div class="mb-3"><div class="form-check"><input class="form-check-input" type="checkbox" id="debug_mode"><label class="form-check-label" for="debug_mode">
                                        وضع التطوير
                                    </label></div><div class="form-text">لا تفعل هذا في البيئة الإنتاجية</div></div><div class="row"><div class="col-md-6 mb-3"><label for="max_file_size" class="form-label">حد رفع الملفات (MB)</label><input type="number" class="form-control" id="max_file_size" value="16"></div><div class="col-md-6 mb-3"><label for="allowed_extensions" class="form-label">امتدادات الملفات المسموحة</label><input type="text" class="form-control" id="allowed_extensions" value="pdf,doc,docx,jpg,png"></div></div></form></div></div></div></div></div></div>
{% endblock %}

{% block extra_js %}
<script>
function saveSettings() {
    // جمع جميع الإعدادات
    const formData = new FormData();
    formData.append('system_name', document.getElementById('system_name').value);
    formData.append('company_name', document.getElementById('company_name').value);
    formData.append('company_address', document.getElementById('company_address').value);
    formData.append('company_phone', document.getElementById('company_phone').value);
    formData.append('company_email', document.getElementById('company_email').value);
    formData.append('timezone', document.getElementById('timezone').value);
    formData.append('language', document.getElementById('language').value);
    formData.append('currency', document.getElementById('currency').value);

    if (document.getElementById('email_notifications').checked) {
        formData.append('email_notifications', 'on');
    }
    if (document.getElementById('sms_notifications').checked) {
        formData.append('sms_notifications', 'on');
    }
    if (document.getElementById('auto_backup').checked) {
        formData.append('auto_backup', 'on');
    }
    if (document.getElementById('maintenance_mode').checked) {
        formData.append('maintenance_mode', 'on');
    }

    // إضافة إعدادات المظهر
    formData.append('primary_color', document.getElementById('primary_color').value);
    formData.append('secondary_color', document.getElementById('secondary_color').value);
    formData.append('theme_mode', document.getElementById('theme_mode').value);
    formData.append('sidebar_style', document.getElementById('sidebar_style').value);

    // إرسال البيانات للخادم
    fetch('/admin/settings', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            showAlert('success', 'تم حفظ الإعدادات بنجاح!');
            setTimeout() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('error', 'حدث خطأ أثناء حفظ الإعدادات');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('error', 'حدث خطأ أثناء حفظ الإعدادات');
    });
}

// دالة منفصلة لحفظ إعدادات المظهر فقط
function saveAppearanceSettings() {
    const appearanceData = {
        primary_color: document.getElementById('primary_color').value,
        secondary_color: document.getElementById('secondary_color').value,
        theme_mode: document.getElementById('theme_mode').value,
        sidebar_style: document.getElementById('sidebar_style').value
    };

    
    fetch('/admin/save-appearance-settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(appearanceData)
    })
    .then(response => response.json()
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('error', 'حدث خطأ أثناء حفظ إعدادات المظهر');
    });
}

// تحديث معاينة اللون وحفظ تلقائي
document.getElementById('primary_color').addEventListener('change', function() {
    document.documentElement.style.setProperty('--bs-primary', this.value);
    saveAppearanceSettings();
});

document.getElementById('secondary_color').addEventListener('change', function() {
    document.documentElement.style.setProperty('--bs-secondary', this.value);
    saveAppearanceSettings();
});

document.getElementById('theme_mode').addEventListener('change', function() {
    applyThemeMode(this.value);
    saveAppearanceSettings();
});

document.getElementById('sidebar_style').addEventListener('change', function() {
    applySidebarStyle(this.value);
    saveAppearanceSettings();
});

// دالة لتطبيق وضع السمة
function applyThemeMode(mode) {
    if (mode === 'dark') {
        document.body.classList.add('dark-theme');
        document.body.classList.remove('light-theme');
    } else {
        document.body.classList.add('light-theme');
        document.body.classList.remove('dark-theme');
    }
}

// دالة لتطبيق نمط الشريط الجانبي
function applySidebarStyle(style) {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.className = sidebar.className.replace(/sidebar-\w+/g, '');
        sidebar.classList.add(`sidebar-${style}`);
    }
}

// إدارة اللوجو
function uploadLogo() {
    const fileInput = document.getElementById('logo');
    const file = fileInput.files[0];

    if (!file) {
        showAlert('warning', 'يرجى اختيار ملف أولاً');
        return;
    }

    // التحقق من نوع الملف
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type) {
        showAlert('error', 'نوع الملف غير مدعوم. يرجى استخدام PNG, JPG, JPEG, GIF, أو SVG');
        return;
    }

    // التحقق من حجم الملف (5MB كحد أقصى)
    if (file.size > 5 * 1024 * 1024) {
        showAlert('error', 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
        return;
    }

    const formData = new FormData();
    formData.append('logo', file);

    // إظهار مؤشر التحميل
    const uploadBtn = event.target;
    const originalText = uploadBtn.innerHTML;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
    uploadBtn.disabled = true;

    fetch('/admin/upload-logo', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json()
    .then(data => {
        uploadBtn.innerHTML = originalText;
        uploadBtn.disabled = false;

        if (data.success) {
            showAlert('success', data.message);
            // تحديث معاينة الشعار
            document.getElementById('logoPreview').src = data.logo_url;
            // مسح اختيار الملف
            fileInput.value = '';
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        uploadBtn.innerHTML = originalText;
        uploadBtn.disabled = false;
        showAlert('error', 'حدث خطأ أثناء رفع الشعار');
        console.error('Error:', error);
    });
}

function removeLogo() {
    if (confirm('هل أنت متأكد من رغبتك في إزالة الشعار؟') {
        // إعادة تعيين الشعار للافتراضي
        document.getElementById('logoPreview').src = '/static/images/default-logo.svg';
        showAlert('success', 'تم إزالة الشعار بنجاح');
    }
}

// معاينة الشعار قبل الرفع
document.getElementById('logo').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // إنشاء معاينة مؤقتة
            const preview = document.createElement('img');
            preview.src = e.target.result;
            preview.className = 'temp-logo-preview mt-2';
            preview.style.maxHeight = '60px';
            preview.title = 'معاينة الشعار الجديد';

            // إزالة المعاينة السابقة إن وجدت
            const existingPreview = document.querySelector('.temp-logo-preview');
            if (existingPreview) {
                existingPreview.remove();
            }

            // إضافة المعاينة الجديدة
            document.querySelector('.current-logo-preview').appendChild(preview);
        };
        reader.readAsDataURL(file);
    }
});

// دالة لتحديث حجم الشعار
function updateLogoSize() {
    const height = document.getElementById('logo_height').value;
    const padding = document.getElementById('logo_padding').value;
    const borderWidth = document.getElementById('logo_border_width').value;
    const shadow = document.getElementById('logo_shadow').value;
    const circular = document.getElementById('logo_circular').checked;

    // تحديث القيم المرتبطة
    document.getElementById('logo_height_range').value = height;
    document.getElementById('logo_padding_range').value = padding;
    document.getElementById('logo_border_width_range').value = borderWidth;
    document.getElementById('logo_shadow_range').value = shadow;

    // تطبيق التغييرات على الشعار في شريط التنقل
    const navbarLogo = document.querySelector('.navbar-brand img');
    if (navbarLogo) {
        navbarLogo.style.height = height + 'px';
        navbarLogo.style.padding = padding + 'px';
        navbarLogo.style.borderWidth = borderWidth + 'px';
        navbarLogo.style.boxShadow = `0 2px ${shadow}px rgba(0, 0, 0, 0.15)`;
        navbarLogo.style.borderRadius = circular ? '50%' : '8px';
    }

    // تطبيق التغييرات على معاينة الشعار
    const previewLogo = document.getElementById('logoPreview');
    if (previewLogo) {
        previewLogo.style.height = height + 'px';
        previewLogo.style.padding = padding + 'px';
        previewLogo.style.borderWidth = borderWidth + 'px';
        previewLogo.style.boxShadow = `0 2px ${shadow}px rgba(0, 0, 0, 0.15)`;
        previewLogo.style.borderRadius = circular ? '50%' : '8px';
    }

    // حفظ الإعدادات تلقائياً
    saveLogoSettings();
}

// دالة لحفظ إعدادات الشعار
function saveLogoSettings() {
    const logoSettings = {
        logo_height: document.getElementById('logo_height').value,
        logo_padding: document.getElementById('logo_padding').value,
        logo_border_width: document.getElementById('logo_border_width').value,
        logo_shadow: document.getElementById('logo_shadow').value,
        logo_circular: document.getElementById('logo_circular').checked
    };

    fetch('/admin/save-logo-settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(logoSettings)
    })
    .then(response => response.json()
    .then(data => {
        if (data.success) {
            
        }
    })
    .catch(error => {
        console.error('خطأ في حفظ إعدادات الشعار:', error);
    });
}

// دالة لتحديث المتزلقات عند تغيير القيم
function syncRangeInputs() {
    // ربط المتزلقات بالحقول الرقمية
    document.getElementById('logo_height').addEventListener('input', function() {
        document.getElementById('logo_height_range').value = this.value;
        updateLogoSize();
    });

    document.getElementById('logo_padding').addEventListener('input', function() {
        document.getElementById('logo_padding_range').value = this.value;
        updateLogoSize();
    });

    document.getElementById('logo_border_width').addEventListener('input', function() {
        document.getElementById('logo_border_width_range').value = this.value;
        updateLogoSize();
    });

    document.getElementById('logo_shadow').addEventListener('input', function() {
        document.getElementById('logo_shadow_range').value = this.value;
        updateLogoSize();
    });
}

// دالة لإظهار التنبيهات
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

    const icon = type === 'success' ? 'check-circle' :
                 type === 'error' ? 'exclamation-triangle' :
                 type === 'warning' ? 'exclamation-circle' : 'info-circle';

    alertDiv.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// دالة لتطبيق الإعدادات المسبقة
function applyPreset(size) {
    let settings;

    switch(size) {
        case 'small':
            settings = {
                height: 30,
                padding: 3,
                border_width: 1,
                shadow: 4,
                circular: true
            };
            break;
        case 'medium':
            settings = {
                height: 40,
                padding: 5,
                border_width: 2,
                shadow: 8,
                circular: true
            };
            break;
        case 'large':
            settings = {
                height: 60,
                padding: 8,
                border_width: 3,
                shadow: 12,
                circular: true
            };
            break;
    }

    // تطبيق الإعدادات
    document.getElementById('logo_height').value = settings.height;
    document.getElementById('logo_padding').value = settings.padding;
    document.getElementById('logo_border_width').value = settings.border_width;
    document.getElementById('logo_shadow').value = settings.shadow;
    document.getElementById('logo_circular').checked = settings.circular;

    updateLogoSize();
    showAlert('success', `تم تطبيق الحجم ${size === 'small' ? 'الصغير' : size === 'medium' ? 'المتوسط' : 'الكبير'}`);
}

// دالة لإعادة تعيين الإعدادات
function resetLogoSettings() {
    if (confirm('هل أنت متأكد من رغبتك في إعادة تعيين إعدادات الشعار للقيم الافتراضية؟') {
        document.getElementById('logo_height').value = 40;
        document.getElementById('logo_padding').value = 5;
        document.getElementById('logo_border_width').value = 2;
        document.getElementById('logo_shadow').value = 8;
        document.getElementById('logo_circular').checked = true;

        updateLogoSize();
        showAlert('success', 'تم إعادة تعيين إعدادات الشعار');
    }
}

// دالة لحفظ الإعدادات كملف تعريف مخصص
function saveCustomProfile() {
    const settings = {
        height: document.getElementById('logo_height').value,
        padding: document.getElementById('logo_padding').value,
        border_width: document.getElementById('logo_border_width').value,
        shadow: document.getElementById('logo_shadow').value,
        circular: document.getElementById('logo_circular').checked
    };

    const profileName = prompt('أدخل اسم الملف التعريفي المخصص:');
    if (profileName) {
        localStorage.setItem(`logo_profile_${profileName}`, JSON.stringify(settings);
        showAlert('success', `تم حفظ الملف التعريفي: ${profileName}`);
    }
}

// تطبيق الإعدادات المحفوظة عند تحميل الصفحة
function applyCurrentSettings() {
    // تطبيق الألوان المحفوظة
    const primaryColor = document.getElementById('primary_color').value;
    const secondaryColor = document.getElementById('secondary_color').value;

    document.documentElement.style.setProperty('--bs-primary', primaryColor);
    document.documentElement.style.setProperty('--bs-secondary', secondaryColor);

    // تطبيق السمة المحفوظة
    const themeMode = document.getElementById('theme_mode').value;
    applyThemeMode(themeMode);

    // تطبيق نمط الشريط الجانبي المحفوظ
    const sidebarStyle = document.getElementById('sidebar_style').value;
    applySidebarStyle(sidebarStyle);
}

// وظائف إعدادات الخادم
function loadCurrentServerSettings() {
    fetch('/api/server/settings')
        .then(response => response.json()
        .then(data => {
            if (data.success) {
                const settings = data.settings;
                document.getElementById('server_host').value = settings.host || '0.0.0.0';
                document.getElementById('server_port').value = settings.port || 5000;
                document.getElementById('server_debug').checked = settings.debug || false;
                document.getElementById('server_threaded').checked = settings.threaded || true;
                document.getElementById('server_reloader').checked = settings.use_reloader || false;
                document.getElementById('allow_external_access').checked = settings.network_settings?.allow_external_access || true;
                document.getElementById('max_connections').value = settings.network_settings?.max_connections || 100;
                document.getElementById('connection_timeout').value = settings.network_settings?.timeout || 30;
                document.getElementById('enable_https').checked = settings.security_settings?.enable_https || false;
                document.getElementById('ssl_cert_path').value = settings.security_settings?.ssl_cert_path || '';
                document.getElementById('ssl_key_path').value = settings.security_settings?.ssl_key_path || '';
                document.getElementById('enable_compression').checked = settings.performance_settings?.enable_compression || true;
                document.getElementById('enable_caching').checked = settings.performance_settings?.enable_caching || true;
                document.getElementById('cache_timeout').value = settings.performance_settings?.cache_timeout || 300;

                // تحديث معلومات الشبكة الحالية
                updateNetworkInfo(settings);

                toggleSSLSettings();
                showAlert('success', 'تم تحميل إعدادات الخادم الحالية');
            } else {
                showAlert('error', 'فشل في تحميل إعدادات الخادم');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
}

function updateNetworkInfo(settings) {
    // استخدام API للحصول على معلومات الشبكة الدقيقة
    fetch('/api/server/network-info')
        .then(response => response.json()
        .then(data => {
            if (data.success) {
                const info = data.network_info;

                // تحديث المعلومات الأساسية
                document.getElementById('current-local-ip').textContent = info.local_ip;
                document.getElementById('current-port').textContent = info.port;

                // تحديث الروابط
                document.getElementById('local-access-link').href = info.local_url;
                document.getElementById('local-access-link').textContent = info.local_url;
                document.getElementById('network-access-link').href = info.network_url;
                document.getElementById('network-access-link').textContent = info.network_url;

                // تحديث حالة الأمان
                const securityStatus = document.getElementById('security-status');
                if (info.is_https) {
                    securityStatus.textContent = 'آمن (HTTPS)';
                    securityStatus.className = 'badge bg-success';
                } else {
                    securityStatus.textContent = 'غير آمن (HTTP)';
                    securityStatus.className = 'badge bg-warning';
                }

                // تحديث وضع التشغيل
                const debugStatus = document.getElementById('debug-status');
                if (info.debug_mode) {
                    debugStatus.textContent = 'وضع التطوير';
                    debugStatus.className = 'badge bg-info';
                } else {
                    debugStatus.textContent = 'وضع الإنتاج';
                    debugStatus.className = 'badge bg-success';
                }
            }
        })
        .catch(error => {
            console.error('Error loading network info:', error);
            // fallback للطريقة القديمة
            const port = settings.port || 5000;
            const isHttps = settings.security_settings?.enable_https || false;
            const protocol = isHttps ? 'https' : 'http';

            document.getElementById('current-port').textContent = port;

            const localLink = `${protocol}://localhost:${port}`;
            document.getElementById('local-access-link').href = localLink;
            document.getElementById('local-access-link').textContent = localLink;

            document.getElementById('current-local-ip').textContent = 'غير متاح';
            document.getElementById('network-access-link').textContent = 'غير متاح';
        });
}

function refreshNetworkInfo() {
    // إظهار مؤشر التحميل
    document.getElementById('current-local-ip').textContent = 'جاري التحديث...';
    document.getElementById('current-port').textContent = 'جاري التحديث...';

    // تحديث معلومات الشبكة
    fetch('/api/server/network-info')
        .then(response => response.json()
        .then(data => {
            if (data.success) {
                const info = data.network_info;

                // تحديث المعلومات
                document.getElementById('current-local-ip').textContent = info.local_ip;
                document.getElementById('current-port').textContent = info.port;

                // تحديث الروابط
                document.getElementById('local-access-link').href = info.local_url;
                document.getElementById('local-access-link').textContent = info.local_url;
                document.getElementById('network-access-link').href = info.network_url;
                document.getElementById('network-access-link').textContent = info.network_url;

                // تحديث حالة الأمان
                const securityStatus = document.getElementById('security-status');
                if (info.is_https) {
                    securityStatus.textContent = 'آمن (HTTPS)';
                    securityStatus.className = 'badge bg-success';
                } else {
                    securityStatus.textContent = 'غير آمن (HTTP)';
                    securityStatus.className = 'badge bg-warning';
                }

                // تحديث وضع التشغيل
                const debugStatus = document.getElementById('debug-status');
                if (info.debug_mode) {
                    debugStatus.textContent = 'وضع التطوير';
                    debugStatus.className = 'badge bg-info';
                } else {
                    debugStatus.textContent = 'وضع الإنتاج';
                    debugStatus.className = 'badge bg-success';
                }

                showAlert('success', 'تم تحديث معلومات الشبكة بنجاح');
            } else {
                showAlert('error', 'فشل في تحديث معلومات الشبكة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في تحديث معلومات الشبكة');
            document.getElementById('current-local-ip').textContent = 'خطأ';
            document.getElementById('current-port').textContent = 'خطأ';
        });
}

function saveServerSettings() {
    const settings = {
        host: document.getElementById('server_host').value,
        port: parseInt(document.getElementById('server_port').value),
        debug: document.getElementById('server_debug').checked,
        threaded: document.getElementById('server_threaded').checked,
        use_reloader: document.getElementById('server_reloader').checked,
        network_settings: {
            allow_external_access: document.getElementById('allow_external_access').checked,
            max_connections: parseInt(document.getElementById('max_connections').value),
            timeout: parseInt(document.getElementById('connection_timeout').value)
        },
        security_settings: {
            enable_https: document.getElementById('enable_https').checked,
            ssl_cert_path: document.getElementById('ssl_cert_path').value,
            ssl_key_path: document.getElementById('ssl_key_path').value
        },
        performance_settings: {
            enable_caching: document.getElementById('enable_caching').checked,
            cache_timeout: parseInt(document.getElementById('cache_timeout').value),
            enable_compression: document.getElementById('enable_compression').checked
        }
    };

    fetch('/api/server/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json()
    .then(data => {
        if (data.success) {
            showAlert('success', 'تم حفظ إعدادات الخادم بنجاح! قد تحتاج لإعادة تشغيل الخادم لتطبيق بعض التغييرات.');
        } else {
            showAlert('error', data.message || 'فشل في حفظ إعدادات الخادم');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'حدث خطأ في حفظ إعدادات الخادم');
    });
}

function restartServer() {
    if (confirm('هل أنت متأكد من إعادة تشغيل الخادم؟ سيتم قطع الاتصال مؤقتاً.') {
        showAlert('warning', 'جاري إعادة تشغيل الخادم... سيتم إعادة توجيهك تلقائياً.');

        fetch('/api/server/restart', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json()
        .then(data => {
            if (data.success) {
                showAlert('info', 'تم إرسال أمر إعادة التشغيل. سيتم إعادة تحميل الصفحة خلال 10 ثوان...');
                setTimeout() => {
                    window.location.reload();
                }, 10000);
            } else {
                showAlert('error', data.message || 'فشل في إعادة تشغيل الخادم');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في إعادة تشغيل الخادم');
        });
    }
}

function toggleSSLSettings() {
    const enableHttps = document.getElementById('enable_https').checked;
    const sslSettings = document.getElementById('sslSettings');

    if (enableHttps) {
        sslSettings.style.display = 'block';
    } else {
        sslSettings.style.display = 'none';
    }
}

// إضافة مستمع للتغيير في إعداد HTTPS
document.addEventListener('DOMContentLoaded', function() {
    const enableHttpsCheckbox = document.getElementById('enable_https');
    if (enableHttpsCheckbox) {
        enableHttpsCheckbox.addEventListener('change', toggleSSLSettings);
    }
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    syncRangeInputs();
    updateLogoSize(); // تطبيق الإعدادات المحفوظة
    applyCurrentSettings(); // تطبيق إعدادات المظهر المحفوظة
    loadCurrentServerSettings(); // تحميل إعدادات الخادم الحالية
});
</script>
{% endblock %}
