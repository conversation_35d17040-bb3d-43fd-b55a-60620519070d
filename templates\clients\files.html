{% extends "base.html" %} {% block title %}ملفات العميل: {{ client.full_name }}{% endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
><h1 class="h2"><i class="fas fa-folder-open me-2"></i>
    ملفات العميل: {{ client.full_name }}
  </h1><div class="btn-toolbar mb-2 mb-md-0"><div class="btn-group me-2"><a
        href="{{ url_for ('clients.view', id=client.id }}"
        class="btn btn-outline-secondary"
  ><i class="fas fa-arrow-right me-1"></i>
        العودة لتفاصيل العميل
      </a><button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        data-bs-target="#uploadModal"
  ><i class="fas fa-upload me-1"></i>
        رفع ملف جديد
      </button></div></div></div><!-- معلومات العميل السريعة --><div class="card mb-4"><div class="card-body"><div class="row"><div class="col-md-3"><strong>رمز العميل:</strong> {{ client.client_code }}
      </div><div class="col-md-3"><strong>نوع العميل:</strong>
        {% if client.client_type == 'individual' %}
        <span class="badge bg-info">فرد</span>
        {% elif client.client_type == 'company' %}
        <span class="badge bg-warning">شركة</span>
        {% elif client.client_type == 'organization' %}
        <span class="badge bg-secondary">مؤسسة</span>
        {% endif %}
      </div><div class="col-md-3"><strong>البريد الإلكتروني:</strong> {{ client.email or 'غير محدد' }}
      </div><div class="col-md-3"><strong>الهاتف:</strong> {{ client.phone or 'غير محدد' }}
      </div></div></div></div><!-- قائمة الملفات --><div class="card"><div class="card-header"><h5 class="mb-0"><i class="fas fa-files me-2"></i>
      ملفات العميل ({{ files|length }}
    </h5></div><div class="card-body">
    {% if files %}
    <div class="table-responsive"><table class="table table-hover"><thead><tr><th>اسم الملف</th><th>النوع</th><th>الحجم</th><th>الوصف</th><th>رفع بواسطة</th><th>تاريخ الرفع</th><th>الإجراءات</th></tr></thead><tbody>
          {% for file in files %}
          <tr><td><i
                class="fas fa-file-{{ get_file_icon(file.file_type or '' }} me-2"
          ></i>
             ) {{ file.original_filename or 'ملف غير معروف' }}
            </td><td><span class="badge bg-secondary"
            >{{ (file.file_type or 'N/A').upper( }}</span></td><td>{{ format_file_size(file.file_size or 0 }}</td><td>{{ file.description or 'لا يوجد وصف' }}</td><td>
              {{ file.uploader.full_name if file.uploader else 'غير محدد' }}
            </td><td>
              {{ file.created_at.created_at else 'غير محدد'|strftime("%Y-%m-%d %H:%M") if file }}
            </td><td><div class="btn-group btn-group-sm"><a
                  href="{{ url_for ('clients.download_file', client_id=client.id, file_id=file.id }}"
                  class="btn btn-outline-primary"
                  title="تحميل"
            ><i class="fas fa-download"></i></a><button
                  class="btn btn-outline-danger delete-file-btn"
                  data-file-id="{{ file.id }}"
                  data-filename="{{ file.original_filename }}"
                  title="حذف"
            ><i class="fas fa-trash"></i></button></div></td></tr>
          {% endfor %}
        </tbody></table></div>
    {% else %}
    <div class="text-center py-5"><i class="fas fa-folder-open fa-3x text-muted mb-3"></i><h5 class="text-muted">لا توجد ملفات</h5><p class="text-muted">لم يتم رفع أي ملفات لهذا العميل بعد</p><button
        type="button"
        class="btn btn-primary"
        data-bs-toggle="modal"
        data-bs-target="#uploadModal"
  ><i class="fas fa-upload me-1"></i>
        رفع ملف جديد
      </button></div>
   ) {% endif %}
  </div></div><!-- نافذة رفع الملفات --><div
  class="modal fade"
  id="uploadModal"
  tabindex="-1"
  aria-labelledby="uploadModalLabel"
  aria-hidden="true"
><div class="modal-dialog"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="uploadModalLabel"><i class="fas fa-upload me-2"></i>
          رفع ملف جديد
        </h5><button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="إغلاق"
    ></button></div><form id="uploadForm" enctype="multipart/form-data"><div class="modal-body"><div class="mb-3"><label for="file" class="form-label"
          >اختر الملف <span class="text-danger">*</span></label><input
              type="file"
              class="form-control"
              id="file"
              name="file"
              required
              accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
            /><div class="form-text">
              الأنواع المسموحة: PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF
            </div></div><div class="mb-3"><label for="description" class="form-label">وصف الملف</label><textarea
              class="form-control"
              id="description"
              name="description"
              rows="3"
              placeholder="أدخل وصفاً للملف (اختياري)"
        ></textarea></div><div class="alert alert-info"><h6 class="alert-heading"><i class="fas fa-info-circle me-1"></i>
              معلومات مهمة:
            </h6><ul class="mb-0"><li>الحد الأقصى لحجم الملف: 10 ميجابايت</li><li>تأكد من أن الملف لا يحتوي على معلومات حساسة</li><li>سيتم حفظ الملف بشكل آمن ومشفر</li></ul></div></div><div class="modal-footer"><button
            type="button"
            class="btn btn-secondary"
            data-bs-dismiss="modal"
      >
            إلغاء
          </button><button type="submit" class="btn btn-primary"><i class="fas fa-upload me-1"></i>
            رفع الملف
          </button></div></form></div></div></div><script>
  // رفع الملف
  document
    .getElementById("uploadForm")
    .addEventListener("submit", function (e) {
      e.preventDefault();

      const formData = new FormData(this);
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;

      // تعطيل الزر وإظهار مؤشر التحميل
      submitBtn.disabled = true;
      submitBtn.innerHTML =
        '<i class="fas fa-spinner fa-spin me-1"></i> جاري الرفع...';

      fetch(`/clients/{{ client.id }}/upload`, {
        method: "POST",
        body: formData,
      })
        .then(response) => response.json()
        .then(data) => {
          if (data.success) {
            showAlert("success", data.message);
            // إغلاق النافذة وإعادة تحميل الصفحة
            bootstrap.Modal.getInstance(
              document.getElementById("uploadModal")
            ).hide();
            setTimeout() => location.reload(), 1500);
          } else {
            showAlert("error", data.message);
          }
        })
        .catch(error) => {
          console.error("Error:", error);
          showAlert("error", "حدث خطأ في الاتصال بالخادم");
        })
        .finally() => {
          // إعادة تفعيل الزر
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalText;
        });
    });

  // حذف الملف
  function deleteFile(fileId, filename) {
    if (
      confirm(
        `هل أنت متأكد من حذف الملف "${filename}"؟\nلا يمكن التراجع عن هذا الإجراء.`
      )
    ) {
      fetch(`/clients/{{ client.id }}/files/${fileId}/delete`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then(response) => response.json()
        .then(data) => {
          if (data.success) {
            showAlert("success", data.message);
            setTimeout() => location.reload(), 1500);
          } else {
            showAlert("error", data.message);
          }
        })
        .catch(error) => {
          console.error("Error:", error);
          showAlert("error", "حدث خطأ في الاتصال بالخادم");
        });
    }
  }

  // إظهار التنبيهات
  function showAlert(type, message) {
    const alertDiv = document.createElement("div");
    alertDiv.className = `alert alert-${
      type === "error" ? "danger" : type
    } alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container =
      document.querySelector(".container-fluid") || document.body;
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }

  // إعادة تعيين النموذج عند إغلاق النافذة
  document
    .getElementById("uploadModal")
    .addEventListener("hidden.bs.modal", function () {
      document.getElementById("uploadForm").reset();
    });

  // إضافة مستمعي الأحداث لأزرار الحذف
  document.addEventListener("click", function (e) {
    if (e.target.closest(".delete-file-btn") {
      const button = e.target.closest(".delete-file-btn");
      const fileId = button.getAttribute("data-file-id");
      const filename = button.getAttribute("data-filename");
      deleteFile(fileId, filename);
    }
  });
</script>
{% endblock %} {% block extra_js %}
<script>
  // دوال مساعدة لتنسيق البيانات
  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k);
    return parseFloat(bytes / Math.pow(k, i).toFixed(2) + " " + sizes[i];
  }

  function getFileIcon(fileType) {
    const icons = {
      pdf: "pdf",
      doc: "word",
      docx: "word",
      txt: "text",
      jpg: "image",
      jpeg: "image",
      png: "image",
      gif: "image",
    };
    return icons[fileType] || "file";
  }
</script>
{% endblock %}
