from django.shortcuts import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime
import uuid

bp = Blueprint('cases', __name__, url_prefix='/cases')

def get_models():
    """الحصول على النماذج من التطبيق الحالي"""
    try:
        from django.shortcuts import current_app
        return current_app.config.get('MODELS', {})
    except RuntimeError:
        # في حالة عدم وجود app context
        return {}

@bp.route('/')
@login_required
def index():
    """عرض جميع القضايا"""
    try:
        page = request.GET.get('page', 1, type=int)
        search = request.GET.get('search', '')
        status = request.GET.get('status', '')

        # بيانات تجريبية للقضايا
        fake_cases = [
            {
                'id': 1,
                'case_number': 'CASE-2025-001',
                'case_title': 'قضية تجارية - نزاع عقد',
                'client': {'full_name': 'شركة الأعمال المتقدمة'},
                'lawyer': {'user': {'full_name': 'د. سارة أحمد'}},
                'case_type': 'تجاري',
                'case_status': 'active',
                'court_name': 'المحكمة التجارية',
                'case_value': 500000,
                'filing_date': datetime.now().date(),
                'next_hearing_date': datetime.now().date(),
                'created_at': datetime.now()
            },
            {
                'id': 2,
                'case_number': 'CASE-2025-002',
                'case_title': 'قضية مدنية - تعويض أضرار',
                'client': {'full_name': 'أحمد محمد السعيد'},
                'lawyer': {'user': {'full_name': 'أ. محمد علي'}},
                'case_type': 'مدني',
                'case_status': 'pending',
                'court_name': 'المحكمة العامة',
                'case_value': 150000,
                'filing_date': datetime.now().date(),
                'next_hearing_date': None,
                'created_at': datetime.now()
            },
            {
                'id': 3,
                'case_number': 'CASE-2025-003',
                'case_title': 'قضية عمالية - فصل تعسفي',
                'client': {'full_name': 'فاطمة علي الزهراني'},
                'lawyer': {'user': {'full_name': 'د. سارة أحمد'}},
                'case_type': 'عمالي',
                'case_status': 'completed',
                'court_name': 'محكمة العمل',
                'case_value': 75000,
                'filing_date': datetime.now().date(),
                'next_hearing_date': None,
                'created_at': datetime.now()
            }
        ]

        # تطبيق الفلترة
        if search:
            fake_cases = [case for case in fake_cases if search.lower() in case['case_title'].lower() or search.lower() in case['case_number'].lower()]

        if status:
            fake_cases = [case for case in fake_cases if case['case_status'] == status]

        # محاكاة pagination
        cases_obj = type('obj', (object,), {
            'items': fake_cases,
            'pages': 1,
            'has_prev': False,
            'has_next': False,
            'page': 1,
            'per_page': 10,
            'total': len(fake_cases)
        })()

        return render('cases/index.html',
                             cases=cases_obj,
                             search=search,
                             status=status)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """إضافة قضية جديدة"""
    try:
        if request.method == 'POST':
            flash('تم حفظ القضية بنجاح (وظيفة تجريبية)', 'success')
            return redirect(url_for('cases.index'))

        # بيانات تجريبية للعملاء والمحامين
        fake_clients = [
            type('obj', (object,), {'id': 1, 'full_name': 'أحمد محمد السعيد', 'client_code': 'CLIENT-001'})(),
            type('obj', (object,), {'id': 2, 'full_name': 'شركة الأعمال المتقدمة', 'client_code': 'CLIENT-002'})(),
        ]

        fake_lawyers = [
            type('obj', (object,), {
                'id': 1,
                'user': type('obj', (object,), {'full_name': 'د. سارة أحمد'})(),
                'specialization': 'القانون التجاري'
            })(),
            type('obj', (object,), {
                'id': 2,
                'user': type('obj', (object,), {'full_name': 'أ. محمد علي'})(),
                'specialization': 'القانون المدني'
            })(),
        ]

        return render('cases/add.html', clients=fake_clients, lawyers=fake_lawyers)

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل القضية"""
    try:
        # بيانات تجريبية للعرض
        fake_case = type('obj', (object,), {
            'id': id,
            'case_number': f'CASE-2025-{id:04d}',
            'case_title': 'قضية تجريبية',
            'case_type': 'تجاري',
            'case_status': 'active',
            'client': type('obj', (object,), {'full_name': 'عميل تجريبي'})(),
            'lawyer': type('obj', (object,), {'user': type('obj', (object,), {'full_name': 'محامي تجريبي'})()})(),
            'court_name': 'المحكمة التجارية',
            'case_value': 100000,
            'filing_date': datetime.now().date(),
            'case_description': 'وصف تجريبي للقضية',
            'notes': 'ملاحظات تجريبية',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        })()

        fake_sessions = []
        fake_notes = []
        fake_timeline = []

        return render('cases/view.html',
                             case=fake_case,
                             sessions=fake_sessions,
                             notes=fake_notes,
                             timeline=fake_timeline)
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('cases.index'))

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل القضية"""
    try:
        if request.method == 'POST':
            flash('تم تحديث القضية بنجاح (وظيفة تجريبية)', 'success')
            return redirect(url_for('cases.view', id=id))

        # بيانات تجريبية للقضية المراد تعديلها
        fake_case = {
            'id': id,
            'case_number': f'CASE-2025-{id:04d}',
            'case_title': 'قضية تجريبية للتعديل',
            'case_type': 'تجاري',
            'case_status': 'active',
            'client_id': 1,
            'lawyer_id': 1,
            'court_name': 'المحكمة التجارية',
            'case_value': 100000,
            'filing_date': datetime.now().date(),
            'case_description': 'وصف تجريبي للقضية',
            'notes': 'ملاحظات تجريبية'
        }

        # بيانات تجريبية للعملاء والمحامين
        fake_clients = [
            {'id': 1, 'full_name': 'أحمد محمد السعيد', 'client_code': 'CLIENT-001'},
            {'id': 2, 'full_name': 'شركة الأعمال المتقدمة', 'client_code': 'CLIENT-002'},
        ]

        fake_lawyers = [
            {
                'id': 1,
                'user': {'full_name': 'د. سارة أحمد'},
                'specialization': 'القانون التجاري'
            },
            {
                'id': 2,
                'user': {'full_name': 'أ. محمد علي'},
                'specialization': 'القانون المدني'
            },
        ]

        return render('cases/edit.html',
                             case=fake_case,
                             clients=fake_clients,
                             lawyers=fake_lawyers)

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('cases.index'))

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف القضية"""
    try:
        flash('تم حذف القضية بنجاح (وظيفة تجريبية)', 'success')
        return redirect(url_for('cases.index'))
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'error')
        return redirect(url_for('cases.index'))


