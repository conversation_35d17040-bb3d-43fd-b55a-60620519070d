#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح الأخطاء المتبقية في بناء الجملة
"""

import os
import re
import glob

def fix_specific_syntax_patterns():
    """إصلاح أنماط أخطاء محددة"""
    
    print("🔧 إصلاح أنماط الأخطاء المحددة...")
    
    template_files = glob.glob("templates/**/*.html", recursive=True)
    total_fixes = 0
    
    for file_path in template_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_fixes = 0
            
            # أنماط الأخطاء المحددة
            patterns = [
                # إصلاح "expected token 'end of print statement', got 'else'"
                (r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s*\n\s*([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}'),
                (r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}'),
                
                # إصلاح "unexpected ')'"
                (r'\{\{\s*([^}]+?)\)\s*\)\s*\}\}', r'{{ \1) }}'),
                (r'\{\{\s*([^}]+?)\(\s*\(\s*([^}]+?)\s*\}\}', r'{{ \1(\2 }}'),
                
                # إصلاح "unexpected '}', expected ')'"
                (r'\{\{\s*([^}]+?)\(\s*([^}]+?)\s*\}\s*\}\}', r'{{ \1(\2) }}'),
                
                # إصلاح الأقواس المفقودة في الدوال
                (r'\{\{\s*([^}]+?)\.([a-zA-Z_]+)\(\s*([^}]*?)\s*\}\}', r'{{ \1.\2(\3) }}'),
                
                # إصلاح المسافات الإضافية في التعبيرات الشرطية
                (r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', r'{{ \1 if \2 else \3 }}'),
                
                # إصلاح علامات Django المختلطة
                (r'\{\%\s*url\s+([^%]+?)\s*\%\}', r'{{ url_for(\1) }}'),
                
                # إصلاح الفلاتر الخاطئة
                (r'\|\s*date\s*:', r'|strftime('),
                (r'\|\s*floatformat\s*:', r'|round('),
                (r'\|\s*default\s*:', r'|default('),
                
                # إصلاح الأقواس المضاعفة
                (r'\{\{\s*\{\{\s*([^}]+?)\s*\}\}\s*\}\}', r'{{ \1 }}'),
                (r'\{\%\s*\{\%\s*([^%]+?)\s*\%\}\s*\%\}', r'{% \1 %}'),
                
                # إصلاح التعبيرات المقسمة
                (r'\{\{\s*([^}]+?)\n\s*([^}]+?)\s*\}\}', r'{{ \1 \2 }}'),
                (r'\{\%\s*([^%]+?)\n\s*([^%]+?)\s*\%\}', r'{% \1 \2 %}'),
            ]
            
            for pattern, replacement in patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
                if matches:
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
                    file_fixes += len(matches)
            
            # إصلاحات نصية مباشرة للمشاكل الشائعة
            text_fixes = [
                # إصلاح if-else المقسم
                ('if user.email else\n                user.username', 'if user.email else user.username'),
                ('if user.full_name else\n                user.username', 'if user.full_name else user.username'),
                ('if employee.user.email else\n                employee.user.username', 'if employee.user.email else employee.user.username'),
                
                # إصلاح الأقواس الإضافية
                ('format(format(', 'format('),
                ('))', ')'),
                ('((', '('),
                
                # إصلاح المسافات الإضافية
                ('  }}', ' }}'),
                ('{{  ', '{{ '),
                ('  %}', ' %}'),
                ('{%  ', '{% '),
                
                # إصلاح علامات Django
                ('{% url ', '{{ url_for('),
                (' %}"', ') }}"'),
                
                # إصلاح الفلاتر
                ('|date:"', '|strftime("'),
                ('|floatformat:', '|round('),
                ('|default:', '|default('),
                ('|truncatechars:', '|truncate('),
            ]
            
            for old, new in text_fixes:
                if old in content:
                    content = content.replace(old, new)
                    file_fixes += 1
            
            # حفظ الملف إذا تم إجراء إصلاحات
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ {file_path}: تم إصلاح {file_fixes} خطأ")
                total_fixes += file_fixes
            
        except Exception as e:
            print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"🎉 تم إصلاح {total_fixes} خطأ إضافي")
    return total_fixes

def fix_individual_problematic_files():
    """إصلاح الملفات المشكلة بشكل فردي"""
    
    print("\n🔧 إصلاح الملفات المشكلة بشكل فردي...")
    
    problematic_files = [
        'templates/admin/edit_profile.html',
        'templates/admin/profile.html',
        'templates/cases/edit.html',
        'templates/cases/view.html',
        'templates/clients/edit.html',
        'templates/clients/files.html',
        'templates/employees/edit.html',
        'templates/employees/view.html',
    ]
    
    fixed_count = 0
    
    for file_path in problematic_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # إصلاحات محددة لكل ملف
                if 'edit_profile.html' in file_path or 'profile.html' in file_path:
                    # إصلاح مشاكل ملفات البروفايل
                    content = re.sub(r'\{\{\s*user\.email\s+if\s+user\.email\s+else\s*user\.username\s*\}\}', 
                                   r'{{ user.email if user.email else user.username }}', content)
                    content = re.sub(r'\{\{\s*user\.full_name\s+if\s+user\.full_name\s+else\s*user\.username\s*\}\}', 
                                   r'{{ user.full_name if user.full_name else user.username }}', content)
                
                elif 'edit.html' in file_path or 'view.html' in file_path:
                    # إصلاح مشاكل ملفات التحرير والعرض
                    content = re.sub(r'\{\{\s*([^}]+?)\s+if\s+([^}]+?)\s+else\s+([^}]+?)\s*\}\}', 
                                   r'{{ \1 if \2 else \3 }}', content)
                    content = re.sub(r'\{\{\s*([^}]+?)\)\s*\)\s*\}\}', r'{{ \1) }}', content)
                
                # إصلاحات عامة
                content = re.sub(r'\{\{\s*([^}]+?)\s{2,}\}\}', r'{{ \1 }}', content)
                content = re.sub(r'\{\%\s*([^%]+?)\s{2,}\%\}', r'{% \1 %}', content)
                
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"  ✅ {file_path}: تم إصلاحه")
                    fixed_count += 1
                
            except Exception as e:
                print(f"  ❌ خطأ في {file_path}: {e}")
    
    print(f"✅ تم إصلاح {fixed_count} ملف مشكل")
    return fixed_count

def validate_syntax():
    """التحقق النهائي من بناء الجملة"""
    
    print("\n🔍 التحقق النهائي من بناء الجملة...")
    
    try:
        from jinja2 import Environment, FileSystemLoader, TemplateSyntaxError
        
        env = Environment(loader=FileSystemLoader('templates'))
        template_files = glob.glob("templates/**/*.html", recursive=True)
        
        valid_count = 0
        error_count = 0
        errors = []
        
        for file_path in template_files:
            try:
                relative_path = os.path.relpath(file_path, 'templates').replace('\\', '/')
                template = env.get_template(relative_path)
                valid_count += 1
                
            except TemplateSyntaxError as e:
                error_count += 1
                errors.append(f"{relative_path}: {str(e)[:100]}...")
            except Exception as e:
                error_count += 1
                errors.append(f"{relative_path}: {str(e)[:100]}...")
        
        print(f"📊 النتائج النهائية:")
        print(f"  ✅ ملفات صحيحة: {valid_count}")
        print(f"  ❌ ملفات بها أخطاء: {error_count}")
        
        if errors:
            print(f"\n❌ الأخطاء المتبقية ({len(errors)}):")
            for error in errors[:5]:  # أول 5 أخطاء
                print(f"  - {error}")
            if len(errors) > 5:
                print(f"  ... و {len(errors) - 5} خطأ آخر")
        
        return error_count == 0, errors
        
    except ImportError:
        print("❌ لا يمكن استيراد Jinja2")
        return False, []

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إصلاح الأخطاء المتبقية...")
    
    # إصلاح الأنماط المحددة
    pattern_fixes = fix_specific_syntax_patterns()
    
    # إصلاح الملفات المشكلة
    individual_fixes = fix_individual_problematic_files()
    
    # التحقق النهائي
    is_valid, errors = validate_syntax()
    
    print("\n" + "="*50)
    print("📊 تقرير الإصلاح النهائي")
    print("="*50)
    print(f"🔧 إصلاحات الأنماط: {pattern_fixes}")
    print(f"🎯 إصلاحات فردية: {individual_fixes}")
    print(f"📁 إجمالي الإصلاحات: {pattern_fixes + individual_fixes}")
    
    if is_valid:
        print("\n🎉 تم إصلاح جميع أخطاء بناء الجملة!")
        print("✅ جميع القوالب تعمل بشكل صحيح")
    else:
        print(f"\n⚠️ لا تزال هناك {len(errors)} أخطاء")
        print("💡 قد تحتاج مراجعة يدوية")
    
    print("="*50)
    
    return is_valid

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
