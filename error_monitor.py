#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب الأخطاء التلقائي
Auto Error Monitor

يراقب الملفات ويصلح الأخطاء تلقائياً عند حدوث تغييرات
"""

import time
import json
import logging
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from auto_error_fixer import AutoErrorFixer

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('error_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ErrorFixHandler(FileSystemEventHandler):
    """معالج أحداث تغيير الملفات"""
    
    def __init__(self, config_path: str = "error_fixer_config.json"):
        self.fixer = AutoErrorFixer()
        self.config = self.load_config(config_path)
        self.last_fix_time = {}
        self.fix_delay = 2  # ثواني انتظار قبل الإصلاح
        
    def load_config(self, config_path: str) -> dict:
        """تحميل إعدادات المراقب"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"فشل في تحميل الإعدادات: {e}")
            return {"auto_error_fixer_config": {"template_fixes": {"enabled": True}}}
    
    def should_process_file(self, file_path: Path) -> bool:
        """تحديد ما إذا كان يجب معالجة الملف"""
        # تجاهل الملفات المؤقتة والنسخ الاحتياطية
        if file_path.name.startswith('.') or file_path.name.endswith('.backup'):
            return False
            
        # تجاهل المجلدات المستثناة
        exclude_dirs = self.config.get("auto_error_fixer_config", {}).get(
            "file_patterns", {}).get("exclude_directories", [])
        
        for exclude_dir in exclude_dirs:
            if exclude_dir in str(file_path):
                return False
        
        # فحص أنواع الملفات المدعومة
        supported_extensions = ['.html', '.py', '.js', '.css']
        return file_path.suffix in supported_extensions
    
    def fix_file_with_delay(self, file_path: Path):
        """إصلاح الملف مع تأخير لتجنب الإصلاح المتكرر"""
        current_time = time.time()
        file_key = str(file_path)
        
        # تحديث وقت آخر إصلاح
        self.last_fix_time[file_key] = current_time
        
        # انتظار قبل الإصلاح
        time.sleep(self.fix_delay)
        
        # التحقق من عدم حدوث تغيير آخر أثناء الانتظار
        if self.last_fix_time.get(file_key, 0) > current_time:
            return  # تم تحديث الملف مرة أخرى، تجاهل هذا الإصلاح
        
        self.fix_file(file_path)
    
    def fix_file(self, file_path: Path):
        """إصلاح ملف واحد"""
        try:
            logger.info(f"إصلاح الملف: {file_path}")
            
            if file_path.suffix == '.html':
                success = self.fixer.fix_template_file(file_path)
                if success:
                    self.fixer.add_javascript_for_dynamic_styles(file_path)
                    
            elif file_path.suffix == '.py':
                success = self.fixer.fix_python_file(file_path)
                
            elif file_path.suffix == '.js':
                success = self.fixer.fix_javascript_file(file_path)
                
            else:
                return
            
            if success:
                logger.info(f"✅ تم إصلاح {file_path}")
            else:
                logger.debug(f"ℹ️ لا توجد أخطاء في {file_path}")
                
        except Exception as e:
            logger.error(f"خطأ في إصلاح {file_path}: {e}")
    
    def on_modified(self, event):
        """عند تعديل ملف"""
        if event.is_directory:
            return
            
        file_path = Path(event.src_path)
        
        if self.should_process_file(file_path):
            logger.info(f"تم تعديل الملف: {file_path}")
            # تشغيل الإصلاح في thread منفصل لتجنب التأخير
            import threading
            threading.Thread(
                target=self.fix_file_with_delay, 
                args=(file_path,),
                daemon=True
            ).start()
    
    def on_created(self, event):
        """عند إنشاء ملف جديد"""
        if event.is_directory:
            return
            
        file_path = Path(event.src_path)
        
        if self.should_process_file(file_path):
            logger.info(f"تم إنشاء ملف جديد: {file_path}")
            # انتظار قصير للتأكد من اكتمال كتابة الملف
            import threading
            threading.Thread(
                target=self.fix_file_with_delay, 
                args=(file_path,),
                daemon=True
            ).start()

class ErrorMonitor:
    """مراقب الأخطاء الرئيسي"""
    
    def __init__(self, watch_directories: list | None = None):
        self.watch_directories = watch_directories or [
            "templates", "routes", "static", "."
        ]
        self.observer = Observer()
        self.handler = ErrorFixHandler()
        
    def start_monitoring(self):
        """بدء المراقبة"""
        logger.info("🔍 بدء مراقبة الأخطاء التلقائية...")
        
        for directory in self.watch_directories:
            dir_path = Path(directory)
            if dir_path.exists():
                self.observer.schedule(
                    self.handler, 
                    str(dir_path), 
                    recursive=True
                )
                logger.info(f"📁 مراقبة المجلد: {dir_path}")
            else:
                logger.warning(f"⚠️ المجلد غير موجود: {dir_path}")
        
        self.observer.start()
        logger.info("✅ تم بدء المراقبة بنجاح")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        logger.info("⏹️ إيقاف مراقبة الأخطاء...")
        self.observer.stop()
        self.observer.join()
        logger.info("✅ تم إيقاف المراقبة")

def main():
    """الوظيفة الرئيسية"""
    import argparse
    
    parser = argparse.ArgumentParser(description="مراقب الأخطاء التلقائي")
    parser.add_argument('--directories', nargs='+', 
                       help='المجلدات المراد مراقبتها')
    parser.add_argument('--config', default='error_fixer_config.json',
                       help='ملف الإعدادات')
    
    args = parser.parse_args()
    
    print("🔍 مراقب الأخطاء التلقائي")
    print("=" * 40)
    print("اضغط Ctrl+C للإيقاف")
    print()
    
    try:
        monitor = ErrorMonitor(args.directories)
        monitor.start_monitoring()
    except Exception as e:
        logger.error(f"خطأ في تشغيل المراقب: {e}")

if __name__ == "__main__":
    main()
