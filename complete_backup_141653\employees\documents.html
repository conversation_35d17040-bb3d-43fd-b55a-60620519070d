{% extends "base.html" %}

{% block title %}مستندات الموظف: {{ employee.full_name }} - نظام الشؤون القانونية{% endblock %}

{# دوال مساعدة محلية - تم إزالتها لتجنب تضارب الأسماء #}

{% block content %}
<!-- رأس التقرير للطباعة -->
{% include 'components/print_header.html' with context %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2"><i class="fas fa-folder-open me-2"></i>
        مستندات الموظف: {{ employee.full_name }}
    </h1><div class="btn-toolbar mb-2 mb-md-0"><div class="btn-group me-2"><a href="{{ url_for('employees.view', id=employee.id }}" class="btn btn-outline-secondary"><i class="fas fa-arrow-right me-1"></i>
                العودة لتفاصيل الموظف
            </a><button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal"><i class="fas fa-upload me-1"></i>
                رفع مستند جديد
            </button></div></div></div><!-- معلومات الموظف --><div class="card mb-4"><div class="card-body"><div class="row"><div class="col-md-3"><strong>رقم الموظف:</strong> {{ employee.employee_number }}
            </div><div class="col-md-3"><strong>القسم:</strong> {{ employee.department.name if employee.department else 'غير محدد' }}
            </div><div class="col-md-3"><strong>المنصب:</strong> {{ employee.position or 'غير محدد' }}
            </div><div class="col-md-3"><strong>عدد المستندات:</strong> {{ documents.total }}
            </div></div></div></div><!-- فلاتر البحث --><div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="fas fa-search me-2"></i>
            البحث والفلترة
        </h5></div><div class="card-body"><form method="GET" action="{{ url_for('employees.documents', id=employee.id }}"><div class="row"><div class="col-md-4"><div class="mb-3"><label for="search" class="form-label">البحث</label><input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="العنوان، اسم الملف، الوصف..."></div></div><div class="col-md-4"><div class="mb-3"><label for="type" class="form-label">نوع المستند</label><select class="form-select" id="type" name="type"><option value="">جميع الأنواع</option>
                            {% for type_value, type_name in document_types %}
                            <option value="{{ type_value }}" {{ 'selected' if selected_type == type_value }}>{{ type_name }}</option>
                            {% endfor %}
                        </select></div></div><div class="col-md-4"><div class="mb-3"><label class="form-label"></label><div class="d-grid"><button type="submit" class="btn btn-primary"><i class="fas fa-search me-1"></i>
                                بحث
                            </button></div></div></div></div></form></div></div><!-- قائمة المستندات --><div class="card"><div class="card-header"><h5 class="mb-0"><i class="fas fa-list me-2"></i>
            المستندات ({{ documents.total }} مستند)
        </h5></div><div class="card-body">
        {% if documents.items %}
        <div class="table-responsive"><table class="table table-hover"><thead><tr><th>العنوان</th><th>نوع المستند</th><th>تاريخ المستند</th><th>تاريخ انتهاء الصلاحية</th><th>حجم الملف</th><th>تاريخ الرفع</th><th>الإجراءات</th></tr></thead><tbody>
                    {% for doc in documents.items %}
                    <tr><td><div class="d-flex align-items-center">
                                {% if doc.file_type == 'pdf' %}
                                    <i class="fas fa-file-pdf me-2 text-danger"></i>
                                {% elif doc.file_type in ['doc', 'docx'] %}
                                    <i class="fas fa-file-word me-2 text-primary"></i>
                                {% elif doc.file_type == 'txt' %}
                                    <i class="fas fa-file-alt me-2 text-secondary"></i>
                                {% elif doc.file_type in ['jpg', 'jpeg', 'png', 'gif'] %}
                                    <i class="fas fa-file-image me-2 text-success"></i>
                                {% else %}
                                    <i class="fas fa-file me-2 text-muted"></i>
                                {% endif %}
                                <div><strong>{{ doc.title }}</strong>
                                    {% if doc.is_confidential %}
                                    <span class="badge bg-warning ms-1">سري</span>
                                    {% endif %}
                                    <br><small class="text-muted">{{ doc.original_filename }}</small>
                                    {% if doc.description %}
                                    <br><small class="text-muted">{{ doc.description }}</small>
                                    {% endif %}
                                </div></div></td><td>
                            {% for type_value, type_name in document_types %}
                                {% if doc.document_type == type_value %}
                                    <span class="badge bg-info">{{ type_name }}</span>
                                {% endif %}
                            {% endfor %}
                        </td><td>{{ doc.document_date.document_date else '-'|strftime("%Y-%m-%d") if doc }}</td><td>
                            {% if doc.expiry_date %}
                                {% set days_to_expiry = (doc.expiry_date - today).days if today else 0 %}
                                {% if days_to_expiry < 0 %}
                                    <span class="text-danger">{{ doc.expiry_date.strftime("%Y-%m-%d") if doc.expiry_date else "-" }} (منتهي)</span>
                                {% elif days_to_expiry <= 30 %}
                                    <span class="text-warning">{{ doc.expiry_date.strftime("%Y-%m-%d") if doc.expiry_date else "-" }} (قريب الانتهاء)</span>
                                {% else %}
                                    {{ doc.expiry_date.strftime("%Y-%m-%d") if doc.expiry_date else "-" }}
                                {% endif %}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td><td>
                            {% if doc.file_size %}
                                {{ format_file_size(doc.file_size }}
                            {% else %}
                                -
                            {% endif %}
                        </td><td>{{ doc.created_at.created_at else '-'|strftime("%Y-%m-%d") if doc }}</td><td><div class="btn-group btn-group-sm"><a href="{{ url_for('employees.download_document', doc_id=doc.id }}" 
                                   class="btn btn-outline-primary" title="تحميل"><i class="fas fa-download"></i></a><button onclick="deleteDocument('{{ doc.id }}', '{{ doc.title }}')"
                                        class="btn btn-outline-danger" title="حذف"><i class="fas fa-trash"></i></button></div></td></tr>
                    {% endfor %}
                </tbody></table></div><!-- Pagination -->
        {% if documents.pages > 1 %}
        <nav aria-label="صفحات المستندات"><ul class="pagination justify-content-center">
                {% if documents.has_prev %}
                <li class="page-item"><a class="page-link" href="{{ url_for('employees.documents', id=employee.id, page=documents.prev_num, search=search, type=selected_type }}">السابق</a></li>
                {% endif %}
                
                {% for page_num in documents.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != documents.page %}
                        <li class="page-item"><a class="page-link" href="{{ url_for('employees.documents', id=employee.id, page=page_num, search=search, type=selected_type }}">{{ page_num }}</a></li>
                        {% else %}
                        <li class="page-item active"><span class="page-link">{{ page_num }}</span></li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled"><span class="page-link">...</span></li>
                    {% endif %}
                {% endfor %}
                
                {% if documents.has_next %}
                <li class="page-item"><a class="page-link" href="{{ url_for('employees.documents', id=employee.id, page=documents.next_num, search=search, type=selected_type }}">التالي</a></li>
                {% endif %}
            </ul></nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5"><i class="fas fa-folder-open fa-3x text-muted mb-3"></i><h5 class="text-muted">لا توجد مستندات</h5><p class="text-muted">لم يتم رفع أي مستندات لهذا الموظف بعد</p><button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal"><i class="fas fa-upload me-1"></i>
                رفع مستند جديد
            </button></div>
        {% endif %}
    </div></div><!-- نافذة رفع مستند جديد --><div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="uploadModalLabel"><i class="fas fa-upload me-2"></i>
                    رفع مستند جديد
                </h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button></div><form id="uploadForm" enctype="multipart/form-data"><div class="modal-body"><div class="row"><div class="col-md-6"><div class="mb-3"><label for="file" class="form-label">اختر الملف <span class="text-danger">*</span></label><input type="file" class="form-control" id="file" name="file" required 
                                       accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"><div class="form-text">الأنواع المدعومة: PDF, DOC, DOCX, TXT, JPG, PNG, GIF (حد أقصى 50 ميجابايت)</div></div></div><div class="col-md-6"><div class="mb-3"><label for="document_type" class="form-label">نوع المستند <span class="text-danger">*</span></label><select class="form-select" id="document_type" name="document_type" required><option value="">اختر نوع المستند</option>
                                    {% for type_value, type_name in document_types %}
                                    <option value="{{ type_value }}">{{ type_name }}</option>
                                    {% endfor %}
                                </select></div></div></div><div class="mb-3"><label for="title" class="form-label">عنوان المستند</label><input type="text" class="form-control" id="title" name="title" 
                               placeholder="سيتم استخدام اسم الملف إذا ترك فارغاً"></div><div class="mb-3"><label for="description" class="form-label">وصف المستند</label><textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="وصف مختصر للمستند (اختياري)"></textarea></div><div class="row"><div class="col-md-6"><div class="mb-3"><label for="document_date" class="form-label">تاريخ المستند</label><input type="date" class="form-control" id="document_date" name="document_date"></div></div><div class="col-md-6"><div class="mb-3"><label for="expiry_date" class="form-label">تاريخ انتهاء الصلاحية</label><input type="date" class="form-control" id="expiry_date" name="expiry_date"><div class="form-text">للمستندات التي لها تاريخ انتهاء صلاحية</div></div></div></div><div class="form-check"><input class="form-check-input" type="checkbox" id="is_confidential" name="is_confidential"><label class="form-check-label" for="is_confidential">
                            مستند سري
                        </label><div class="form-text">المستندات السرية تتطلب صلاحيات خاصة للوصول إليها</div></div></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button><button type="submit" class="btn btn-primary"><i class="fas fa-upload me-1"></i>
                        رفع المستند
                    </button></div></form></div></div></div><script>
// رفع المستند
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // التحقق من حجم الملف قبل الرفع
    const fileInput = document.getElementById('file');
    const file = fileInput.files[0];

    if (file && file.size > 50 * 1024 * 1024) { // 50 MB
        showAlert('error', 'حجم الملف كبير جداً. الحد الأقصى المسموح هو 50 ميجابايت.');
        return;
    }

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // تعطيل الزر وإظهار مؤشر التحميل
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري الرفع...';
    
    fetch(`/employees/{{ employee.id }}/documents/upload`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.status === 413) {
            throw new Error('حجم الملف كبير جداً. الحد الأقصى المسموح هو 50 ميجابايت.');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (error.message.includes('كبير جداً') {
            showAlert('error', error.message);
        } else {
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        }
    })
    .finally() => {
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// حذف المستند
function deleteDocument(docId, docTitle) {
    if (confirm(`هل أنت متأكد من حذف المستند "${docTitle}"؟`) {
        fetch(`/employees/documents/${docId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json()
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout() => location.reload(), 1500);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'حدث خطأ في الاتصال بالخادم');
        });
    }
}

// عرض التنبيهات
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid') || document.body;
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تعيين تاريخ اليوم كتاريخ افتراضي للمستند
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('document_date').value = today;
});
</script>
{% endblock %}
