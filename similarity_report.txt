============================================================
📊 تقرير فحص التشابه في العلاقات والقوالب والنماذج
============================================================

📈 ملخص عام:
  - إجمالي مشاكل التشابه: 26
  - قوالب مكررة: 0
  - بنى قوالب متشابهة: 6
  - نماذج مكررة: 0
  - مسارات مكررة: 20
  - تحذيرات: 0

⚠️ بنى قوالب متشابهة (6):
  1. templates\dashboard.html ≈ templates\dashboard_backup.html
  2. templates\appointments\index.html ≈ templates\documents\index.html ≈ templates\invoices\index.html ≈ templates\lawyers\index.html ≈ templates\reports\index.html
  3. templates\attendance\add.html ≈ templates\attendance\edit.html
  4. templates\components\print_header.html ≈ templates\components\print_signatures.html
  5. templates\penalties\add.html ≈ templates\penalties\edit.html
  6. templates\warnings\add.html ≈ templates\warnings\edit.html

❌ مسارات مكررة (20):
  1. دالة 'get_models' في: routes\admin.py, routes\appointments.py, routes\attendance.py, routes\cases.py, routes\clients.py, routes\contracts.py, routes\documents.py, routes\employees.py, routes\invoices.py, routes\lawyers.py, routes\leaves.py, routes\penalties.py, routes\reports.py, routes\settings.py, routes\warnings.py
  2. دالة 'index' في: routes\admin.py, routes\appointments.py, routes\attendance.py, routes\cases.py, routes\clients.py, routes\contracts.py, routes\documents.py, routes\employees.py, routes\invoices.py, routes\lawyers.py, routes\leaves.py, routes\penalties.py, routes\reports.py, routes\settings.py, routes\warnings.py, app.py
  3. دالة 'format_file_size' في: routes\admin.py, routes\admin.py, app.py
  4. دالة 'add' في: routes\appointments.py, routes\attendance.py, routes\cases.py, routes\clients.py, routes\contracts.py, routes\employees.py, routes\invoices.py, routes\lawyers.py, routes\leaves.py, routes\penalties.py, routes\warnings.py
  5. دالة 'view' في: routes\appointments.py, routes\cases.py, routes\clients.py, routes\contracts.py, routes\documents.py, routes\employees.py, routes\invoices.py, routes\lawyers.py
  6. دالة 'edit' في: routes\appointments.py, routes\attendance.py, routes\cases.py, routes\clients.py, routes\contracts.py, routes\documents.py, routes\employees.py, routes\invoices.py, routes\lawyers.py, routes\penalties.py, routes\warnings.py
  7. دالة 'delete' في: routes\appointments.py, routes\attendance.py, routes\cases.py, routes\clients.py, routes\contracts.py, routes\documents.py, routes\employees.py, routes\invoices.py, routes\lawyers.py, routes\leaves.py, routes\penalties.py, routes\warnings.py
  8. دالة 'api_client_cases' في: routes\appointments.py, routes\invoices.py
  9. دالة 'get_db' في: routes\attendance.py, routes\clients.py, routes\contracts.py, routes\employees.py, routes\leaves.py, routes\penalties.py, routes\settings.py, routes\warnings.py
  10. دالة 'report' في: routes\attendance.py, routes\leaves.py, routes\penalties.py, routes\warnings.py
  11. دالة 'allowed_file' في: routes\clients.py, routes\documents.py, routes\employees.py, routes\leaves.py, routes\penalties.py, routes\settings.py, routes\warnings.py
  12. دالة 'ensure_upload_folder' في: routes\clients.py, routes\employees.py, routes\leaves.py, routes\penalties.py, routes\settings.py, routes\warnings.py
  13. دالة 'activate' في: routes\clients.py, routes\employees.py
  14. دالة 'api_search' في: routes\clients.py, routes\lawyers.py
  15. دالة 'api_stats' في: routes\clients.py, routes\contracts.py
  16. دالة 'export' في: routes\clients.py, routes\employees.py
  17. دالة 'download' في: routes\contracts.py, routes\documents.py
  18. دالة 'reports' في: routes\contracts.py, routes\settings.py
  19. دالة 'get_system_settings' في: app.py, app.py
  20. دالة 'get_local_ip' في: app.py, app.py

⚠️ تم العثور على 26 حالة تشابه
💡 يُنصح بمراجعة الحالات المكررة