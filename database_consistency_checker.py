#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص التطابق والاتساق بين النماذج وقاعدة البيانات
"""

import os
import sqlite3
import re
from collections import defaultdict

def get_model_tables():
    """استخراج تعريفات الجداول من النماذج"""
    
    print("🔍 تحليل النماذج...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تقسيم المحتوى إلى كلاسات
        class_pattern = r'class\s+(\w+)\(.*?db\.Model.*?\):(.*?)(?=class\s+\w+|$)'
        classes = re.findall(class_pattern, content, re.DOTALL)
        
        model_tables = {}
        
        for class_name, class_content in classes:
            # البحث عن اسم الجدول
            table_match = re.search(r"__tablename__\s*=\s*['\"]([^'\"]+)['\"]", class_content)
            if table_match:
                table_name = table_match.group(1)
                
                # البحث عن الحقول
                field_pattern = r'(\w+)\s*=\s*db\.Column\((.*?)\)'
                fields = re.findall(field_pattern, class_content)
                
                model_tables[table_name] = {
                    'class_name': class_name,
                    'fields': fields
                }
        
        return model_tables
        
    except Exception as e:
        print(f"❌ خطأ في تحليل النماذج: {e}")
        return {}

def get_database_schema():
    """استخراج مخطط قاعدة البيانات"""
    
    print("🔍 تحليل قاعدة البيانات...")
    
    db_files = ['instance/legal_system.db', 'legal_system.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # الحصول على قائمة الجداول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                tables = [row[0] for row in cursor.fetchall()]
                
                db_schema = {}
                
                for table in tables:
                    # الحصول على معلومات الأعمدة
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = cursor.fetchall()
                    
                    db_schema[table] = {
                        'columns': columns,
                        'column_names': [col[1] for col in columns]
                    }
                
                conn.close()
                print(f"✅ تم تحليل {len(tables)} جدول من {db_file}")
                return db_schema
                
            except Exception as e:
                print(f"❌ خطأ في تحليل {db_file}: {e}")
    
    return {}

def compare_models_and_database():
    """مقارنة النماذج مع قاعدة البيانات"""
    
    print("\n📊 مقارنة النماذج مع قاعدة البيانات...")
    
    model_tables = get_model_tables()
    db_schema = get_database_schema()
    
    issues = []
    matches = []
    
    # فحص الجداول الموجودة في النماذج
    for table_name, model_info in model_tables.items():
        if table_name in db_schema:
            matches.append(f"✅ جدول {table_name} موجود في كلا المكانين")
            
            # مقارنة الحقول
            model_fields = [field[0] for field in model_info['fields']]
            db_fields = db_schema[table_name]['column_names']
            
            # الحقول المفقودة في قاعدة البيانات
            missing_in_db = set(model_fields) - set(db_fields)
            if missing_in_db:
                issues.append(f"⚠️ جدول {table_name}: حقول مفقودة في قاعدة البيانات: {', '.join(missing_in_db)}")
            
            # الحقول الإضافية في قاعدة البيانات
            extra_in_db = set(db_fields) - set(model_fields)
            if extra_in_db:
                issues.append(f"ℹ️ جدول {table_name}: حقول إضافية في قاعدة البيانات: {', '.join(extra_in_db)}")
        else:
            issues.append(f"❌ جدول {table_name} موجود في النماذج لكن مفقود في قاعدة البيانات")
    
    # فحص الجداول الموجودة في قاعدة البيانات فقط
    for table_name in db_schema:
        if table_name not in model_tables:
            # تجاهل الجداول المؤقتة والنسخ الاحتياطية
            if not ('backup' in table_name or 'temp' in table_name or table_name.endswith('_old')):
                issues.append(f"⚠️ جدول {table_name} موجود في قاعدة البيانات لكن مفقود في النماذج")
    
    return {
        'issues': issues,
        'matches': matches,
        'model_tables': model_tables,
        'db_schema': db_schema
    }

def check_foreign_key_consistency():
    """فحص اتساق المفاتيح الخارجية"""
    
    print("\n🔗 فحص اتساق المفاتيح الخارجية...")
    
    try:
        with open('models.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن المفاتيح الخارجية
        fk_pattern = r'db\.ForeignKey\([\'"]([^.\'"]+)\.([^.\'"]+)[\'"]\)'
        foreign_keys = re.findall(fk_pattern, content)
        
        issues = []
        
        # فحص كل مفتاح خارجي
        for table, column in foreign_keys:
            # التحقق من وجود الجدول المرجعي
            if not check_table_exists(table):
                issues.append(f"❌ مفتاح خارجي يشير إلى جدول غير موجود: {table}.{column}")
            elif not check_column_exists(table, column):
                issues.append(f"❌ مفتاح خارجي يشير إلى عمود غير موجود: {table}.{column}")
        
        return issues
        
    except Exception as e:
        print(f"❌ خطأ في فحص المفاتيح الخارجية: {e}")
        return []

def check_table_exists(table_name):
    """التحقق من وجود جدول"""
    
    db_files = ['instance/legal_system.db', 'legal_system.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                result = cursor.fetchone()
                conn.close()
                if result:
                    return True
            except:
                pass
    
    return False

def check_column_exists(table_name, column_name):
    """التحقق من وجود عمود في جدول"""
    
    db_files = ['instance/legal_system.db', 'legal_system.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [row[1] for row in cursor.fetchall()]
                conn.close()
                if column_name in columns:
                    return True
            except:
                pass
    
    return False

def generate_consistency_report():
    """إنشاء تقرير الاتساق"""
    
    print("📋 إنشاء تقرير الاتساق...")
    
    comparison = compare_models_and_database()
    fk_issues = check_foreign_key_consistency()
    
    report = []
    report.append("=" * 60)
    report.append("📊 تقرير اتساق قاعدة البيانات")
    report.append("=" * 60)
    
    # ملخص
    report.append(f"\n📈 ملخص:")
    report.append(f"  - عدد الجداول في النماذج: {len(comparison['model_tables'])}")
    report.append(f"  - عدد الجداول في قاعدة البيانات: {len(comparison['db_schema'])}")
    report.append(f"  - عدد التطابقات: {len(comparison['matches'])}")
    report.append(f"  - عدد المشاكل: {len(comparison['issues']) + len(fk_issues)}")
    
    # التطابقات
    if comparison['matches']:
        report.append(f"\n✅ التطابقات ({len(comparison['matches'])}):")
        for match in comparison['matches']:
            report.append(f"  {match}")
    
    # المشاكل
    if comparison['issues']:
        report.append(f"\n⚠️ مشاكل الجداول والحقول ({len(comparison['issues'])}):")
        for issue in comparison['issues']:
            report.append(f"  {issue}")
    
    if fk_issues:
        report.append(f"\n❌ مشاكل المفاتيح الخارجية ({len(fk_issues)}):")
        for issue in fk_issues:
            report.append(f"  {issue}")
    
    if not comparison['issues'] and not fk_issues:
        report.append(f"\n🎉 ممتاز! قاعدة البيانات متسقة مع النماذج")
    
    return "\n".join(report)

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء فحص اتساق قاعدة البيانات...")
    
    # إنشاء التقرير
    report = generate_consistency_report()
    
    # طباعة التقرير
    print(report)
    
    # حفظ التقرير
    with open('database_consistency_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 تم حفظ التقرير في: database_consistency_report.txt")

if __name__ == "__main__":
    main()
