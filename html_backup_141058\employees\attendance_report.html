{% extends "base.html" %}

{% block title %}تقرير الحضور والغياب - {{ company_name or "نظام الشؤون القانونية" }}{% endblock %}

{% block content %}
<!-- رأس التقرير للطباعة -->
{% include 'components/print_header.html' with context %}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom no-print">
    <h1 class="h2">
        <i class="fas fa-calendar-check me-2"></i>
        تقرير الحضور والغياب
        {% if employee %}
        - {{ employee.full_name }}
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0 no-print">
        <div class="btn-group me-2">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-1"></i>
                طباعة التقرير
            </button>
            <button onclick="exportToPDF()" class="btn btn-success">
                <i class="fas fa-file-pdf me-1"></i>
                تصدير PDF
            </button>
            <a href="{{ url_for('employees.index' }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للموظفين
            </a>
        </div>
    </div>
</div>

<!-- معلومات التقرير -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات التقرير
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>نوع التقرير:</strong><br>
                        <span class="badge bg-info">تقرير الحضور والغياب</span>
                    </div>
                    <div class="col-md-3">
                        <strong>فترة التقرير:</strong><br>
                        {{ report_period or "الشهر الحالي" }}
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ الإنشاء:</strong><br>
                        {{ report_date or datetime.now().strftime("%Y-%m-%d") if report_date or datetime.now() else "-" }}
                    </div>
                    <div class="col-md-3">
                        <strong>عدد الموظفين:</strong><br>
                        <span class="badge bg-success">{{ employees|length if employees else (1 if employee else 0 }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if employee %}
<!-- تقرير حضور موظف واحد -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تقرير حضور الموظف: {{ employee.full_name }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- معلومات الموظف -->
                    <div class="col-md-6">
                        <h6 class="text-primary">المعلومات الأساسية</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>رقم الموظف:</strong></td>
                                <td>{{ employee.employee_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>الاسم الكامل:</strong></td>
                                <td>{{ employee.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>القسم:</strong></td>
                                <td>{{ employee.department.name if employee.department else 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>المنصب:</strong></td>
                                <td>{{ employee.position or 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التوظيف:</strong></td>
                                <td>{{ employee.hire_date.hire_date else 'غير محدد'|strftime("%Y-%m-%d") if employee }}</td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- إحصائيات الحضور -->
                    <div class="col-md-6">
                        <h6 class="text-success">إحصائيات الحضور</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>أيام الحضور:</strong></td>
                                <td class="text-success">{{ attendance_stats.present_days if attendance_stats else 0 }} يوم</td>
                            </tr>
                            <tr>
                                <td><strong>أيام الغياب:</strong></td>
                                <td class="text-danger">{{ attendance_stats.absent_days if attendance_stats else 0 }} يوم</td>
                            </tr>
                            <tr>
                                <td><strong>أيام التأخير:</strong></td>
                                <td class="text-warning">{{ attendance_stats.late_days if attendance_stats else 0 }} يوم</td>
                            </tr>
                            <tr>
                                <td><strong>نسبة الحضور:</strong></td>
                                <td>
                                    {% set attendance_rate = attendance_stats.attendance_rate if attendance_stats else 0 %}
                                    <span class="badge {{ 'bg-success' if attendance_rate >= 90 else ('bg-warning' if attendance_rate >= 75 else 'bg-danger' }}">
                                        {{ "%.1f".format(attendance_rate) }}%
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- سجل الحضور التفصيلي -->
                {% if attendance_records %}
                <hr>
                <h6 class="text-info">سجل الحضور التفصيلي</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>التاريخ</th>
                                <th>وقت الحضور</th>
                                <th>وقت الانصراف</th>
                                <th>ساعات العمل</th>
                                <th>الحالة</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in attendance_records %}
                            <tr>
                                <td>{{ record.date.date else '-'|strftime("%Y-%m-%d") if record }}</td>
                                <td>{{ record.check_in.check_in else '-'|strftime("%H:%M") if record }}</td>
                                <td>{{ record.check_out.check_out else '-'|strftime("%H:%M") if record }}</td>
                                <td>{{ record.hours_worked or '-' }}</td>
                                <td>
                                    {% if record.status == 'present' %}
                                        <span class="badge bg-success">حاضر</span>
                                    {% elif record.status == 'absent' %}
                                        <span class="badge bg-danger">غائب</span>
                                    {% elif record.status == 'late' %}
                                        <span class="badge bg-warning">متأخر</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>{{ record.notes or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- تقرير حضور جميع الموظفين -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    تقرير حضور جميع الموظفين
                </h5>
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>رقم الموظف</th>
                                <th>الاسم</th>
                                <th>القسم</th>
                                <th>أيام الحضور</th>
                                <th>أيام الغياب</th>
                                <th>أيام التأخير</th>
                                <th>نسبة الحضور</th>
                                <th>التقييم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for emp in employees %}
                            {% set stats = emp.attendance_stats if emp.attendance_stats else {} %}
                            {% set present = stats.present_days or 0 %}
                            {% set absent = stats.absent_days or 0 %}
                            {% set late = stats.late_days or 0 %}
                            {% set rate = stats.attendance_rate or 0 %}
                            
                            <tr>
                                <td>{{ emp.employee_number }}</td>
                                <td>{{ emp.full_name }}</td>
                                <td>{{ emp.department.name if emp.department else 'غير محدد' }}</td>
                                <td class="text-success">{{ present }}</td>
                                <td class="text-danger">{{ absent }}</td>
                                <td class="text-warning">{{ late }}</td>
                                <td>{{ "%.1f".format(rate }}%</td>
                                <td>
                                    {% if rate >= 95 %}
                                        <span class="badge bg-success">ممتاز</span>
                                    {% elif rate >= 90 %}
                                        <span class="badge bg-info">جيد جداً</span>
                                    {% elif rate >= 80 %}
                                        <span class="badge bg-warning">جيد</span>
                                    {% elif rate >= 70 %}
                                        <span class="badge bg-orange">مقبول</span>
                                    {% else %}
                                        <span class="badge bg-danger">ضعيف</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5>لا توجد بيانات حضور</h5>
                    <p class="text-muted">لم يتم العثور على بيانات حضور للعرض في هذا التقرير</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- إحصائيات عامة -->
{% if employees %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ employees|length }}</h4>
                <p class="mb-0">إجمالي الموظفين</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>{{ overall_stats.total_present if overall_stats else 0 }}</h4>
                <p class="mb-0">إجمالي أيام الحضور</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-times-circle fa-2x mb-2"></i>
                <h4>{{ overall_stats.total_absent if overall_stats else 0 }}</h4>
                <p class="mb-0">إجمالي أيام الغياب</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h4>{{ "%.1f".format(overall_stats.average_attendance_rate if overall_stats else 0) }}%</h4>
                <p class="mb-0">متوسط نسبة الحضور</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- التوقيعات للطباعة -->
{% include 'components/print_signatures.html' with context %}

<script>
function exportToPDF() {
    // تحويل الصفحة إلى PDF
    window.print();
}

// تحسينات الطباعة
window.addEventListener('beforeprint', function() {
    document.title = 'تقرير الحضور والغياب - {{ company_name or "نظام الشؤون القانونية" }}';
});

window.addEventListener('afterprint', function() {
    document.title = '{{ self.title( }}';
});
</script>

<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        border: 1px solid #333 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
        border-bottom: 1px solid #333 !important;
    }
    
    .table {
        font-size: 10pt;
    }
    
    .table th,
    .table td {
        border: 1px solid #333 !important;
        padding: 4px !important;
    }
    
    .table-dark th {
        background-color: #333 !important;
        color: white !important;
    }
    
    .text-success {
        color: #198754 !important;
    }
    
    .text-danger {
        color: #dc3545 !important;
    }
    
    .text-warning {
        color: #ffc107 !important;
    }
    
    .text-info {
        color: #0dcaf0 !important;
    }
    
    .text-primary {
        color: #0d6efd !important;
    }
    
    .badge {
        background-color: #6c757d !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 3px !important;
    }
    
    .bg-success {
        background-color: #198754 !important;
        color: white !important;
    }
    
    .bg-danger {
        background-color: #dc3545 !important;
        color: white !important;
    }
    
    .bg-warning {
        background-color: #ffc107 !important;
        color: #000 !important;
    }
    
    .bg-info {
        background-color: #0dcaf0 !important;
        color: white !important;
    }
    
    .bg-primary {
        background-color: #0d6efd !important;
        color: white !important;
    }
}
</style>
{% endblock %}
